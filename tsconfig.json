{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "preserve",
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "baseUrl": ".",
    "paths": {
      "@smartdesk/common": ["packages/common/src"],
      "@smartdesk/preview": ["packages/preview/src"],
      "@smartdesk/design": ["packages/design/src"],
      "@smartdesk/admin": ["packages/admin/src"],
      "@smartdesk/main": ["apps/main/src"],
    }
  },
  "include": ["apps/*/src/**/*", "packages/*/src/**/*"],
  "files": []
}
