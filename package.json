{"name": "smartdesk-portal", "version": "1.0.0", "description": "可视化前端页面", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"type-check": "lerna run type-check", "dev:common": "pnpm --filter common dev", "build:common": "pnpm --filter common build", "dev:lib:common": "pnpm --filter common dev:lib", "build:lib:common": "pnpm --filter common build:lib", "dev:preview": "pnpm --filter preview dev", "build:preview": "pnpm --filter preview build", "dev:lib:preview": "pnpm --filter preview dev:lib", "build:lib:preview": "pnpm --filter preview build:lib", "dev:design": "pnpm --filter design dev", "build:design": "pnpm --filter design build", "dev:lib:design": "pnpm --filter design dev:lib", "build:lib:design": "pnpm --filter design build:lib", "dev:admin": "pnpm --filter admin dev", "build:admin": "pnpm --filter admin build", "dev:lib:admin": "pnpm --filter admin dev:lib", "build:lib:admin": "pnpm --filter admin build:lib", "dev": "pnpm --filter main dev", "build": "pnpm --filter main build", "format": "prettier --write \"**/*.{vue,ts,tsx,md}\" --config .prettierrc", "clean": "rm -rf node_modules **/*/node_modules dist **/*/dist .vite package-lock.json pnpm-lock.yaml"}, "devDependencies": {"lerna": "^8.1.8", "typescript": "^5.5.3", "prettier": "^3.6.2", "lint-staged": "^16.1.2", "husky": "^8.0.3"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "lint-staged": {"**/*.{vue,ts,tsx,md}": "prettier --write --config .prettierrc"}}