{"arrowParens": "always", "bracketSpacing": true, "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "singleAttributePerLine": false, "bracketSameLine": true, "jsxBracketSameLine": false, "jsxSingleQuote": false, "printWidth": 120, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": true, "singleQuote": true, "tabWidth": 4, "trailingComma": "es5", "useTabs": false, "embeddedLanguageFormatting": "auto", "vueIndentScriptAndStyle": true, "experimentalTernaries": false, "overrides": [{"files": "**/*.vue", "options": {"parser": "vue"}}, {"files": ["**/*.ts", "**/*.tsx"], "options": {"parser": "typescript"}}, {"files": "**/*.md", "options": {"parser": "markdown"}}]}