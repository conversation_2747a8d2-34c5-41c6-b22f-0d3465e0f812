import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import { useMenuStore, useOperatorStore } from '@chances/portal_common_core';
import { routes as preview_routes } from '@smartdesk/preview/router';
import { routes as design_routes } from '@smartdesk/design/router';
import { routes as admin_routes } from '@smartdesk/admin/router';

// @ts-ignore
import { routes as iam_routes } from '@chances/iam_portal';
// @ts-ignore
import { routes as audit_publish_routes } from '@chances/audit_publish_portal';

// 常量定义
const WHITE_LIST_PATHS = new Set(['/iframe', '/forbidden', '/login']);

// 动态路由集合
const dynamicRoutes: RouteRecordRaw[] = [
    ...preview_routes,
    ...design_routes,
    ...admin_routes,
    ...iam_routes,
    ...audit_publish_routes,
];

// 基座项目路由
const baseRoutes: RouteRecordRaw[] = [
    {
        path: '/iframe',
        name: 'BaseIframe',
        component: () => import('../components/base-iframe.vue'),
        props: (route) => ({ url: route.query.url as string }),
    },
    {
        path: '/forbidden',
        name: 'Forbidden',
        component: () => import('../views/Forbidden.vue'),
    },
    {
        path: '/login',
        name: 'Login',
        meta: {
            componentName: 'Login',
            layout: 'designer',
        },
        component: () => import('../views/login/index.vue'),
    },
];

// 处理路由名称，避免重复
const processRoutes = (routes: RouteRecordRaw[]): RouteRecordRaw[] => {
    return routes.map((route) => {
        const processedRoute: RouteRecordRaw = {
            ...route,
            path: route.path.replace(/^\//, ''),
            name: route.name,
        };

        if (route.children?.length) {
            processedRoute.children = processRoutes(route.children);
        }

        return processedRoute;
    });
};

// 创建应用路由
const createAppRoutes = (): RouteRecordRaw[] => {
    try {
        // 主布局路由
        const mainLayoutRoute: RouteRecordRaw = {
            path: '/',
            component: () => import('../layouts/sider-header-content-layout.vue'),
            children: processRoutes(dynamicRoutes),
        };

        return [mainLayoutRoute, ...baseRoutes];
    } catch (error) {
        console.error('路由初始化失败:', error);
        return baseRoutes;
    }
};

// 创建路由实例
export const router = createRouter({
    history: createWebHistory(),
    routes: createAppRoutes(),
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
    const operatorStore = useOperatorStore();
    const menuStore = useMenuStore();
    const operator = operatorStore.getOperator();

    // 登录状态检查
    if (!operator?.userId && to.path !== '/login') {
        return next({ path: '/login' });
    }

    // 处理根路径重定向
    if (to.path === '/' && operator?.userId) {
        const menus = menuStore.getMenus() || [];
        const firstChildPath = menus[0]?.children?.[0]?.path;

        if (firstChildPath) {
            return next({ path: firstChildPath });
        }
    }

    // 权限检查
    const hasMenuPermission = menuStore.menuMap && to.path in menuStore.menuMap;
    const isWhiteListed = WHITE_LIST_PATHS.has(to.path);

    if (hasMenuPermission || isWhiteListed) {
        return next();
    }

    return next({ path: '/forbidden' });
});
