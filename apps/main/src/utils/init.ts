import { enumApi } from '@smartdesk/common/api';
import { useEnumStore } from '@chances/portal_common_core';

/**
 * 加载可视化枚举
 */
export const loadVisualEnums = async () => {
    const res = await enumApi.treeEnum({});
    if (res.code === 200) {
        return res.result;
    }

    return [];
};

/**
 * 加载 CMS 枚举
 */
export const loadCmsEnums = async () => {
    const res = await enumApi.searchCmsEnum({
        codes: ['vodDubEnum', 'countryEnum', 'yearEnum', 'linkTypeEnum', 'pointContentTypeEnum'],
    });

    if (res.code === 200) {
        return res.result;
    }

    return [];
};

/**
 * 初始化函数
 */
export const init = async () => {
    // 并行加载两种枚举
    const [visualEnums, cmsEnums] = await Promise.all([
        // 可视化
        loadVisualEnums(),
        // CMS
        loadCmsEnums(),
    ]);

    // 合并结果
    const enums = [...visualEnums, ...cmsEnums];

    useEnumStore().setEnums(enums);
};
