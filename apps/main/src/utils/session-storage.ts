class SessionStorageUtil {
    /**
     * 将数据存储到 sessionStorage
     * @param key 存储数据的键
     * @param value 要存储的数据
     */
    static setItem<T>(key: string, value: T): void {
        try {
            const jsonData = JSON.stringify(value);
            sessionStorage.setItem(key, jsonData);
        } catch (error) {
            console.error('将数据存储到 sessionStorage 时出错:', error);
        }
    }

    /**
     * 从 sessionStorage 获取数据
     * @param key 要获取数据的键
     * @returns 获取的数据或 null
     */
    static getItem<T>(key: string): T | null {
        try {
            const jsonData = sessionStorage.getItem(key);
            return jsonData ? (JSON.parse(jsonData) as T) : null;
        } catch (error) {
            console.error('从 sessionStorage 获取数据时出错:', error);
            return null;
        }
    }

    /**
     * 从 sessionStorage 移除数据
     * @param key 要移除数据的键
     */
    static removeItem(key: string): void {
        try {
            sessionStorage.removeItem(key);
        } catch (error) {
            console.error('从 sessionStorage 移除数据时出错:', error);
        }
    }

    /**
     * 清空 sessionStorage
     */
    static clear(): void {
        try {
            sessionStorage.clear();
        } catch (error) {
            console.error('清空 sessionStorage 时出错:', error);
        }
    }
}

export default SessionStorageUtil;
