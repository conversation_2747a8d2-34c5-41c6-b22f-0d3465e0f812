class LocalStorageUtil {
    /**
     * 将数据存储到 localStorage
     * @param key 存储数据的键
     * @param value 要存储的数据
     */
    static setItem<T>(key: string, value: T): void {
        try {
            const jsonData = JSON.stringify(value);
            localStorage.setItem(key, jsonData);
        } catch (error) {
            console.error('将数据存储到 localStorage 时出错:', error);
        }
    }

    /**
     * 从 localStorage 获取数据
     * @param key 要获取数据的键
     * @returns 获取的数据或 null
     */
    static getItem<T>(key: string): T | null {
        try {
            const jsonData = localStorage.getItem(key);
            return jsonData ? (JSON.parse(jsonData) as T) : null;
        } catch (error) {
            console.error('从 localStorage 获取数据时出错:', error);
            return null;
        }
    }

    /**
     * 从 localStorage 移除数据
     * @param key 要移除数据的键
     */
    static removeItem(key: string): void {
        try {
            localStorage.removeItem(key);
        } catch (error) {
            console.error('从 localStorage 移除数据时出错:', error);
        }
    }

    /**
     * 清空 localStorage
     */
    static clear(): void {
        try {
            localStorage.clear();
        } catch (error) {
            console.error('清空 localStorage 时出错:', error);
        }
    }
}

export default LocalStorageUtil;
