<template>
    <a-upload
        :disabled="readonly"
        :file-list="innerValue"
        list-type="picture-card"
        image-preview
        :custom-request="uploadFile"
        :limit="1"
        @change="handleFileChange"
        accept=".jpg,.jpeg,.png" />
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import { UploadRequest } from '@arco-design/web-vue';
    import { storageApi } from '@smartdesk/common/api';

    export default defineComponent({
        name: 'ImageUpload',
        props: {
            modelValue: {
                type: String,
                default: '',
            },
            readonly: {
                type: Boolean,
                default: true,
            },
        },
        emits: ['update:modelValue'],
        data() {
            return {
                allowedTypes: ['image/jpeg', 'image/png', 'image/jpg'],
            };
        },
        computed: {
            innerValue: {
                get() {
                    // 读取图片时走代理，改为相对路径 /iam_images/
                    return this.modelValue
                        ? ([
                              {
                                  url: `/cms-picture/iam_images/${this.modelValue}`,
                              },
                          ] as any)
                        : [];
                },
                set(val: any) {
                    const fullUrl = val.length ? val[0].url : '';
                    const newUrl = fullUrl?.replace('/cms-picture/iam_images/', ''); // 去掉代理前缀，只保留相对路径
                    this.$emit('update:modelValue', newUrl);
                },
            },
        },
        methods: {
            // 处理文件变化
            handleFileChange(fileList: any) {
                this.innerValue = fileList;
            },
            // 上传文件
            // @ts-ignore
            uploadFile({ fileItem, onSuccess, onError }): UploadRequest {
                const file = fileItem?.file;

                // 校验文件类型
                if (!this.allowedTypes.includes(file.type)) {
                    (this as any).$message.error('文件格式不支持，请上传 JPG、JPEG 或 PNG 格式的图片');
                    this.innerValue = [];
                    onError();
                    return {};
                }

                storageApi
                    .uploadFile(file)
                    .then((res: any) => {
                        if (res.data?.code === 200) {
                            onSuccess({ url: res.data?.result });
                            this.innerValue = [{ url: res.data?.result }];
                            (this as any).$message.success('上传成功');
                        } else {
                            onError();
                            (this as any).$message.error('上传失败');
                        }
                    })
                    .catch(() => {
                        onError();
                        (this as any).$message.error('上传出错');
                    });
                return {};
            },
        },
    });
</script>
