<template>
    <div class="base-menu">
        <a-menu :selected-keys="selectedKeys" auto-scroll-into-view auto-open-selected breakpoint="xl">
            <template v-for="(menu, menuIndex) in menus" :key="menu.path || menuIndex">
                <a-sub-menu v-if="!menu.hidden" :key="menu.path || menuIndex">
                    <template #title>
                        <span> <component :is="menu.iconName" />{{ menu.name }} </span>
                    </template>
                    <template v-for="item in menu.children" :key="item.path">
                        <a-menu-item v-if="!item.hidden" :key="item.path" @click="navigateTo(item.path)">
                            <template #icon>
                                <component :is="item.iconName"></component>
                            </template>
                            {{ item.name }}
                        </a-menu-item>
                    </template>
                </a-sub-menu>
            </template>
        </a-menu>
    </div>
</template>

<script lang="ts" setup>
    import { computed, ref, watch } from 'vue';
    import { useRoute, useRouter } from 'vue-router';
    import { useMenuStore } from '@chances/portal_common_core';
    import { useTabViewStore } from '@smartdesk/main/stores';

    // 定义菜单项接口（根据你的实际数据调整）
    interface MenuItem {
        path: string;
        name: string;
        iconName?: string;
        hidden?: boolean;
        children?: MenuItem[];
    }

    // 响应式数据
    const route = useRoute();
    const router = useRouter();
    const menuStore = useMenuStore();
    const tabViewStore = useTabViewStore();

    const selectedKeys = ref<string[]>([]);
    const menus = computed(() => menuStore.getMenus() || []); // 响应式 getter

    // 初始化和监听路由
    selectedKeys.value = [route.path];
    watch(
        () => route.path,
        (newPath) => {
            selectedKeys.value = [newPath];
        }
    );

    // 导航方法
    const navigateTo = (path: string) => {
        const resolved = router.resolve(path);
        tabViewStore.delCachedView(resolved.meta?.componentName as string);
        router.push({ path });
    };
</script>

<style scoped>
    .base-menu :deep(.arco-menu) {
        width: 100%;
    }
</style>
