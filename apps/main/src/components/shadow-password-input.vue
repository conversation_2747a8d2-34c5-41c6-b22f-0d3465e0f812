<template>
    <div ref="containerRef" class="shadow-password-container" :class="customClass">
        <a-input-password
            v-if="!initialized"
            v-model="tempModelValue"
            :placeholder="placeholder"
            allow-clear
            class="temp-input">
            <template #prefix>
                <icon-lock />
            </template>
        </a-input-password>
    </div>
</template>

<script lang="ts">
    import { IconLock } from '@arco-design/web-vue/es/icon';
    import { defineComponent, PropType } from 'vue';

    export default defineComponent({
        name: 'ShadowPasswordInput',
        components: {
            IconLock,
        },
        props: {
            modelValue: {
                type: String as PropType<string>,
                default: '',
            },
            placeholder: {
                type: String as PropType<string>,
                default: '请输入密码',
            },
            customClass: {
                type: String as PropType<string>,
                default: '',
            },
            disabled: {
                type: Boolean as PropType<boolean>,
                default: false,
            },
        },
        emits: ['update:modelValue', 'blur', 'focus', 'enter', 'keyup:enter'],
        data() {
            return {
                initialized: false,
                tempModelValue: '',
                internalPassword: '',
                scrollPos: 0,
                // DOM 引用
                shadowRoot: null as ShadowRoot | null,
                realInput: null as HTMLInputElement | null,
                passwordDisplay: null as HTMLElement | null,
                clearButton: null as HTMLElement | null,
                eyeIcon: null as HTMLElement | null,
                placeholder$: null as HTMLElement | null,
            };
        },
        watch: {
            modelValue: {
                handler(newVal: string) {
                    this.internalPassword = newVal || '';
                    this.updatePasswordDisplay();

                    // 如果已初始化 DOM
                    if (this.initialized) {
                        if (!newVal && this.placeholder$ && !this.passwordDisplay?.contains(this.placeholder$)) {
                            this.passwordDisplay?.appendChild(this.placeholder$);
                        } else if (newVal && this.placeholder$ && this.passwordDisplay?.contains(this.placeholder$)) {
                            this.placeholder$?.remove();
                        }

                        if (this.clearButton) {
                            if (newVal) {
                                this.clearButton.style.display = '';
                            } else {
                                this.clearButton.style.display = 'none';
                            }
                        }

                        // 同步真实输入框的值
                        if (this.realInput && this.realInput.value !== newVal) {
                            this.realInput.value = newVal || '';
                        }
                    }
                },
                immediate: true,
            },
        },
        mounted() {
            // 为了确保 DOM 完全就绪，我们使用定时器延迟初始化
            setTimeout(() => {
                this.createPasswordInput();

                // 如果有初始密码，更新显示
                if (this.modelValue) {
                    this.internalPassword = this.modelValue;
                    this.updatePasswordDisplay();
                }

                this.initialized = true;

                // 添加一个微任务，处理可能的自动填充
                setTimeout(() => {
                    this.handleAutofill();
                    this.syncScrollPosition();
                }, 300);
            }, 0);
        },
        beforeUnmount() {
            if (this.realInput) {
                this.realInput.removeEventListener('input', null as any);
                this.realInput.removeEventListener('focus', null as any);
                this.realInput.removeEventListener('blur', null as any);
                this.realInput.removeEventListener('keyup', null as any);
                this.realInput.removeEventListener('keydown', null as any);
                this.realInput.removeEventListener('click', null as any);
                this.realInput.removeEventListener('change', null as any);
                this.realInput.removeEventListener('animationstart', null as any);
            }
        },
        methods: {
            // 更新密码显示区域
            updatePasswordDisplay() {
                if (!this.passwordDisplay || !this.initialized) return;

                // 清空当前显示
                while (this.passwordDisplay.firstChild) {
                    if (this.passwordDisplay.firstChild !== this.placeholder$) {
                        this.passwordDisplay.removeChild(this.passwordDisplay.firstChild);
                    } else {
                        break;
                    }
                }

                // 如果密码为空且有占位符，显示占位符
                if (!this.internalPassword) {
                    if (this.placeholder$ && !this.passwordDisplay.contains(this.placeholder$)) {
                        this.passwordDisplay.appendChild(this.placeholder$);
                    }
                    return;
                }

                // 移除占位符（如果存在）
                if (this.placeholder$ && this.passwordDisplay.contains(this.placeholder$)) {
                    this.placeholder$.remove();
                }

                // 根据密码是否可见设置显示模式
                const isVisible = this.eyeIcon?.dataset?.visible === 'true';

                // 创建内部包装容器（用于滚动）
                const passwordTextWrapper = document.createElement('div');
                passwordTextWrapper.className = 'password-text-wrapper';

                if (isVisible) {
                    // 不直接显示密码文本，而是创建一个带掩码的span
                    const passwordText = document.createElement('span');
                    passwordText.textContent = this.internalPassword;
                    passwordText.className = 'password-text';
                    // 添加data属性 - 不要直接使用textContent暴露密码
                    passwordText.dataset.masked = 'false';
                    passwordTextWrapper.appendChild(passwordText);
                } else {
                    // 使用固定字符代替圆点，这样能确保与光标位置匹配
                    const passwordText = document.createElement('span');
                    passwordText.textContent = '•'.repeat(this.internalPassword.length);
                    passwordText.className = 'password-text';
                    passwordText.dataset.masked = 'true';
                    passwordTextWrapper.appendChild(passwordText);
                }

                // 将包装器添加到显示区域
                this.passwordDisplay.appendChild(passwordTextWrapper);

                // 应用当前滚动位置
                this.passwordDisplay.scrollLeft = this.scrollPos;
            },

            // 切换密码可见性
            togglePasswordVisibility(e?: Event) {
                if (e) e.stopPropagation();
                if (!this.eyeIcon) return;

                const isVisible = this.eyeIcon.dataset.visible === 'true';
                this.eyeIcon.dataset.visible = isVisible ? 'false' : 'true';

                // 切换图标
                if (isVisible) {
                    this.eyeIcon.innerHTML =
                        '<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-eye-invisible" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter"><path d="M14 14.5c-3.91 2.638-7.416 6.327-10 10.254 6.667 9.333 15.167 14.5 20 14.5 1.314 0 2.67-.267 4.02-.688M17.5 11.264C19.585 10.424 21.694 10 24 10c4.833 0 13.333 5.167 20 14.5-.33.462-.665.915-1.005 1.36M41 41 7 7"></path><path d="M24 18a6 6 0 0 0-6 6"></path></svg>';
                } else {
                    this.eyeIcon.innerHTML =
                        '<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-eye" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter"><path d="M24 36c11.046 0 20-12 20-12s-8.954-12-20-12S4 24 4 24s8.954 12 20 12Z"></path><path d="M24 29a5 5 0 1 0 0-10 5 5 0 0 0 0 10Z"></path></svg>';
                }

                // 保存当前滚动位置
                if (this.passwordDisplay) {
                    this.scrollPos = this.passwordDisplay.scrollLeft;
                }

                // 更新密码显示
                this.updatePasswordDisplay();

                // 获取焦点并保存光标位置
                if (this.realInput) {
                    const currentPosition = this.realInput.selectionStart || 0;
                    setTimeout(() => {
                        this.realInput?.focus();
                        try {
                            this.realInput?.setSelectionRange(currentPosition, currentPosition);
                        } catch (e) {
                            // 忽略错误
                        }
                    }, 10);
                }
            },

            // 清空密码
            clearPassword(e?: Event) {
                if (e) e.stopPropagation();

                this.internalPassword = '';
                if (this.realInput) this.realInput.value = '';
                this.$emit('update:modelValue', '');
                this.scrollPos = 0;
                this.updatePasswordDisplay();

                if (this.realInput) {
                    setTimeout(() => {
                        this.realInput?.focus();
                    }, 10);
                }
            },

            // 测量文本宽度
            measureTextWidth(text: string) {
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');
                if (!context) return text.length * 8; // 默认估计值
                context.font = '14px sans-serif'; // 与密码显示字体相同
                return context.measureText(text).width;
            },

            // 处理浏览器自动填充
            handleAutofill() {
                if (!this.realInput || !this.initialized) return;

                // 检测输入框值是否被浏览器填充
                if (this.realInput.value && this.realInput.value !== this.internalPassword) {
                    this.internalPassword = this.realInput.value;
                    this.$emit('update:modelValue', this.realInput.value);
                    this.updatePasswordDisplay();

                    // 显示清除按钮
                    if (this.clearButton) {
                        this.clearButton.style.display = '';
                    }
                }
            },

            // 同步滚动位置
            syncScrollPosition(cursorPos: number = -1) {
                if (!this.realInput || !this.passwordDisplay) return;

                // 如果没有提供光标位置，使用当前光标位置
                if (cursorPos === -1) {
                    cursorPos = this.realInput.selectionStart || this.internalPassword.length;
                }

                // 防止光标位置超出范围
                cursorPos = Math.min(cursorPos, this.internalPassword.length);

                // 计算文本宽度
                const textBeforeCursor = this.internalPassword.substring(0, cursorPos);
                const textWidth = this.measureTextWidth(textBeforeCursor);
                const totalWidth = this.measureTextWidth(this.internalPassword);

                // 获取可见区域宽度
                const visibleWidth = this.passwordDisplay.clientWidth;

                // 计算最佳滚动位置，确保光标在可见区域内
                if (totalWidth <= visibleWidth) {
                    // 如果总宽度小于可见区域，不需要滚动
                    this.scrollPos = 0;
                } else if (cursorPos === this.internalPassword.length) {
                    // 如果光标在最后，滚动到最右边
                    this.scrollPos = totalWidth - visibleWidth + 20; // 额外空间让光标更明显
                } else {
                    // 保持光标在可见区域内
                    const minScroll = Math.max(0, textWidth - visibleWidth + 20);
                    const maxScroll = Math.max(0, textWidth - 40); // 前面留一点空间

                    // 如果当前滚动位置已经在合理范围内，不调整
                    if (this.scrollPos < minScroll) {
                        this.scrollPos = minScroll;
                    } else if (this.scrollPos > maxScroll) {
                        this.scrollPos = maxScroll;
                    }
                }

                // 应用滚动位置
                this.passwordDisplay.scrollLeft = this.scrollPos;
                this.realInput.scrollLeft = this.scrollPos;
            },

            // 创建密码输入组件
            createPasswordInput() {
                const containerRef = this.$refs.containerRef as HTMLElement;
                if (!containerRef) return;

                // 获取父级的类以传递样式值
                const parentClassList = containerRef.classList;
                const isLoginInput = parentClassList.contains('login-input');

                // 创建 Shadow DOM (open 模式便于修复和调试)
                this.shadowRoot = containerRef.attachShadow({ mode: 'open' });

                // 创建样式
                const style = document.createElement('style');
                style.textContent = `
    :host {
      display: inline-block;
      width: 100%;
      height: 48px;
    }

    .input-container {
      position: relative;
      width: 100%;
      height: 48px;
      border: 1px solid rgba(220, 223, 230, 1);
      border-radius: 4px;
      background-color: white;
      display: flex;
      align-items: center;
      box-sizing: border-box;
      transition: all 0.2s;
      cursor: text;
    }

    .input-container:hover {
      border-color: #20b094;
    }

    .input-container:focus-within {
      border-color: #20b094;
      box-shadow: 0 0 0 2px rgba(32, 176, 148, 0.2);
      outline: none;
    }

    .prefix-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 12px;
      color: #4E5969;
      font-size: 16px;
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      z-index: 3;
      pointer-events: none;
    }

    .suffix-icon {
      margin-right: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #4E5969;
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      z-index: 3;
    }

    .suffix-icon:hover {
      color: #86909C;
    }

    .suffix-icon svg, .prefix-icon svg {
      width: 16px;
      height: 16px;
    }

    .real-input {
      position: absolute;
      top: 0;
      left: 0;
      width: calc(100% - 36px);
      height: 100%;
      border: none;
      padding: 0 12px 0 40px;
      box-sizing: border-box;
      background: transparent;
      z-index: 1;
      font-size: 14px;
      font-family: sans-serif;
      color: transparent;
      caret-color: #333;
    }

    .real-input:focus {
      outline: none;
    }

    /* 防止浏览器自动填充时改变背景色 */
    .real-input:-webkit-autofill,
    .real-input:-webkit-autofill:hover,
    .real-input:-webkit-autofill:focus {
      -webkit-text-fill-color: transparent;
      transition: background-color 5000s ease-in-out 0s;
      box-shadow: none;
    }

    .password-display {
      flex: 1;
      height: 100%;
      padding-left: 10px;
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #333;
      overflow-x: auto;
      overflow-y: hidden;
      pointer-events: none;
      z-index: 2;
      /* 隐藏滚动条但保留滚动功能 */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */
    }

    /* 隐藏 WebKit 滚动条 */
    .password-display::-webkit-scrollbar {
      display: none;
    }

    .password-text-wrapper {
      display: inline-block;
      white-space: nowrap;
      min-width: 100%;
    }

    .placeholder {
      color: #C2C7CC;
      pointer-events: none;
    }

    .password-text {
      font-family: sans-serif;
      font-size: 14px;
      color: #333;
      letter-spacing: 0px;
      white-space: nowrap;
    }

    .clear-button {
      margin-right: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #4E5969;
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      visibility: hidden;
      z-index: 3;
    }

    .input-container:hover .clear-button {
      visibility: visible;
    }

    .clear-button svg {
      width: 16px;
      height: 16px;
    }

    .clear-button:hover {
      color: #86909C;
    }

    /* 专门针对登录页面的样式 */
    .login-input.input-container {
      width: 290px;
      background: rgba(255, 255, 255, 1);
      border: 1px solid rgba(220, 223, 230, 1);
      border-radius: 4px;
    }
  `;

                // 创建容器
                const container = document.createElement('div');
                container.className = 'input-container';
                if (isLoginInput) {
                    container.classList.add('login-input');
                }

                // 创建前缀图标
                const prefixIcon = document.createElement('div');
                prefixIcon.className = 'prefix-icon';
                prefixIcon.innerHTML =
                    '<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-lock" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter"><path d="M23 29h2v5h-2z"></path><path d="M40 21v-3a16 16 0 0 0-32 0v3"></path><rect x="8" y="19" width="32" height="24" rx="1"></rect></svg>';

                // 创建密码显示区域
                this.passwordDisplay = document.createElement('div');
                this.passwordDisplay.className = 'password-display';

                // 创建实际输入框 (可见但透明)
                this.realInput = document.createElement('input');
                this.realInput.type = 'password'; // 使用password类型增加安全性
                this.realInput.className = 'real-input';
                this.realInput.setAttribute('autocomplete', 'current-password'); // 允许浏览器自动填充
                this.realInput.setAttribute('spellcheck', 'false');

                // 如果有初始值，设置到真实输入框
                if (this.modelValue) {
                    this.realInput.value = this.modelValue;
                }

                // 创建占位符
                this.placeholder$ = document.createElement('span');
                this.placeholder$.className = 'placeholder';
                this.placeholder$.textContent = this.placeholder;

                // 如果没有值，添加占位符
                if (!this.internalPassword) {
                    this.passwordDisplay.appendChild(this.placeholder$);
                }

                // 创建清除按钮
                this.clearButton = document.createElement('div');
                this.clearButton.className = 'clear-button';
                this.clearButton.innerHTML =
                    '<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-close" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter"><path d="M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142"></path></svg>';
                this.clearButton.style.display = this.internalPassword ? '' : 'none';

                // 创建眼睛图标
                this.eyeIcon = document.createElement('div');
                this.eyeIcon.className = 'suffix-icon';
                this.eyeIcon.dataset.visible = 'false';
                this.eyeIcon.innerHTML =
                    '<svg viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="currentColor" class="arco-icon arco-icon-eye-invisible" stroke-width="4" stroke-linecap="butt" stroke-linejoin="miter"><path d="M14 14.5c-3.91 2.638-7.416 6.327-10 10.254 6.667 9.333 15.167 14.5 20 14.5 1.314 0 2.67-.267 4.02-.688M17.5 11.264C19.585 10.424 21.694 10 24 10c4.833 0 13.333 5.167 20 14.5-.33.462-.665.915-1.005 1.36M41 41 7 7"></path><path d="M24 18a6 6 0 0 0-6 6"></path></svg>';

                // 添加事件监听器

                // 输入事件
                this.realInput.addEventListener('input', (e) => {
                    const target = e.target as HTMLInputElement;
                    // 更新内部密码值
                    this.internalPassword = target.value;
                    this.$emit('update:modelValue', this.internalPassword);

                    // 显示清除按钮
                    if (this.internalPassword) {
                        if (this.clearButton) this.clearButton.style.display = '';
                    } else {
                        if (this.clearButton) this.clearButton.style.display = 'none';
                    }

                    this.updatePasswordDisplay();

                    // 使用增强的滚动同步
                    setTimeout(() => {
                        this.syncScrollPosition(target.selectionStart || target.value.length);
                    }, 0);
                });

                // 键盘导航事件 - 用于处理左右箭头键
                this.realInput.addEventListener('keydown', (e) => {
                    // 检测左右箭头键
                    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                        setTimeout(() => {
                            this.syncScrollPosition();
                        }, 0);
                    }
                });

                // 鼠标点击事件 - 处理光标位置变化
                this.realInput.addEventListener('click', () => {
                    setTimeout(() => {
                        this.syncScrollPosition();
                    }, 0);
                });

                // 焦点事件
                this.realInput.addEventListener('focus', (e) => {
                    container.classList.add('focused');
                    this.$emit('focus', e);

                    // 切换输入类型，以适应可见性
                    if (this.eyeIcon?.dataset.visible === 'true') {
                        this.realInput!.type = 'text';
                    } else {
                        this.realInput!.type = 'password';
                    }

                    // 检查是否有自动填充
                    setTimeout(() => {
                        this.handleAutofill();
                        this.syncScrollPosition();
                    }, 100);
                });

                this.realInput.addEventListener('blur', (e) => {
                    container.classList.remove('focused');
                    this.$emit('blur', e);
                });

                // Enter 键事件
                this.realInput.addEventListener('keyup', (e) => {
                    if (e.key === 'Enter') {
                        this.$emit('enter', e);
                        this.$emit('keyup:enter', e);
                    }
                });

                // 处理浏览器自动填充
                this.realInput.addEventListener('animationstart', (e: AnimationEvent) => {
                    // 这是检测Chrome/Safari自动填充的一种常见方法
                    if (e.animationName === 'onAutoFillStart' || e.animationName.indexOf('autofill') !== -1) {
                        setTimeout(() => {
                            this.handleAutofill();
                            this.syncScrollPosition();
                        }, 100);
                    }
                });

                // 额外的变化监听
                this.realInput.addEventListener('change', () => {
                    setTimeout(() => {
                        this.handleAutofill();
                        this.syncScrollPosition();
                    }, 100);
                });

                // 点击整个容器时聚焦输入框
                container.addEventListener('click', (e) => {
                    if (e.target === container || e.target === this.passwordDisplay) {
                        this.realInput?.focus();
                        // 点击时尝试将光标移动到末尾
                        setTimeout(() => {
                            try {
                                const length = this.internalPassword.length;
                                this.realInput?.setSelectionRange(length, length);
                                this.syncScrollPosition(length);
                            } catch (e) {
                                // 忽略错误
                            }
                        }, 10);
                    }
                });

                // 清除按钮事件 - 使用 mousedown 而不是 click 以防止失去焦点
                this.clearButton.addEventListener('mousedown', (e) => {
                    e.preventDefault(); // 防止失去焦点
                    this.clearPassword(e);
                });

                // 眼睛图标点击事件 - 使用 mousedown 而不是 click 以防止失去焦点
                this.eyeIcon.addEventListener('mousedown', (e) => {
                    e.preventDefault(); // 防止失去焦点
                    this.togglePasswordVisibility(e);

                    // 切换输入类型
                    if (this.eyeIcon?.dataset.visible === 'true') {
                        if (this.realInput) this.realInput.type = 'text';
                    } else {
                        if (this.realInput) this.realInput.type = 'password';
                    }
                });

                // 组装组件
                container.appendChild(this.realInput);
                container.appendChild(prefixIcon);
                container.appendChild(this.passwordDisplay);
                container.appendChild(this.clearButton);
                container.appendChild(this.eyeIcon);

                // 添加到 Shadow DOM
                this.shadowRoot.appendChild(style);
                this.shadowRoot.appendChild(container);

                // 初始显示
                this.updatePasswordDisplay();
            },
        },
    });
</script>

<style scoped>
    .shadow-password-container {
        display: block;
        position: relative;
        width: 100%;
        height: 48px;
    }

    .temp-input {
        width: 100%;
        height: 48px;
    }

    /* 用于检测自动填充的特殊动画 */
    @keyframes onAutoFillStart {
        from {
            /**/
        }
        to {
            /**/
        }
    }

    @keyframes onAutoFillCancel {
        from {
            /**/
        }
        to {
            /**/
        }
    }
</style>
