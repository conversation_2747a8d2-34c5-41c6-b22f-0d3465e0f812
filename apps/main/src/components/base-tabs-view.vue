<template>
    <div class="tabs-view">
        <div class="tabs" ref="tabsContainer">
            <div
                v-for="tab in tabs"
                :key="tab.key"
                :class="['tab-item', { active: tab.key === activeKey }]"
                @click="handleTabClick(tab.key)"
                @contextmenu.prevent="openContextMenu($event, tab)">
                {{ tab.title }}
                <icon-close v-if="tab.closable" class="close-icon" @click.stop="closeTab(tab.key)" />
            </div>
        </div>
        <a-trigger
            v-if="contextMenuVisible"
            :popup-visible.sync="contextMenuVisible"
            :popup-style="popupStyle"
            popup-transition-name=""
            @popup-visible-change="onPopupVisibleChange">
            <template #content>
                <a-menu @menu-item-click="handleMenuClick">
                    <a-menu-item key="closeCurrent">关闭当前标签</a-menu-item>
                    <a-menu-item key="closeOthers">关闭其他标签</a-menu-item>
                    <a-menu-item key="closeAll">关闭全部标签</a-menu-item>
                </a-menu>
            </template>
            <div></div>
        </a-trigger>
    </div>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import { IconClose } from '@arco-design/web-vue/es/icon';
    import { Menu, MenuItem, Trigger } from '@arco-design/web-vue';
    import { useTabViewStore } from '@smartdesk/main/stores';

    export default defineComponent({
        name: 'BaseTabsView',
        components: {
            IconClose,
            'a-menu': Menu,
            'a-menu-item': MenuItem,
            'a-trigger': Trigger,
        },
        data() {
            return {
                tabViewStore: useTabViewStore(),
                contextMenuVisible: false,
                dropdownPosition: { x: 0, y: 0 },
                selectedTab: null as null | { key: string; title: string },
            };
        },
        computed: {
            tabs() {
                return this.tabViewStore.tabs;
            },
            activeKey() {
                return this.tabViewStore.activeKey;
            },
            popupStyle() {
                return {
                    position: 'fixed',
                    left: `${this.dropdownPosition.x}px`,
                    top: `${this.dropdownPosition.y}px`,
                } as any;
            },
        },
        mounted() {
            // 可选：监听鼠标滚轮事件以控制滚动
            (this.$refs.tabsContainer as any).addEventListener('wheel', this.handleScroll, {
                passive: false,
            });
        },
        beforeDestroy() {
            // 清除事件监听器
            (this.$refs.tabsContainer as any).removeEventListener('wheel', this.handleScroll);
        },
        methods: {
            // 滚动容器的内容
            handleScroll(event: WheelEvent) {
                event.preventDefault();
                (this.$refs.tabsContainer as HTMLElement).scrollLeft += event.deltaY;
            },

            // 点击 tab
            handleTabClick(key: string) {
                this.$router.push(key);
            },
            // 关闭 tab
            closeTab(key: string) {
                const wasActive = key === this.activeKey;
                this.tabViewStore.removeTab(key);
                if (wasActive) {
                    const newActiveKey = this.tabViewStore.activeKey;
                    this.$router.push(newActiveKey);
                }
            },
            // 打开右键菜单
            openContextMenu(event: MouseEvent, tab: { key: string; title: string }) {
                this.selectedTab = tab;
                this.contextMenuVisible = true;
                this.dropdownPosition = { x: event.clientX, y: event.clientY };
            },
            // 处理菜单点击
            handleMenuClick(key: string) {
                if (key === 'closeCurrent' && this.selectedTab) {
                    this.closeTab(this.selectedTab.key);
                } else if (key === 'closeOthers' && this.selectedTab) {
                    this.tabViewStore.closeOthers(this.selectedTab.key);
                    if (this.activeKey !== this.selectedTab.key) {
                        this.$router.push(this.selectedTab.key);
                    }
                } else if (key === 'closeAll') {
                    this.tabViewStore.closeAll();
                    this.$router.push('/');
                }
                this.contextMenuVisible = false;
            },
            // 菜单显示状态变化
            onPopupVisibleChange(visible: boolean) {
                if (!visible) {
                    this.contextMenuVisible = false;
                }
            },
        },
    });
</script>

<style scoped>
    .tabs-view {
        display: flex;
        align-items: center;
        margin: 5px 10px 0 5px;
        border-radius: 4px 4px 0 0;
    }

    .tabs {
        display: flex;
        column-gap: 10px;
        white-space: nowrap;
        overflow-x: scroll;
        overflow-y: hidden;
        scrollbar-width: none;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
            display: none;
        }
    }

    .tab-item {
        display: flex;
        align-items: center;
        padding: 8px 16px;
        cursor: pointer;
        position: relative;
        user-select: none;
        background-color: #f4f1f1;
    }

    .tab-item.active {
        color: rgb(var(--primary-6));
        background-color: white;
    }

    .close-icon {
        margin-left: 8px;
        color: #888;
        font-size: 12px;
    }

    .close-icon:hover {
        color: #f56c6c;
    }

    .tab-item:hover .close-icon {
        visibility: visible;
    }

    .tabs {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .tabs::-webkit-scrollbar {
        display: none;
    }
</style>
