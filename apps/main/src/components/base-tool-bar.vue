<template>
    <div>
        <a-tooltip content="首页">
            <icon-home :stroke-width="2" :size="20" class="base-tool-bar-icon" @click="onClickHomePage" />
        </a-tooltip>

        <a-tooltip content="退出">
            <icon-export :stroke-width="2" :size="20" class="base-tool-bar-icon" @click="onClickLogout" />
        </a-tooltip>
    </div>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import { useEnumStore, useMenuStore, useOperatorStore, usePermissionStore } from '@chances/portal_common_core';
    import { useTabViewStore } from '@smartdesk/main/stores';
    import { loginApi } from '@smartdesk/main/api';

    export default defineComponent({
        name: 'BaseToolBar',
        methods: {
            // 点击首页
            onClickHomePage() {
                this.$router.push('/');
            },
            // 点击退出
            onClickLogout() {
                loginApi.logout().then((res: any) => {
                    if (res.code === 200) {
                        (this as any).$message.success('退出登录成功');
                        useOperatorStore().$reset();
                        useMenuStore().$reset();
                        usePermissionStore().$reset();
                        useTabViewStore().$reset();
                        useEnumStore().$reset();
                        sessionStorage.removeItem('token');
                        this.$router.push('/login');
                    } else {
                        (this as any).$message.error('退出登录失败：', res.msg);
                    }
                });
            },
        },
    });
</script>

<style scoped>
    .base-tool-bar-icon {
        margin: 0 5px;
    }
</style>
