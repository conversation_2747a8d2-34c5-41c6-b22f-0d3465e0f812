<template>
    <div class="user-info">
        <div class="user-info-avatar">
            <img
                class="user-info-img"
                :src="operator && operator.avatar ? '/cms-picture/iam_images/' + operator.avatar : defaultAvatar"
                alt="" />
        </div>
        <div class="user-info-name">{{ operator.name }}</div>
    </div>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import { useOperatorStore } from '@chances/portal_common_core';
    import ImageUpload from '@smartdesk/main/components/image-upload/index.vue';
    // @ts-ignore
    import DefaultAvatar from '@smartdesk/main/assets/default-avatar.png';

    export default defineComponent({
        name: 'BaseUserInfo',
        components: { ImageUpload },
        data() {
            return {
                // 默认头像
                defaultAvatar: DefaultAvatar,
            };
        },
        computed: {
            operator() {
                return useOperatorStore().operator;
            },
        },
    });
</script>

<style scoped>
    .user-info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 32px 0 5px 0;
    }

    .user-info-avatar {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100px;
        height: 100px;
    }

    .user-info-img {
        width: 100%;
        height: 100%;
        border-radius: 50px;
    }

    .user-info-name {
        text-align: center;
        word-break: break-all;
        word-wrap: break-word;
        padding: 5px;
    }

    .user-info-avatar-image-upload :deep(.arco-upload-list-picture) {
        width: 145px;
        height: 145px;
        border-radius: 50%;
    }
</style>
