<template>
    <a-space direction="vertical">
        <a-breadcrumb>
            <a-breadcrumb-item>
                <IconApps />
            </a-breadcrumb-item>
            <a-breadcrumb-item v-for="item in items" :key="item.path" @click="onClick(item)">
                {{ item.name }}
            </a-breadcrumb-item>
        </a-breadcrumb>
    </a-space>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import type { BreadcrumbItem } from '@chances/portal_common_core';
    import { useBreadcrumbStore } from '@chances/portal_common_core';

    export default defineComponent({
        name: 'BaseBreadcrumb',
        data() {
            return {
                items: [] as BreadcrumbItem[],
            };
        },
        methods: {
            // 点击面包屑，跳转对应 path
            onClick(item: BreadcrumbItem) {
                if (item.path) {
                    this.$router.push(item.path);
                }
            },
        },
        watch: {
            $route(to) {
                this.items = useBreadcrumbStore().getBreadcrumbs(to.path);
            },
        },
        mounted() {
            this.items = useBreadcrumbStore().getBreadcrumbs(this.$route.path);
        },
    });
</script>

<style scoped></style>
