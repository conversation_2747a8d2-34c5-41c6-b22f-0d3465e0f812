<script lang="ts">
    import { defineComponent } from 'vue';

    export default defineComponent({
        name: 'BaseIframe',
        props: {
            url: {
                type: String,
                required: true,
            },
        },
    });
</script>

<template>
    <div class="iframe-container">
        <iframe :src="url" width="100%" height="100%" style="border: unset"></iframe>
    </div>
</template>

<style scoped>
    .iframe-container {
        width: 100%;
        height: 100%;
        overflow: hidden;
    }
</style>
