import { defineStore } from 'pinia';
import { RouteLocationNormalized } from 'vue-router';
import type { MenuPathInfo } from '@chances/portal_common_core';
import { STORAGE_DRIVER, useMenuStore } from '@chances/portal_common_core';

export const useTabViewStore = defineStore('tab-view-store', {
    state: () => ({
        tabs: [] as Array<{
            title: string;
            key: string;
            closable: boolean;
            name: string;
        }>,
        activeKey: '/' as string,
        cachedViews: [] as string[],
    }),
    actions: {
        // 添加标签
        addTab(route: RouteLocationNormalized) {
            const { path, meta } = route;

            let title = '';
            const menuPathInfo: MenuPathInfo = useMenuStore().getMenuMap()[path];
            if (menuPathInfo) {
                title = (meta.title as string) || menuPathInfo.name;
            }
            const componentName: string = meta.componentName as string;

            const existingTab = this.tabs.find((tab) => tab.key === path);
            if (!existingTab) {
                this.tabs.push({
                    title: title,
                    key: path,
                    closable: path !== '/',
                    name: componentName,
                });
            }

            // 添加到缓存视图
            if (!this.cachedViews.includes(componentName) && !meta.noCache) {
                this.cachedViews.push(componentName);
            }
            this.activeKey = path;
        },

        // 删除标签
        removeTab(targetKey: string) {
            const index = this.tabs.findIndex((tab) => tab.key === targetKey);
            if (index !== -1) {
                const tab = this.tabs[index];
                this.tabs.splice(index, 1);
                if (tab.name) {
                    this.cachedViews = this.cachedViews.filter((c) => c !== tab.name);
                }

                if (this.activeKey === targetKey) {
                    const nextTab = this.tabs[index] || this.tabs[index - 1];
                    if (nextTab) {
                        this.activeKey = nextTab.key;
                    } else {
                        this.activeKey = '/';
                    }
                }
            }
        },

        // 关闭其他标签
        closeOthers(selectedKey: string) {
            this.tabs = this.tabs.filter((tab) => tab.key === selectedKey || !tab.closable);
            this.cachedViews = this.tabs.map((tab) => tab.name);

            if (!this.tabs.some((tab) => tab.key === this.activeKey)) {
                this.activeKey = selectedKey;
            }
        },

        // 关闭所有标签
        closeAll() {
            this.tabs = this.tabs.filter((tab) => !tab.closable);
            this.cachedViews = this.tabs.filter((tab) => !tab.closable).map((tab) => tab.name);
            this.activeKey = '/';
        },

        // 删除缓存的组件
        delCachedView(componentName: string) {
            this.cachedViews = this.cachedViews.filter((name) => name !== componentName);
        },
    },
    persist: {
        enabled: true,
        storeName: 'base-store',
        driver: STORAGE_DRIVER.LOCAL_STORAGE,
        storeKey: 'tab-view-store',
    },
});
