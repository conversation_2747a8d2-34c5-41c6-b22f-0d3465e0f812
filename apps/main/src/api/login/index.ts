import { RestResultResponse } from '@chances/portal_common_core';
import { LoginForm } from '@smartdesk/main/models';
import { iamHttpClient } from '@smartdesk/common/http';

// auth API
export interface LoginApi {
    // 登录
    login(data: LoginForm): Promise<RestResultResponse<any>>;

    // 获取验证码
    getCaptcha(): Promise<string>;

    // 退出登录
    logout(): Promise<RestResultResponse<any>>;
}

export const loginApi: LoginApi = {
    // 登录
    login(data) {
        return iamHttpClient.post('/auth/login', data).send();
    },

    // 获取验证码
    async getCaptcha(): Promise<string> {
        const res = await iamHttpClient
            .request('/auth/captcha', {
                method: 'GET',
                responseType: 'blob',
            })
            .send();
        const blob = new Blob([res], { type: 'image/jpeg' });
        return URL.createObjectURL(blob);
    },

    // 登出
    logout() {
        return iamHttpClient.post('/auth/logout').send();
    },
};
