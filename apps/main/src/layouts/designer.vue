<template>
    <div class="base-layout" style="z-index: 99999999">
        <slot />
    </div>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';

    export default defineComponent({
        name: 'DesignerLayout',
    });
</script>

<style scoped>
    /* 高度撑满屏幕 */
    .base-layout {
        height: 100%;
    }

    /* 高度撑满屏幕 */
    .base-layout :deep(.arco-layout) {
        height: 100%;
    }

    /* 顶部和底部，固定高度 */
    .base-layout :deep(.arco-layout-header),
    .base-layout :deep(.arco-layout-footer) {
        height: 64px;
        background-color: var(--color-primary-light-4);
    }

    /* 侧边栏默认宽度，可伸缩 */
    .base-layout :deep(.arco-layout-sider) {
        width: 200px;
        min-width: 150px;
        max-width: 500px;
        background-color: var(--color-primary-light-3);
    }

    /* 内容 */
    .base-layout :deep(.arco-layout-content) {
        background-color: rgb(var(--arcoblue-6));
    }

    /* 顶部、底部、侧边栏、内容 */
    .base-layout :deep(.arco-layout-header),
    .base-layout :deep(.arco-layout-footer),
        /*.base-layout :deep(.arco-layout-sider-children),*/
    .base-layout :deep(.arco-layout-content) {
        display: flex;
        flex-direction: column;
        justify-content: center;
        color: var(--color-white);
        font-size: 16px;
        font-stretch: condensed;
        text-align: center;
    }
</style>
