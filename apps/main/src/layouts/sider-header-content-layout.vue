<template>
    <div class="sider-header-content-layout">
        <a-layout>
            <a-layout-sider v-if="showManageLayout" :resize-directions="['right']" class="layout-sider">
                <base-user-info />
                <a-scrollbar style="height: 100%; overflow: auto">
                    <base-menu />
                </a-scrollbar>
            </a-layout-sider>
            <a-layout>
                <a-layout-header v-if="showManageLayout">
                    <base-breadcrumb />
                    <base-tool-bar />
                </a-layout-header>
                <base-tabs-view v-if="showManageLayout"></base-tabs-view>
                <a-layout-content>
                    <div class="layout-content-wrapper">
                        <router-view v-slot="{ Component }">
                            <keep-alive :include="cachedViews">
                                <component :is="Component" />
                            </keep-alive>
                        </router-view>
                    </div>
                </a-layout-content>
            </a-layout>
        </a-layout>
    </div>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import BaseMenu from '../components/base-menu.vue';
    import BaseBreadcrumb from '../components/base-breadcrumb.vue';
    import BaseUserInfo from '../components/base-user-info.vue';
    import BaseToolBar from '../components/base-tool-bar.vue';
    import BaseTabsView from '../components/base-tabs-view.vue';
    import { useTabViewStore } from '@smartdesk/main/stores';

    export default defineComponent({
        name: 'SiderHeaderContentLayout',
        components: {
            BaseTabsView,
            BaseToolBar,
            BaseUserInfo,
            BaseMenu,
            BaseBreadcrumb,
        },
        data() {
            return {
                // 是否显示管理布局
                showManageLayout: true,
            };
        },
        computed: {
            cachedViews() {
                return useTabViewStore().cachedViews;
            },
        },
        methods: {
            inceptRoute() {
                const tabViewStore = useTabViewStore();

                this.showManageLayout = this.$route.meta?.layout !== 'designer';

                if (this.$route.meta?.layout === 'designer') {
                    return;
                }

                if (this.$route.path === '/login') {
                    return;
                }

                tabViewStore.addTab(this.$route);
            },
        },
        watch: {
            $route(to) {
                this.inceptRoute();
            },
        },
        created() {
            this.inceptRoute();
        },
    });
</script>

<style scoped>
    /* 高度撑满屏幕 */
    .sider-header-content-layout {
        height: 100%;
    }

    /* 高度撑满屏幕 */
    .sider-header-content-layout :deep(.arco-layout) {
        height: 100%;
        overflow: hidden;
        background: #f4f1f1;
    }

    /* 顶部和底部，固定高度 */
    .sider-header-content-layout :deep(.arco-layout-header),
    .sider-header-content-layout :deep(.arco-layout-footer) {
        height: 40px;
    }

    /* 侧边栏默认宽度，可伸缩 */
    .sider-header-content-layout :deep(.arco-layout-sider) {
        width: 200px;
        min-width: 150px;
        max-width: 500px;
        margin: 10px 5px 10px 10px;
        border-radius: 4px;
    }

    /* sider children */
    .sider-header-content-layout :deep(.arco-layout-sider-children) {
        overflow: hidden;
    }

    /* layout-sider 滚动条 */
    .layout-sider :deep(.arco-scrollbar) {
        height: calc(100% - 164px);
    }

    /* header */
    .sider-header-content-layout :deep(.arco-layout-header) {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        font-stretch: condensed;
        background: white;
        margin: 10px 10px 5px 5px;
        padding: 0 10px 0 10px;
        border-radius: 4px;
    }

    /* content */
    .sider-header-content-layout :deep(.arco-layout-content) {
        height: 100%;
        overflow: hidden;
        margin: 0 10px 10px 5px;
    }

    .arco-layout-content :deep(.arco-scrollbar) {
        height: 100%;
    }

    .layout-content-wrapper {
        height: 100%;
    }
</style>
