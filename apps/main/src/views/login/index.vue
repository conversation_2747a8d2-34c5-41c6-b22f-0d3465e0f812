<template>
    <div class="con">
        <div class="header-box">
            <img class="logo-img" src="../../assets/img/logo.png" alt="" />
            <div class="header-title">可视化管理系统</div>
        </div>
        <div class="login-container">
            <h2 class="login-title">用户登录</h2>
            <a-form ref="loginForm" :model="loginForm" :rules="formRules" auto-label-width>
                <a-form-item laba="账号" field="userId" hide-asterisk hide-label>
                    <div class="item-form">
                        <a-input v-model="loginForm.userId" placeholder="请输入账号" allow-clear class="login-input">
                            <template #prefix>
                                <icon-user />
                            </template>
                        </a-input>
                    </div>
                </a-form-item>
                <a-form-item laba="密码" field="password" hide-asterisk hide-label>
                    <div class="item-form">
                        <shadow-password-input
                            v-model="loginForm.password"
                            placeholder="请输入密码"
                            class="login-input"
                            @keyup.enter="onClickLogin" />
                    </div>
                </a-form-item>
                <a-form-item laba="验证码" field="captcha" hide-asterisk hide-label>
                    <div class="item-form">
                        <a-input
                            v-model="loginForm.captcha"
                            placeholder="请输入验证码"
                            allow-clear
                            class="captcha-input"
                            @keyup.enter="onClickLogin">
                            <template #prefix>
                                <icon-safe />
                            </template>
                        </a-input>
                        <img :src="captchaImageUrl" alt="" class="captcha-image" @click="onClickCaptcha" />
                    </div>
                </a-form-item>
                <a-form-item class="btn-group" hide-asterisk hide-label>
                    <a-button type="primary" class="btn-item" @click="onClickLogin">登录</a-button>
                </a-form-item>
            </a-form>
        </div>
        <div class="footer-title">技术支持：上海成思信息科技有限公司</div>
    </div>
</template>

<script lang="ts">
    import { Message } from '@arco-design/web-vue';
    // @ts-ignore
    import CryptoJS from 'crypto-js';
    import {
        useBreadcrumbStore,
        useMenuStore,
        useOperatorStore,
        usePermissionStore,
    } from '@chances/portal_common_core';
    import { LoginForm } from '@smartdesk/main/models';
    import { loginApi } from '@smartdesk/main/api';
    import ShadowPasswordInput from '@smartdesk/main/components/shadow-password-input.vue';

    export default {
        components: {
            ShadowPasswordInput,
        },
        data() {
            return {
                loginForm: new LoginForm(),
                captchaImageUrl: '',
                formRules: {
                    userId: [
                        {
                            required: true,
                            message: '请输入登录账号',
                            trigger: 'blur',
                        },
                    ],
                    password: [
                        {
                            required: true,
                            message: '请输入密码',
                            trigger: 'blur',
                        },
                    ],
                    captcha: [
                        {
                            required: true,
                            message: '请输入验证码',
                            trigger: 'blur',
                        },
                    ],
                },
            };
        },
        methods: {
            // 点击验证码
            onClickCaptcha() {
                loginApi.getCaptcha().then((res) => {
                    this.captchaImageUrl = res;
                });
            },
            // 点击登录
            onClickLogin() {
                (this.$refs.loginForm as any).validate((valid: boolean) => {
                    if (!valid) {
                        const form = Object.assign({}, this.loginForm);
                        form.password = CryptoJS.MD5(this.loginForm.password).toString();
                        loginApi.login(form).then((res) => {
                            if (res.result) {
                                // 设置当前操作员
                                const operator = res.result.accessTokenModel?.operatorContext;
                                operator.avatar = res.result.operator?.avatar;
                                operator.phone = res.result.operator?.phone;
                                operator.mail = res.result.operator?.mail;
                                useOperatorStore().setOperator(operator);
                                useOperatorStore().setLoginModel(res.result);

                                // 设置 access token
                                const token = res.result.accessTokenModel?.accessToken;
                                if (token) {
                                    sessionStorage.setItem('token', token);
                                }

                                // 设置菜单
                                useMenuStore().setMenus(res.result.menus);

                                // 初始化面包屑数据
                                useBreadcrumbStore().init(useMenuStore().getMenuMap());

                                // 设置权限
                                usePermissionStore().setPermissions(res.result.apis);

                                Message.success('登录成功');
                                this.$router.push('/');
                            } else {
                                Message.error(res.msg);
                                this.onClickCaptcha();
                                this.loginForm.captcha = '';
                            }
                        });
                    }
                });
            },
        },
        mounted() {
            this.onClickCaptcha();
        },
    };
</script>

<style scoped>
    .con {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100vh;
        background: url(../../assets/img/bg_login.png) no-repeat;
        background-size: cover;
    }

    .con :deep(.arco-form-item-content-flex) {
        align-items: center;
        justify-content: center;
    }

    .con :deep(.arco-form-item-message) {
        margin: 0 0 0 60px;
    }

    .logo-img {
        width: 70px;
        height: 60px;
    }

    .header-box {
        display: flex;
        margin-top: 30px;
        align-items: center;
        margin-left: 70px;
    }

    .header-title {
        height: 70px;
        display: flex;
        align-items: center;
        border-left: 1px solid rgba(220, 223, 230, 1);
        margin-left: 30px;
        padding-left: 30px;
        font-size: 24px;
        font-weight: bold;
        color: #68696b;
    }

    .login-container {
        width: 400px;
        height: 330px;
        padding-top: 30px;
        margin-top: 9%;
        margin-left: 70%;
        border: 1px solid #ccc;
        border-radius: 5px;
        background-color: #fff;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    }

    .login-title {
        text-align: center;
        font-size: 24px;
        margin-bottom: 20px;
        color: #20b094;
    }

    .login-input {
        width: 290px;
        height: 48px;
    }

    .captcha-input {
        flex: 1;
        margin-right: 10px;
        height: 48px;
        width: 150px;
    }

    .captcha-image {
        cursor: pointer;
        width: 128px;
        height: 48px;
    }

    .btn-group {
        text-align: center;
    }

    .btn-item {
        width: 290px;
        height: 40px;
        background-color: #20b094;
    }

    .btn-item:hover {
        opacity: 0.7;
        background-color: #20b094;
    }

    .footer-title {
        width: 250px;
        margin-top: 10%;
        margin-left: calc(50% - 125px);
        font-weight: 900;
        color: #68696b;
    }

    .item-form {
        display: flex;
    }

    .item-form :deep(.arco-input-wrapper),
    .item-form :deep(.arco-select-view-single),
    .item-form :deep(.arco-picker),
    .item-form :deep(.arco-select-view-multiple) {
        background: rgba(255, 255, 255, 1);
        border: 1px solid rgba(220, 223, 230, 1);
        border-radius: 4px;
    }
</style>
