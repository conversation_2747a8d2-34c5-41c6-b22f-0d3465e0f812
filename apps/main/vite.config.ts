import { defineConfig } from 'vite';
// @ts-ignore
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import Components from 'unplugin-vue-components/vite';

// @ts-ignore
export default defineConfig(({}) => {
    return {
        plugins: [
            vue(),
            Components({
                resolvers: [
                    IconsResolver({
                        prefix: 'i',
                        enabledCollections: ['mdi'],
                    }),
                ],
                dts: false,
            }),
            Icons({
                compiler: 'vue3',
                autoInstall: true,
                scale: 1,
                defaultClass: 'inline-block',
            }),
        ],
        css: {
            postcss: './postcss.config.js',
        },
        build: {
            sourcemap: false,
            outDir: '../../dist',
            emptyOutDir: true,
            rollupOptions: {
                input: resolve(__dirname, 'index.html'),
                treeshake: {
                    annotations: false,
                    moduleSideEffects: () => true,
                },
            },
            minify: 'terser',
            terserOptions: {
                compress: {
                    drop_console: true,
                    drop_debugger: true,
                },
            },
        },
        resolve: {
            alias: {
                '@smartdesk/main': resolve(__dirname, 'src'),
                '@smartdesk/common': resolve(__dirname, '../../packages/common/src'),
                '@smartdesk/preview': resolve(__dirname, '../../packages/preview/src'),
                '@smartdesk/design': resolve(__dirname, '../../packages/design/src'),
                '@smartdesk/admin': resolve(__dirname, '../../packages/admin/src'),
            },
        },
        server: {
            proxy: {
                '/smartdesk_admin': {
                    target: 'http://localhost:10015',
                    changeOrigin: true,
                },
                '/cms_adapter': {
                    target: 'http://localhost:8808',
                    changeOrigin: true,
                },
                '/cms-picture': {
                    target: 'http://localhost:81',
                    changeOrigin: true,
                },
                '/cs_iam': {
                    target: 'http://localhost:7002',
                    changeOrigin: true,
                },
                '/usergroup': {
                    target: 'http://**************:9000',
                    changeOrigin: true,
                },
                '/audit_publish': {
                    target: 'http://localhost:10015',
                    changeOrigin: true,
                    rewrite: (path) => path.replace(/^\/audit_publish/, '/smartdesk_admin'),
                },
            },
            // proxy: {
            //     '/smartdesk_admin': {
            //         target: 'http://*************:58093',
            //         changeOrigin: true,
            //     },
            //     '/cms_adapter': {
            //         target: 'http://*************:58093',
            //         changeOrigin: true,
            //     },
            //     '/cms-picture': {
            //         target: 'http://*************:58093',
            //         changeOrigin: true,
            //     },
            //     '/cs_iam': {
            //         target: 'http://*************:58093',
            //         changeOrigin: true,
            //     },
            //     '/usergroup': {
            //         target: 'http://*************:58093',
            //         changeOrigin: true,
            //     },
            //     '/audit_publish': {
            //         target: 'http://*************:58093',
            //         changeOrigin: true,
            //         rewrite: (path) =>
            //             path.replace(/^\/audit_publish/, '/smartdesk_admin'),
            //     },
            // },
        },
    };
});
