# SmartDesk Portal - 可视化前端页面管理系统

## 📖 项目概述

SmartDesk Portal 是一个基于 Vue3 + TypeScript + Vite + Pinia 的可视化前端页面管理系统，采用 monorepo 架构设计。系统提供了强大的可视化页面设计、楼层布局定义、组件管理和预览功能，支持拖拽式页面构建和实时预览。

### 🎯 主要功能

- **可视化页面设计器** - 支持拖拽式页面设计，所见即所得的编辑体验
- **楼层定义设计器** - 灵活的页面楼层布局设计和管理
- **实时预览功能** - 支持多设备预览，实时查看设计效果
- **组件管理系统** - 丰富的组件库和自定义组件支持
- **样式管理** - 可视化样式编辑和主题管理
- **权限管理** - 完善的用户权限和角色管理
- **多端适配** - 支持不同设备类型和分辨率适配

## 🛠 技术栈

### 核心技术

- **Vue 3.5.13** - 渐进式 JavaScript 框架
- **TypeScript 5.5.3** - JavaScript 的超集，提供静态类型检查
- **Vite 5.4.0** - 下一代前端构建工具
- **Pinia 2.3.1** - Vue 的状态管理库
- **Vue Router 4.5.0** - Vue.js 官方路由管理器

### UI 组件库

- **Element Plus 2.9.10** - 基于 Vue 3 的桌面端组件库
- **Arco Design 2.57.0** - 字节跳动出品的企业级设计语言
- **Tailwind CSS 3.4.0** - 实用优先的 CSS 框架

### 开发工具

- **pnpm 8.0.0+** - 快速、节省磁盘空间的包管理器
- **Lerna 8.1.8** - 多包管理工具
- **ESLint + Prettier** - 代码质量和格式化工具
- **Husky + lint-staged** - Git hooks 和代码提交检查

### 其他依赖

- **html2canvas-pro** - 页面截图功能
- **vue-draggable-plus** - 拖拽功能支持
- **axios** - HTTP 客户端
- **dayjs** - 日期处理库

## 🏗 项目架构

```
smartdesk-portal/
├── apps/                          # 应用层
│   └── main/                      # 主应用（基座）
│       ├── src/
│       │   ├── components/        # 基座组件
│       │   ├── layouts/           # 布局组件
│       │   ├── router/            # 路由配置
│       │   ├── stores/            # 状态管理
│       │   ├── utils/             # 工具函数
│       │   └── views/             # 页面组件
│       └── package.json
├── packages/                      # 功能包
│   ├── common/                    # 公共模块
│   │   ├── src/
│   │   │   ├── api/               # API 接口
│   │   │   ├── components/        # 公共组件
│   │   │   ├── composables/       # 组合式函数
│   │   │   ├── constant/          # 常量定义
│   │   │   ├── stores/            # 公共状态
│   │   │   ├── types/             # 类型定义
│   │   │   └── utils/             # 工具函数
│   │   └── package.json
│   ├── design/                    # 设计器模块
│   │   ├── src/
│   │   │   ├── components/        # 设计器组件
│   │   │   ├── stores/            # 设计器状态
│   │   │   ├── views/             # 设计器页面
│   │   │   └── router/            # 设计器路由
│   │   └── package.json
│   ├── preview/                   # 预览模块
│   │   ├── src/
│   │   │   ├── components/        # 预览组件
│   │   │   ├── stores/            # 预览状态
│   │   │   ├── views/             # 预览页面
│   │   │   └── router/            # 预览路由
│   │   └── package.json
│   └── admin/                     # 管理模块
│       ├── src/
│       │   ├── components/        # 管理组件
│       │   ├── views/             # 管理页面
│       │   └── router/            # 管理路由
│       └── package.json
├── dist/                          # 构建输出目录
├── package.json                   # 根包配置
├── pnpm-workspace.yaml           # pnpm 工作空间配置
├── lerna.json                     # Lerna 配置
└── tsconfig.json                  # TypeScript 配置
```

## 📦 模块说明

### @smartdesk/main (主应用)

- **功能**: 应用基座，负责整合各个功能模块
- **职责**: 路由管理、权限控制、布局管理、全局状态
- **特点**: 微前端架构，支持模块化加载

### @smartdesk/common (公共模块)

- **功能**: 提供公共组件、工具函数、API 接口等
- **职责**: 基础设施、通用逻辑、类型定义
- **特点**: 被其他模块依赖，提供统一的基础能力

### @smartdesk/design (设计器模块)

- **功能**: 可视化页面设计和楼层定义
- **职责**: 页面设计器、楼层设计器、组件编辑
- **特点**: 支持拖拽操作、实时预览、可视化编辑

### @smartdesk/preview (预览模块)

- **功能**: 页面预览和设备模拟
- **职责**: 多设备预览、实时渲染、交互测试
- **特点**: 支持多种设备类型、实时数据模拟

### @smartdesk/admin (管理模块)

- **功能**: 系统管理和配置
- **职责**: 组件管理、样式管理、用户管理、权限配置
- **特点**: 完善的后台管理功能

## 🚀 环境要求

- **Node.js**: >= 18.0.0
- **pnpm**: >= 8.0.0
- **浏览器**: 支持 ES2020 的现代浏览器

## 📥 安装和运行

### 1. 克隆项目

```bash
git clone <repository-url>
cd smartdesk-portal
```

### 2. 安装依赖

```bash
# 安装 pnpm (如果未安装)
npm install -g pnpm

# 安装项目依赖
pnpm install
```

### 3. 启动开发服务器

```bash
# 启动主应用
pnpm run dev
```

### 4. 构建项目

```bash
# 构建主应用
pnpm run build
```

## 🔧 开发指南

### 代码规范

- 使用 TypeScript 进行类型检查
- 遵循 Vue 3 Composition API 最佳实践
- 使用 Prettier 进行代码格式化
- 提交前自动执行 lint-staged 检查

### 开发流程

1. 从 main 分支创建功能分支
2. 在对应的模块中进行开发
3. 运行测试确保功能正常
4. 提交代码并创建 Pull Request
5. 代码审查通过后合并到 main 分支

### 调试技巧

- 使用 Vue DevTools 进行组件调试
- 利用 Vite 的 HMR 功能快速开发
- 使用浏览器开发者工具进行网络和性能调试

## 🌐 API 接口配置

项目通过 Vite 代理配置连接后端服务：

```javascript
// apps/main/vite.config.ts 中的代理配置
proxy: {
  '/smartdesk_admin': 'http://localhost:10015',  // 管理接口
  '/cms_adapter': 'http://localhost:8808',       // CMS 适配器
  '/cms-picture': 'http://localhost:81',         // 图片服务
  '/cs_iam': 'http://localhost:7002',           // 身份认证
  '/usergroup': 'http://**************:9000',   // 用户组服务
  '/audit_publish': 'http://localhost:10015'     // 审核发布
}
```

---

**注意**: 请确保在开发前阅读完整的开发文档，并遵循项目的编码规范和最佳实践。
