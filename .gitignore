# Dependencies
node_modules/
.pnpm-store/

# Build outputs
dist/
build/
*.tsbuildinfo
/tmp
/out-tsc

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE
.vscode/*
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Package manager
/.pnp
.pnp.js

# Coverage
coverage/
*.lcov
.nyc_output

# Cache
.cache
.parcel-cache
.eslintcache

pnpm-lock.yaml
