<template>
    <div class="rule-container">
        <div class="left-panel">
            <rule-tree ref="ruleTreeRef" @node-click="handleNodeClick" />
        </div>
        <div class="right-panel">
            <rule-list ref="ruleListRef" :current-node="currentNode" @add="handleAdd" @edit="handleEdit" />
        </div>
    </div>
    <rule-add
        v-model:visible="addVisible"
        :is-edit="isEdit"
        :current-data="currentRule"
        @save="handleSave"
        @sync="handleSync" />
    <rule-sync-dialog
        v-model:visible="syncDialogVisible"
        :rule-code="currentRule?.code"
        :sync-data="syncData"
        @confirm="handleSyncConfirm" />
</template>

<script setup lang="ts">
    import { ref } from 'vue';
    import { PersonalRule, PersonalRuleFolderModel } from '@smartdesk/common/types';
    import RuleTree from './components/rule-tree.vue';
    import RuleList from './components/rule-list.vue';
    import RuleAdd from './components/rule-add.vue';
    import RuleSyncDialog from './components/rule-sync-dialog.vue';
    import { personalRuleApi } from '@smartdesk/common/api';
    import { useFeedback } from '@smartdesk/common/composables';

    defineOptions({
        name: 'PersonalRule',
    });

    // pinia store
    const feedback = useFeedback();

    // 策略目录树引用
    const ruleTreeRef = ref();

    // 策略列表引用
    const ruleListRef = ref<InstanceType<typeof RuleList> | null>(null);

    // 当前选中的策略目录节点
    const currentNode = ref<PersonalRuleFolderModel | null>(null);

    // 抽屉控制
    const addVisible = ref(false);

    // 是否是编辑
    const isEdit = ref(false);

    // 当前策略
    const currentRule = ref<any>(null);

    // 同步对话框数据
    const syncDialogVisible = ref(false);
    const syncData = ref<Record<string, any[]>>({});

    // 点击节点
    const handleNodeClick = (node: PersonalRuleFolderModel) => {
        currentNode.value = node;
    };

    // 刷新列表
    const refreshList = () => {
        ruleListRef.value?.getPersonalRuleList();
    };

    // 新增策略
    const handleAdd = () => {
        isEdit.value = false;
        currentRule.value = null;
        addVisible.value = true;
    };

    // 编辑策略
    const handleEdit = (row: any) => {
        isEdit.value = true;
        currentRule.value = row;
        addVisible.value = true;
    };

    // 保存策略
    const handleSave = async (formData: PersonalRule) => {
        formData.folderCode = currentNode.value?.code || '';
        if (isEdit.value && currentRule.value.code) {
            const res = await personalRuleApi.updatePersonalRule(currentRule.value.code, formData);
            if (res.code === 200) {
                feedback.success('更新策略成功');
            } else {
                feedback.error('更新策略失败：' + res.msg);
            }
        } else {
            const res = await personalRuleApi.createPersonalRule(formData);
            if (res.code === 200) {
                feedback.success('新增策略成功');
            } else {
                feedback.error('新增策略失败：' + res.msg);
            }
        }
        refreshList();
    };

    // 处理同步请求
    const handleSync = async () => {
        // 获取同步数据
        const res = await personalRuleApi.getSyncData(currentRule.value.code);
        if (res.code === 200) {
            syncData.value = res.result;
            syncDialogVisible.value = true;
            feedback.success('同步成功');
        } else {
            feedback.error('同步失败：' + res.msg);
        }
    };

    // 确认同步
    const handleSyncConfirm = async (data: Record<string, any[]>) => {
        if (!currentRule.value?.code) {
            return;
        }

        const res = await personalRuleApi.updateReference(currentRule.value.code, data);
        if (res.code === 200) {
            feedback.success('同步成功');
            syncDialogVisible.value = false;
            refreshList();
        } else {
            feedback.error('同步失败：' + res.msg);
        }
    };
</script>

<style scoped>
    .rule-container {
        height: 100%;
        display: flex;
        gap: 20px;
    }

    .left-panel {
        width: 300px;
        background: white !important;
        border-radius: 10px;
        padding: 10px;
        display: flex;
        flex-direction: column;
        height: calc(100vh - 100px);
        gap: 16px;
    }

    .right-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        border-radius: 10px;
        gap: 10px;
        height: calc(100vh - 100px);
        min-width: 0; /* 防止溢出 */
    }
</style>
