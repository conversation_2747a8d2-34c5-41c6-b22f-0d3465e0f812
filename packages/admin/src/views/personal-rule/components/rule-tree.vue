<template>
    <div class="rule-tree">
        <div class="catalog-header">
            <span class="title">推荐策略目录</span>
            <el-button
                :disabled="!permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PERSONAL_RULE_FOLDER.CREATE)"
                type="primary"
                size="small"
                @click="handleAdd">
                新增目录
            </el-button>
        </div>
        <div class="search-box">
            <el-input
                v-model="keyword"
                placeholder="搜索目录..."
                prefix-icon="Search"
                clearable
                @keyup.enter="handleSearchInput" />
        </div>
        <el-tree
            ref="treeRef"
            :data="treeData"
            :props="defaultProps"
            node-key="code"
            :highlight-current="true"
            :expand-on-click-node="false"
            :default-expanded-keys="expandedKeys">
            <template #default="{ node, data }">
                <span class="custom-tree-node">
                    <span
                        class="expand-icon"
                        :class="{ 'is-leaf': isLeaf(data) }"
                        @click="() => handleExpand(node)"></span>
                    <span class="node-content" @click="() => handleNodeClick(data)">
                        <el-tooltip :content="data.name" placement="top" :show-after="500">
                            <span
                                :class="{
                                    highlight: keyword && data.name.toLowerCase().includes(keyword.toLowerCase()),
                                }">
                                {{ node.label }}
                            </span>
                        </el-tooltip>
                        <span class="operation-buttons">
                            <el-tooltip content="编辑" placement="top">
                                <el-button
                                    :disabled="
                                        !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.PERSONAL_RULE_FOLDER.EDIT, {
                                            type: 'org',
                                            value: data.orgId,
                                        })
                                    "
                                    type="primary"
                                    link
                                    :icon="Edit"
                                    @click.stop="handleEdit(data)" />
                            </el-tooltip>
                            <el-tooltip content="删除" placement="top">
                                <el-button
                                    :disabled="
                                        !canDeleteByAdmin(data) ||
                                        !permissionStore.hasPermission(
                                            ADMIN_BIZ_PERMISSION.PERSONAL_RULE_FOLDER.DELETE,
                                            { type: 'org', value: data.orgId }
                                        )
                                    "
                                    type="danger"
                                    link
                                    :icon="Delete"
                                    @click.stop="handleDelete(data)" />
                            </el-tooltip>
                        </span>
                    </span>
                </span>
            </template>
        </el-tree>

        <el-dialog v-model="addDialogVisible" width="30%" @close="handleAddCancel">
            <template #header>
                <span v-if="currentNode?.name" class="dialog-title"> 新增子目录 </span>
                <span v-else class="dialog-title"> 新增根目录 </span>
            </template>
            <el-form label-width="92px" label-suffix=":" ref="formRef" :model="addForm" :rules="rules">
                <el-form-item v-if="currentNode?.name" label="父目录" prop="name">
                    <el-input v-model="currentNode.name" disabled />
                </el-form-item>
                <el-form-item label="目录名称" prop="name">
                    <el-input v-model="addForm.name" />
                </el-form-item>
                <el-form-item label="组织" prop="orgId">
                    <dimension-selector
                        v-model="addForm.orgId"
                        placeholder="请选择组织"
                        :data="orgTree"
                        label-key="name"
                        value-key="id"
                        width="370" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="handleAddCancel">取消</el-button>
                <el-button type="primary" @click="handleAddConfirm">确认 </el-button>
            </template>
        </el-dialog>

        <!-- 添加修改目录对话框 -->
        <el-dialog v-model="editDialogVisible" title="修改目录" width="30%" @close="handleEditCancel">
            <el-form label-width="92px" label-suffix=":" ref="editFormRef" :model="editForm" :rules="rules">
                <el-form-item label="目录名称" prop="name">
                    <el-input v-model="editForm.name" />
                </el-form-item>
                <el-form-item label="组织" prop="orgId">
                    <dimension-selector
                        v-model="editForm.orgId"
                        placeholder="请选择组织"
                        :data="orgTree"
                        label-key="name"
                        value-key="id"
                        width="370" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="handleEditCancel">取消</el-button>
                <el-button type="primary" @click="handleEditConfirm">确认 </el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref } from 'vue';
    import type { ElTree, FormInstance } from 'element-plus';
    import { Dimension, PersonalRuleFolderModel } from '@smartdesk/common/types';
    import { dimensionApi, personalRuleFolderApi } from '@smartdesk/common/api';
    import { Delete, Edit } from '@element-plus/icons-vue';
    import { ADMIN_BIZ_PERMISSION, canDeleteByAdmin } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';

    // 事件
    const emit = defineEmits(['node-click']);

    // pinia store
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();
    // 目录搜索关键字
    const keyword = ref<string>('');

    // 目录树配置
    const defaultProps = {
        children: 'children',
        label: 'name',
    };
    // 目录树数据
    const treeData = ref<PersonalRuleFolderModel[]>([]);

    // 展开的节点keys
    const expandedKeys = ref<string[]>([]);

    // 获取所有匹配节点的路径
    const getMatchedNodePaths = (
        nodes: PersonalRuleFolderModel[],
        keyword: string,
        parentPath: string[] = [],
        result: Set<string> = new Set()
    ) => {
        nodes.forEach((node) => {
            const currentPath = [...parentPath, node.code];
            if (node.name.toLowerCase().includes(keyword.toLowerCase())) {
                // 如果节点匹配，添加整个路径
                currentPath.forEach((code) => result.add(code));
            }
            if (node.children) {
                getMatchedNodePaths(node.children, keyword, currentPath, result);
            }
        });
        return Array.from(result);
    };

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 树形组件引用
    const treeRef = ref<InstanceType<typeof ElTree> | null>(null);

    // 当前选中的目录节点
    const currentNode = ref<PersonalRuleFolderModel | null>(null);

    // 新增目录对话框显示状态
    const addDialogVisible = ref(false);

    // 新增目录表单
    const addForm = ref<PersonalRuleFolderModel>({} as PersonalRuleFolderModel);

    // 表单校验规则
    const rules = {
        name: [{ required: true, message: '请输入目录名称', trigger: 'blur' }],
    };

    // 表单引用
    const formRef = ref<FormInstance>();

    // 修改表单
    const editDialogVisible = ref(false);
    const editForm = ref<PersonalRuleFolderModel>({} as PersonalRuleFolderModel);

    // 表单引用
    const editFormRef = ref<FormInstance>();

    // 节点点击事件
    const handleNodeClick = (node: PersonalRuleFolderModel) => {
        currentNode.value = node;
        emit('node-click', node);
    };
    // 新增目录
    const handleAdd = () => {
        addForm.value = {} as PersonalRuleFolderModel;
        addForm.value.parentCode = currentNode.value?.code || '';
        addDialogVisible.value = true;
    };

    // 确认新增
    const handleAddConfirm = async () => {
        if (!formRef.value) return;

        await formRef.value.validate(async (valid, fields) => {
            if (valid) {
                try {
                    await personalRuleFolderApi.createFolder({
                        ...addForm.value,
                    });
                    feedback.success('新增成功');
                    keyword.value = '';
                    addDialogVisible.value = false;
                    // 重新加载树数据
                    await getPersonalRuleFolderTree();
                } catch (error) {
                    console.error('新增失败:', error);
                    feedback.error('新增失败');
                }
            }
        });
    };

    // 取消新增
    const handleAddCancel = () => {
        addDialogVisible.value = false;
    };

    // 判断是否为叶子节点
    const isLeaf = (data: PersonalRuleFolderModel) => {
        return !data.children || data.children.length === 0;
    };

    const handleSearchInput = () => {
        getPersonalRuleFolderTree();
    };

    // 获取目录树
    const getPersonalRuleFolderTree = async () => {
        try {
            const res = await personalRuleFolderApi.getPersonalRuleFolderTree(false, '', keyword.value);
            treeData.value = res.result || [];

            // 根据搜索关键字决定展开的节点
            if (keyword.value) {
                // 获取所有匹配节点的路径
                expandedKeys.value = getMatchedNodePaths(treeData.value, keyword.value);
            } else {
                // 清空搜索时折叠所有节点
                expandedKeys.value = [];
            }
        } catch (error) {
            feedback.error('获取目录树失败');
            treeData.value = [];
        }
    };
    // 展开/收起节点
    const handleExpand = (node: any) => {
        if (!node.isLeaf) {
            node.expanded = !node.expanded;
        }
    };

    // 修改目录
    const handleEdit = (node: PersonalRuleFolderModel) => {
        editForm.value = { ...node };
        editDialogVisible.value = true;
    };

    // 确认修改
    const handleEditConfirm = async () => {
        if (!editFormRef.value) return;

        await editFormRef.value.validate(async (valid, fields) => {
            if (valid) {
                try {
                    if (editForm.value?.code) {
                        // 修改
                        await personalRuleFolderApi.updateFolder(editForm.value.code, editForm.value);
                        feedback.success('修改成功');
                    }
                    editDialogVisible.value = false;
                    // 重新加载树数据
                    await getPersonalRuleFolderTree();
                } catch (error) {
                    console.error('操作失败:', error);
                    feedback.error('操作失败');
                }
            }
        });
    };

    // 取消修改
    const handleEditCancel = () => {
        editDialogVisible.value = false;
    };

    // 删除目录
    const handleDelete = async (node: PersonalRuleFolderModel) => {
        if (await feedback.confirm(`确定要删除该目录吗？`, '确认操作', 'warning')) {
            await personalRuleFolderApi.deleteFolder(node.code);
            feedback.success('删除成功');
            await getPersonalRuleFolderTree();
        }
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 调整暴露的刷新方法
    defineExpose({
        getPersonalRuleFolderTree,
    });
    onMounted(() => {
        getPersonalRuleFolderTree();
        getOrgTree();
    });
</script>

<style scoped>
    .rule-tree {
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
    }

    .catalog-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    .catalog-header .title {
        font-size: 16px;
        font-weight: bold;
    }

    .search-box {
        margin-bottom: 16px;
    }

    :deep(.el-tree) {
        /* 减小节点的缩进距离 */
        --el-tree-node-hover-bg-color: var(--el-fill-color-light);
        --el-tree-indent: 12px; /* 减小缩进距离,默认是16px */
        overflow-x: auto;
        overflow-y: auto;
        flex: 1;
        width: 100%;
        padding-bottom: 8px; /* 为滚动条预留空间 */
    }

    /* 自定义滚动条样式 */
    :deep(.el-tree::-webkit-scrollbar) {
        width: 6px;
        height: 6px;
    }

    :deep(.el-tree::-webkit-scrollbar-thumb) {
        background: #dcdfe6;
        border-radius: 3px;
    }

    :deep(.el-tree::-webkit-scrollbar-track) {
        background: #f5f7fa;
    }

    :deep(.el-tree-node) {
        white-space: nowrap;
        min-width: max-content;
    }

    :deep(.el-tree-node__content) {
        height: 32px;
        padding-right: 24px;
        min-width: max-content;
    }

    :deep(.el-tree-node__children) {
        white-space: nowrap;
    }

    /* 文本溢出时显示省略号 */
    :deep(.el-tree-node__label) {
        display: inline-block;
        max-width: 220px; /* 增加最大宽度 */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .custom-tree-node {
        display: flex;
        align-items: center;
        width: 100%;
        min-width: max-content;
    }

    .expand-icon {
        cursor: pointer;
        padding: 0 6px;
    }

    .expand-icon.is-leaf {
        visibility: hidden;
    }

    .node-content {
        flex: 1;
        padding: 0 6px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        min-width: max-content;
    }

    .operation-buttons {
        display: none;
        margin-left: 8px;
        white-space: nowrap;
    }

    /* 鼠标悬浮时显示操作按钮 */
    .node-content:hover .operation-buttons {
        display: inline-flex;
        align-items: center;
    }

    :deep(.el-button--small) {
        padding: 4px;
    }

    /* 新增高亮样式 */
    .highlight {
        background-color: #e28181;
        color: #000;
        padding: 2px 4px;
        border-radius: 3px;
    }

    .dialog-title {
        font-size: 16px;
        font-weight: bold;
        padding: 8px 16px;
        margin-bottom: 10px;
    }
</style>
