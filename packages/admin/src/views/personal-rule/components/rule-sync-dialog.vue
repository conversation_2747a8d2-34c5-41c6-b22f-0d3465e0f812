<template>
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        width="75%"
        :close-on-click-modal="false"
        @close="handleClose"
        :z-index="2000">
        <div class="sync-dialog-content">
            <!-- 替换按钮 -->
            <div class="action-bar" v-if="props.mode === 'view'">
                <el-button type="primary" @click="handleSwitchToReplace">替换引用</el-button>
            </div>

            <!-- 目标策略选择框，仅在替换模式下显示 -->
            <div v-if="props.mode === 'replace'" class="target-rule-select">
                <div class="select-container">
                    <div class="select-label">目标策略：</div>
                    <el-select
                        v-model="targetRuleCode"
                        filterable
                        placeholder="请选择目标策略"
                        class="target-select"
                        :loading="loading"
                        @change="handleTargetChange">
                        <el-option
                            v-for="item in ruleOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"
                            :disabled="item.code === props.ruleCode" />
                    </el-select>
                    <el-button v-if="targetRuleCode" type="primary" link @click="handleViewTargetRule">
                        查看策略
                    </el-button>
                </div>
            </div>

            <!-- 目标策略详情 -->
            <div v-if="showTargetRules" class="target-rule-detail">
                <div class="detail-header">
                    <div class="detail-title">策略详情</div>
                    <el-button type="primary" link @click="showTargetRules = false">收起</el-button>
                </div>
                <div class="detail-content">
                    <el-descriptions :column="1" border>
                        <el-descriptions-item
                            v-if="targetRuleDetail.rules?.userIdType !== undefined"
                            label="用户尾号奇偶">
                            {{ enumStore.getLabelByKeyAndValue('userIdType', targetRuleDetail.rules.userIdType) }}
                        </el-descriptions-item>
                        <el-descriptions-item v-if="targetRuleDetail.rules?.epgGroups?.length" label="用户分群">
                            {{ getEpgGroupNames(targetRuleDetail.rules.epgGroups) }}
                        </el-descriptions-item>
                        <el-descriptions-item v-if="targetRuleDetail.rules?.vipLevels?.length" label="会员等级">
                            {{ getVipLevelNames(targetRuleDetail.rules.vipLevels) }}
                        </el-descriptions-item>
                        <el-descriptions-item v-if="targetRuleDetail.rules?.userGroup?.length" label="用户分组">
                            {{ getUserGroupNames(targetRuleDetail.rules.userGroup) }}
                        </el-descriptions-item>
                        <el-descriptions-item v-if="targetRuleDetail.rules?.versionName?.length" label="apk版本号">
                            {{ targetRuleDetail.rules.versionName.join(', ') }}
                        </el-descriptions-item>
                        <el-descriptions-item v-if="targetRuleDetail.rules?.stbType?.length" label="机顶盒型号">
                            {{ getStbTypeNames(targetRuleDetail.rules.stbType) }}
                        </el-descriptions-item>
                        <el-descriptions-item v-if="targetRuleDetail.rules?.subAreaCode?.length" label="地区">
                            {{ getSubAreaNames(targetRuleDetail.rules.subAreaCode) }}
                        </el-descriptions-item>
                        <el-descriptions-item v-if="targetRuleDetail.rules?.areaCode?.length" label="区域">
                            {{ getAreaNames(targetRuleDetail.rules.areaCode) }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </div>

            <el-collapse v-model="activeNames">
                <el-collapse-item
                    v-for="panel in PANELS"
                    :key="panel.type"
                    :name="panel.type"
                    :title="getTitle(panel.name, filteredData[panel.type].length)"
                    :disabled="!filteredData[panel.type].length">
                    <el-table
                        :data="filteredData[panel.type]"
                        border
                        stripe
                        @selection-change="(val: any) => handleSelectionChange(val, panel.type)">
                        <el-table-column v-if="props.mode !== 'view'" type="selection" width="50" />
                        <el-table-column
                            :prop="panel.nameField"
                            :label="`${panel.name}名称`"
                            min-width="200"
                            show-overflow-tooltip />
                        <el-table-column
                            :prop="panel.codeField"
                            :label="`${panel.name}索引`"
                            min-width="200"
                            show-overflow-tooltip />
                    </el-table>
                </el-collapse-item>
            </el-collapse>
        </div>

        <template #footer>
            <span class="dialog-footer" v-if="props.mode === 'view'">
                <el-button @click="handleClose">关闭</el-button>
            </span>
            <span class="dialog-footer" v-else-if="props.mode === 'sync'">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleConfirm">确认同步</el-button>
            </span>
            <span class="dialog-footer" v-else>
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="handleReplace" :disabled="!targetRuleCode">确认替换</el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref, watch } from 'vue';
    import { PersonalRule } from '@smartdesk/common/types';
    import { userGroupApi } from '@smartdesk/common/api';
    import { Enumeration, LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';

    interface Props {
        visible: boolean;
        ruleCode?: string;
        syncData?: Record<PanelType, any[]>;
        mode?: 'view' | 'sync' | 'replace';
        ruleOptions?: PersonalRule[];
    }

    const props = withDefaults(defineProps<Props>(), {
        visible: false,
        ruleCode: '',
        syncData: () => ({
            Nav: [],
            PageSection: [],
            PageCellItem: [],
        }),
        mode: 'sync',
        ruleOptions: () => [],
    });

    // 定义emit
    const emit = defineEmits(['update:visible', 'update:mode', 'confirm', 'replace']);

    const feedback = useFeedback();

    // 面板类型定义
    type PanelType = 'Nav' | 'PageSection' | 'PageCellItem';

    // 面板配置
    const PANELS: Array<{
        type: PanelType;
        name: string;
        nameField: string;
        codeField: string;
    }> = [
        { type: 'Nav', name: '导航', nameField: 'title', codeField: 'code' },
        {
            type: 'PageSection',
            name: '页面楼层',
            nameField: 'name',
            codeField: 'localIndex',
        },
        {
            type: 'PageCellItem',
            name: '坑位元素',
            nameField: 'title',
            codeField: 'localIndex',
        },
    ];

    // 对话框显示控制
    const dialogVisible = ref(props.visible);

    // 目标策略 code
    const targetRuleCode = ref('');

    // 加载状态
    const loading = ref(false);

    // 对话框标题
    const dialogTitle = computed(() => {
        switch (props.mode) {
            case 'view':
                return '查看引用';
            case 'sync':
                return '同步引用';
            case 'replace':
                return '替换引用';
            default:
                return '';
        }
    });

    // 当前展开的面板
    const activeNames = ref<Array<string>>([]);

    // 选中的数据
    const selectedData = ref<Record<PanelType, any[]>>({
        Nav: [],
        PageSection: [],
        PageCellItem: [],
    });

    // 过滤后的所有数据
    const filteredData = computed(() => {
        return PANELS.reduce(
            (acc, { type }) => {
                acc[type] = (props.syncData?.[type] || []).filter(
                    (item) => item.ruleCode === props.ruleCode && item.delFlag !== -1
                );
                return acc;
            },
            {} as Record<PanelType, any[]>
        );
    });

    // 有数据的面板
    const panelsWithData = computed(() =>
        PANELS.filter(({ type }) => filteredData.value[type].length > 0).map(({ type }) => type)
    );

    // 获取选中数量
    const getSelectedCount = (type: PanelType) => {
        return selectedData.value[type]?.length || 0;
    };

    // 处理选择变化
    const handleSelectionChange = (val: any[], type: PanelType) => {
        selectedData.value[type] = val;
    };

    // 处理目标策略变化
    const handleTargetChange = (value: string) => {
        targetRuleCode.value = value;
        // 关闭详情展示
        showTargetRules.value = false;
    };

    // 处理替换
    const handleReplace = () => {
        // 检查是否有选中的数据
        const hasSelected = Object.values(selectedData.value).some((items) => items.length > 0);

        if (!hasSelected) {
            feedback.error('请至少选择一项数据进行替换');
            return;
        }

        if (!targetRuleCode.value) {
            feedback.error('请选择目标策略');
            return;
        }

        // 转换数据格式
        const transformedData = Object.entries(selectedData.value)
            .filter(([_, items]) => items.length > 0)
            .reduce((acc, [key, items]) => {
                // 提取code并join成字符串
                const idList = items.map((item) => item.id);
                return { ...acc, [key]: idList };
            }, {});

        emit('replace', {
            data: transformedData,
            targetCode: targetRuleCode.value,
        });
        handleClose();
    };

    // 处理关闭
    const handleClose = () => {
        dialogVisible.value = false;
        emit('update:visible', false);
        // 重置选中数据
        selectedData.value = {
            Nav: [],
            PageSection: [],
            PageCellItem: [],
        };
        // 重置目标策略
        showTargetRules.value = false;
        targetRuleCode.value = '';
        targetRuleDetail.value = {};
    };
    // 处理确认
    const handleConfirm = () => {
        // 检查是否有选中的数据
        const hasSelected = Object.values(selectedData.value).some((items) => items.length > 0);

        if (!hasSelected) {
            feedback.error('请至少选择一项数据进行同步');
            return;
        }

        // 转换数据格式
        const transformedData = Object.entries(selectedData.value)
            .filter(([_, items]) => items.length > 0)
            .reduce((acc, [key, items]) => {
                // 提取code并join成字符串
                const idList = items.map((item) => item.id);
                return { ...acc, [key]: idList };
            }, {});
        emit('confirm', transformedData);
        handleClose();
    };

    // 监听visible变化
    watch(
        () => props.visible,
        (val) => {
            dialogVisible.value = val;
            if (val) {
                // 只展开有数据的面板
                activeNames.value = panelsWithData.value;
            }
        }
    );

    // 获取标题
    const getTitle = (name: string, total: number) => {
        if (total === 0) {
            return `${name} (无)`;
        }
        if (props.mode === 'view') {
            return `${name} (${total})`;
        }
        const count = getSelectedCount(name as PanelType);
        return `${name} (${count}/${total})`;
    };

    // 切换到替换模式
    const handleSwitchToReplace = () => {
        emit('update:mode', 'replace');
    };

    const enumStore = useEnumStore();

    // 目标策略详情
    const targetRuleDetail = ref<any>({});
    const showTargetRules = ref(false);

    // 查看目标策略
    const handleViewTargetRule = () => {
        if (!targetRuleCode.value) return;

        // 从 ruleOptions 中找到对应的策略
        const targetRule = props.ruleOptions.find((rule) => rule.code === targetRuleCode.value);
        if (targetRule) {
            targetRuleDetail.value = targetRule;
            showTargetRules.value = true;
        }
    };

    // 用户分群
    const epgGroupsOption = ref<LabelValue[]>([]);
    // 用户分组
    const userGroupOption = ref<Enumeration[]>(enumStore.getEnumsByKey('userGroupEnum') || []);
    // 获取枚举
    const stbTypesOption = ref<Enumeration[]>(enumStore.getEnumsByKey('deviceTypeEnum') || []);
    // 区域
    const areaCodesOption = ref<Enumeration[]>(enumStore.getEnumsByKey('regionEnum') || []);
    // 会员等级
    const vipLevelsOption = ref<Enumeration[]>(enumStore.getEnumsByKey('membershipEnum') || []);

    // 获取用户分群选项
    const getUserGroupOption = async () => {
        try {
            const res = await userGroupApi.getUserGroupList();
            if (res.status === 200) {
                epgGroupsOption.value = res.resultSet.map((item: any) => ({
                    value: item.code,
                    label: item.name,
                }));
            }
        } catch (error) {
            console.error('获取用户分群失败:', error);
        }
    };

    // 获取名称的方法
    const getEpgGroupNames = (codes: string[]) => {
        if (!codes?.length) return '';
        return codes.map((code) => epgGroupsOption.value.find((item) => item.value == code)?.label || code).join(', ');
    };

    const getStbTypeNames = (codes: string[]) => {
        if (!codes?.length) return '';
        return codes
            .map((code) => {
                // 遍历一级枚举
                for (const group of stbTypesOption.value) {
                    // 在二级枚举中查找
                    const found = group.children?.find((child) => child.code === code);
                    if (found) {
                        return found.name;
                    }
                }
                return code;
            })
            .join(', ');
    };

    const getSubAreaNames = (codes: string[]) => {
        if (!codes?.length) return '';
        return codes
            .map((code) => {
                const found = areaCodesOption.value.find((item) => item.code === code);
                return found?.name || code;
            })
            .join(', ');
    };

    const getAreaNames = (codes: string[]) => {
        if (!codes?.length) return '';
        return codes
            .map((code) => {
                // 遍历一级枚举
                for (const group of areaCodesOption.value) {
                    // 在二级枚举中查找
                    const found = group.children?.find((child) => child.code === code);
                    if (found) {
                        return found.name;
                    }
                }
                return code;
            })
            .join(', ');
    };

    const getVipLevelNames = (codes: string[]) => {
        if (!codes?.length) return '';
        return codes.map((code) => vipLevelsOption.value.find((item) => item.code == code)?.name || code).join(', ');
    };

    const getUserGroupNames = (codes: string[]) => {
        if (!codes?.length) return '';
        return codes
            .map((code) => {
                // 遍历一级枚举
                for (const group of userGroupOption.value) {
                    // 在二级枚举中查找
                    const found = group.children?.find((child) => child.code === code);
                    if (found) {
                        return found.name;
                    }
                }
                return code;
            })
            .join(', ');
    };

    onMounted(() => {
        getUserGroupOption();
    });
</script>

<style scoped>
    .sync-dialog-content {
        max-height: 600px;
        overflow-y: auto;
    }

    .collapse-header {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .collapse-header .title {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
    }

    .collapse-header .count {
        font-size: 14px;
        color: #64748b;
    }

    :deep(.el-collapse) {
        border: none;
    }

    :deep(.el-collapse-item__header) {
        font-size: 16px;
        font-weight: 600;
        padding: 16px 20px;
        background-color: #eef2f6;
        border-bottom: 1px solid #e2e8f0;
        color: #1e293b;
    }

    :deep(.el-collapse-item.is-disabled .el-collapse-item__header) {
        color: #909399;
        cursor: not-allowed;
        background-color: #f5f7fa;
    }

    :deep(.el-collapse-item__content) {
        padding: 0;
    }

    :deep(.el-table) {
        --el-table-border-color: #e2e8f0;
    }

    :deep(.el-table__header th) {
        background-color: #f8fafc !important;
        font-weight: 500;
        color: #475569;
        padding: 12px !important;
        border-bottom: 1px solid #e2e8f0;
        font-size: 14px;
    }

    :deep(.el-table__row) {
        background-color: white;
    }

    :deep(.el-table__row:hover > td) {
        background-color: #f1f5f9 !important;
    }

    :deep(.el-table__cell) {
        padding: 12px !important;
        color: #475569;
    }

    .target-rule-select {
        margin-bottom: 16px;
        padding: 16px;
        background-color: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
    }

    .select-container {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .select-label {
        font-size: 14px;
        font-weight: 500;
        color: #1e293b;
        white-space: nowrap;
    }

    .target-select {
        flex: 1;
    }

    .action-bar {
        margin-bottom: 16px;
        padding: 0 16px;
        display: flex;
        justify-content: flex-end;
    }

    .target-rule-detail {
        margin-bottom: 16px;
        padding: 16px;
        background-color: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
    }

    .detail-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
    }

    .detail-title {
        font-size: 16px;
        font-weight: 500;
        color: #1e293b;
    }

    .detail-content {
        background-color: white;
        border-radius: 4px;
        padding: 16px;
    }

    :deep(.el-descriptions__label) {
        width: 120px;
        color: #475569;
    }

    :deep(.el-descriptions__content) {
        color: #1e293b;
    }
</style>
