<template>
    <div class="select-param">
        <el-table :data="filteredVersions" border stripe style="width: 100%">
            <!-- 一级枚举 -->
            <el-table-column prop="name" label="分类" width="200">
                <template #header>
                    <div class="category-header">分类</div>
                </template>
                <template #default="{ row }">
                    <div class="category-cell">
                        <el-checkbox
                            :model-value="selectedCategories.includes(row.code)"
                            @change="(val: any) => handleCategorySelectChange(val, row)">
                            {{ row.name }}
                        </el-checkbox>
                    </div>
                </template>
            </el-table-column>

            <!-- 二级枚举 -->
            <el-table-column prop="children" label="选项">
                <template #default="{ row }">
                    <div class="options-cell">
                        <el-checkbox-group v-model="selectedVersions" @change="() => handleChildChange(row)">
                            <el-tooltip
                                v-for="item in row.children"
                                :key="item.code"
                                :content="item.name"
                                placement="top"
                                :show-after="500"
                                :hide-after="200"
                                effect="dark">
                                <el-checkbox :value="item.code" :label="item.name">
                                    {{ item.name }}
                                </el-checkbox>
                            </el-tooltip>
                        </el-checkbox-group>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup lang="ts">
    import { computed, ref, watch } from 'vue';
    import { Enumeration } from '@chances/portal_common_core';

    interface Props {
        modelValue?: {
            categories: string[];
            versions: string[];
        };
        enumOption?: Enumeration[];
        keyword?: string;
    }

    const props = withDefaults(defineProps<Props>(), {
        modelValue: () => ({
            categories: [],
            versions: [],
        }),
        keyword: '',
    });

    const emit = defineEmits(['update:modelValue']);

    const selectedCategories = ref<string[]>(props.modelValue.categories || []);
    const selectedVersions = ref<string[]>(props.modelValue.versions || []);

    // 获取带 children 的版本数据
    const versionData = computed(() => {
        const data = props.enumOption || [];
        return data.map((item) => ({
            ...item,
            children: item.children || [],
        }));
    });

    // 关键字过滤
    const filteredVersions = computed(() => {
        if (!props.keyword) return versionData.value;

        const searchText = props.keyword.toLowerCase();
        return versionData.value
            .map((category) => ({
                ...category,
                children: category.children.filter(
                    (item) =>
                        item.name.toLowerCase().includes(searchText) || item.code.toLowerCase().includes(searchText)
                ),
            }))
            .filter((category) => category.children.length > 0);
    });

    // 父级勾选操作（不联动子级）
    const handleCategorySelectChange = (checked: boolean, category: Enumeration) => {
        const code = category.code;
        if (checked) {
            if (!selectedCategories.value.includes(code)) {
                selectedCategories.value.push(code);
            }
        } else {
            selectedCategories.value = selectedCategories.value.filter((c) => c !== code);
        }
        emitValue();
    };

    // 子级勾选变化 → 判断是否要勾选父级
    const handleChildChange = (category: Enumeration) => {
        const codes = category.children.map((c) => c.code);
        const hasSelected = codes.some((code) => selectedVersions.value.includes(code));

        if (hasSelected) {
            if (!selectedCategories.value.includes(category.code)) {
                selectedCategories.value.push(category.code);
            }
        } else {
            selectedCategories.value = selectedCategories.value.filter((c) => c !== category.code);
        }
        emitValue();
    };

    watch(
        () => [props.modelValue?.categories, props.modelValue?.versions],
        ([newCategories, newVersions]) => {
            selectedCategories.value = newCategories || [];
            selectedVersions.value = newVersions || [];
        },
        { immediate: true }
    );

    const emitValue = () => {
        emit('update:modelValue', {
            categories: selectedCategories.value,
            versions: selectedVersions.value,
        });
    };

    // 暴露可操作字段
    defineExpose({
        selectedCategories,
        selectedVersions,
    });
</script>
<style scoped>
    .select-param {
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    }

    .category-cell {
        padding: 16px;
        transition: background-color 0.2s ease;
    }

    .category-cell :deep(.el-checkbox__label) {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
    }

    .options-cell {
        padding: 16px;
        min-height: 48px;
        transition: background-color 0.2s ease;
    }

    :deep(.el-checkbox-group) {
        display: flex;
        flex-wrap: wrap;
        gap: 16px 32px;
    }

    :deep(.el-checkbox) {
        margin-right: 0;
        margin-bottom: 0;
        transition: all 0.2s ease;
        width: 320px !important;
    }

    :deep(.el-checkbox__label) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #475569;
        font-size: 14px;
        transition: color 0.2s ease;
        max-width: 280px; /* 留出一些空间给checkbox本身 */
    }

    :deep(.el-checkbox:hover) {
        transform: translateY(-1px);
    }

    :deep(.el-table) {
        --el-table-border-color: #e2e8f0;
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #e2e8f0;
        box-shadow:
            0 1px 3px 0 rgba(0, 0, 0, 0.1),
            0 1px 2px -1px rgba(0, 0, 0, 0.1);
    }

    :deep(.el-table__header th) {
        background-color: #f8fafc !important;
        font-weight: 600;
        color: #1e293b;
        padding: 16px !important;
        border-bottom: 1px solid #e2e8f0;
        transition: background-color 0.2s ease;
    }

    :deep(.el-table__row) {
        background-color: white;
        transition: background-color 0.2s ease;
    }

    :deep(.el-table__row.el-table__row--striped) {
        background-color: #f8fafc;
    }

    :deep(.el-table__row:hover > td) {
        background-color: #f1f5f9 !important;
        transform: translateZ(0);
    }

    :deep(.el-table__cell) {
        padding: 0 !important;
        color: #475569;
        transition: all 0.2s ease;
    }

    :deep(.el-table__cell.is-leaf) {
        border-right: none;
    }

    :deep(.el-table__column:last-child .el-table__cell) {
        border-right: none;
    }

    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
        transition: all 0.2s ease;
    }

    :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
        transition: all 0.2s ease;
    }

    :deep(.el-checkbox__inner) {
        transition: all 0.2s ease;
    }

    :deep(.el-checkbox__inner:hover) {
        border-color: var(--el-color-primary);
        transform: scale(1.05);
    }

    :deep(.el-checkbox.is-checked .el-checkbox__label) {
        color: var(--el-color-primary);
    }

    .category-header {
        display: flex;
        align-items: center;
        padding: 0 16px;
    }

    :deep(.category-header .el-checkbox__label) {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
    }
</style>
