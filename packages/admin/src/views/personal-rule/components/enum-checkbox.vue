<template>
    <div class="select-param">
        <el-table :data="[{ options: filteredOptions }]" border stripe style="width: 100%">
            <!-- 第一列: 全选 -->
            <el-table-column width="200">
                <template #header>
                    <div class="header-cell">
                        <el-checkbox v-model="allSelected" :indeterminate="isIndeterminate" @change="handleCheckAll">
                            全选
                        </el-checkbox>
                    </div>
                </template>
            </el-table-column>

            <!-- 第二列: 选项列表 -->
            <el-table-column label="选项">
                <template #default="{ row }">
                    <div class="options-cell">
                        <el-checkbox-group v-model="selected" @change="handleSelectedChange">
                            <el-tooltip
                                v-for="item in row.options"
                                :key="item.code"
                                :content="item.name"
                                placement="top"
                                :show-after="500"
                                :hide-after="200"
                                effect="dark">
                                <el-checkbox :value="item.code" :label="item.name">
                                    {{ item.name }}
                                </el-checkbox>
                            </el-tooltip>
                        </el-checkbox-group>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup lang="ts">
    import { Enumeration } from '@chances/portal_common_core';
    import { computed, ref, watch } from 'vue';

    const props = defineProps<{
        title: string;
        enumOption: Enumeration[];
        modelValue: string[];
        keyword?: string;
    }>();

    const emit = defineEmits(['update:modelValue']);

    const selected = ref<string[]>(props.modelValue);
    const allSelected = ref(false);
    const isIndeterminate = ref(false);

    // 表格数据
    const tableData = computed(() => [
        {
            // 空对象用于表格渲染
        },
    ]);

    // 过滤后的选项
    const filteredOptions = computed(() => {
        if (!props.keyword) return props.enumOption;
        return props.enumOption.filter((item) => item.name.toLowerCase().includes(props.keyword!.toLowerCase()));
    });

    // 全选处理
    const handleCheckAll = (val: boolean) => {
        // 如果没有可选项,则不进行操作
        if (filteredOptions.value.length === 0) {
            allSelected.value = false;
            isIndeterminate.value = false;
            return;
        }
        const newSelected = val ? filteredOptions.value.map((item) => item.code) : [];
        selected.value = newSelected;
        isIndeterminate.value = false;
        emit('update:modelValue', newSelected);
    };

    // 选中变化处理
    const handleSelectedChange = (value: string[]) => {
        // 如果没有可选项,重置状态
        if (filteredOptions.value.length === 0) {
            allSelected.value = false;
            isIndeterminate.value = false;
            return;
        }
        const checkedCount = value.length;
        allSelected.value = checkedCount > 0 && checkedCount === filteredOptions.value.length;
        isIndeterminate.value = checkedCount > 0 && checkedCount < filteredOptions.value.length;
        emit('update:modelValue', value);
    };

    // 更新选中状态
    const updateCheckState = () => {
        // 如果没有可选项,重置状态
        if (filteredOptions.value.length === 0) {
            allSelected.value = false;
            isIndeterminate.value = false;
            return;
        }
        const checkedCount = selected.value.filter((code) =>
            filteredOptions.value.some((item) => item.code === code)
        ).length;
        allSelected.value = checkedCount > 0 && checkedCount === filteredOptions.value.length;
        isIndeterminate.value = checkedCount > 0 && checkedCount < filteredOptions.value.length;
    };

    // 监听modelValue变化
    watch(
        () => props.modelValue,
        (newVal) => {
            selected.value = newVal;
            updateCheckState();
        },
        { immediate: true }
    );

    // 监听过滤选项变化
    watch(filteredOptions, () => {
        updateCheckState();
    });
</script>

<style scoped>
    .select-param {
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    }

    .header-cell {
        padding: 0 16px;
        height: 100%;
        display: flex;
        align-items: center;
    }

    .options-cell {
        padding: 16px;
        min-height: 48px;
        transition: background-color 0.2s ease;
    }

    :deep(.el-checkbox-group) {
        display: flex;
        flex-wrap: wrap;
        gap: 16px 32px;
    }

    :deep(.el-checkbox) {
        margin-right: 0;
        margin-bottom: 0;
        transition: all 0.2s ease;
        width: 320px !important;
    }

    :deep(.el-checkbox__label) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #475569;
        font-size: 14px;
        transition: color 0.2s ease;
        max-width: 280px;
    }

    :deep(.el-table) {
        --el-table-border-color: #e2e8f0;
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #e2e8f0;
        box-shadow:
            0 1px 3px 0 rgba(0, 0, 0, 0.1),
            0 1px 2px -1px rgba(0, 0, 0, 0.1);
    }

    :deep(.el-table__header th) {
        background-color: #f8fafc !important;
        font-weight: 600;
        color: #1e293b;
        padding: 16px !important;
        border-bottom: 1px solid #e2e8f0;
        transition: background-color 0.2s ease;
    }

    :deep(.el-table__header th:first-child .el-checkbox__label) {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
    }

    :deep(.el-table__row) {
        background-color: white;
        transition: background-color 0.2s ease;
    }

    :deep(.el-table__row:hover > td) {
        background-color: #f1f5f9 !important;
        transform: translateZ(0);
    }

    :deep(.el-table__cell) {
        padding: 0 !important;
        color: #475569;
        transition: all 0.2s ease;
    }

    :deep(.el-table__cell.is-leaf) {
        border-right: none;
    }

    :deep(.el-table__column:last-child .el-table__cell) {
        border-right: none;
    }

    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
        transition: all 0.2s ease;
    }

    :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
        transition: all 0.2s ease;
    }

    :deep(.el-checkbox__inner) {
        transition: all 0.2s ease;
    }

    :deep(.el-checkbox__inner:hover) {
        border-color: var(--el-color-primary);
        transform: scale(1.05);
    }

    :deep(.el-checkbox.is-checked .el-checkbox__label) {
        color: var(--el-color-primary);
    }
</style>
