<template>
    <div>
        <el-drawer v-model="visible" :size="1600" @close="handleCancel">
            <template #header>
                <span class="drawer-title">
                    {{ isEdit ? '编辑策略' : '新增策略' }}
                </span>
            </template>
            <div class="drawer-content">
                <!-- 左侧锚点导航 -->
                <div class="anchor-nav">
                    <span class="anchor-nav-span" v-if="paramSelected.length">已显示</span>
                    <span class="anchor-nav-span" v-else>暂无</span>
                    <div
                        v-for="item in paramList"
                        :key="item.value"
                        v-show="paramSelected.includes(item.value)"
                        class="anchor-item"
                        :class="{ active: activeNames.includes(item.value) }"
                        @click="scrollToTarget(item.value)">
                        {{ item.label }}
                        <span v-if="getSelectedCount(item.value)" class="count">
                            ({{ getSelectedCount(item.value) }})
                        </span>
                    </div>
                </div>

                <!-- 右侧主内容区 -->
                <div class="main-content">
                    <el-card class="rule-card">
                        <el-form
                            ref="formRef"
                            :model="form"
                            :rules="rules"
                            label-width="120px"
                            label-suffix=":"
                            class="rule-form">
                            <el-form-item label="策略名称" prop="name">
                                <el-input v-model="form.name" placeholder="请输入策略名称" />
                            </el-form-item>
                            <el-form-item label="组织" prop="orgId">
                                <dimension-selector
                                    v-model="form.orgId"
                                    placeholder="请选择组织"
                                    :data="orgTree"
                                    label-key="name"
                                    value-key="id"
                                    width="370" />
                            </el-form-item>
                            <el-form-item label="用户尾号奇偶">
                                <el-select v-model="form.userIdType" placeholder="请选择用户尾号奇偶" clearable>
                                    <el-option
                                        v-for="item in userIdTypeOption"
                                        :key="item.code"
                                        :label="item.name"
                                        :value="item.code" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="用户分群">
                                <el-select
                                    v-model="form.epgGroups"
                                    filterable
                                    multiple
                                    clearable
                                    placeholder="请选择用户分群">
                                    <el-option
                                        v-for="item in epgGroupsOption"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value" />
                                </el-select>
                            </el-form-item>
                        </el-form>
                    </el-card>
                    <el-card class="rule-card">
                        <div>
                            <el-checkbox-group v-model="paramSelected">
                                <el-checkbox
                                    v-for="item in paramList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value" />
                            </el-checkbox-group>
                        </div>
                        <el-collapse v-model="activeNames">
                            <el-collapse-item
                                v-for="item in paramList"
                                :key="item.value"
                                v-show="paramSelected.includes(item.value)"
                                :id="item.value"
                                :name="item.value">
                                <template #header>
                                    <div class="collapse-header">
                                        <span class="collapse-title">{{ item.label }}</span>
                                        <div v-show="activeNames.includes(item.value)" class="search-box" @click.stop>
                                            <el-input
                                                v-model="searchKeywords[item.value]"
                                                placeholder="请输入关键字搜索"
                                                clearable
                                                @keyup.enter="handleSearch"
                                                @click.stop>
                                                <template #prefix>
                                                    <el-icon>
                                                        <Search />
                                                    </el-icon>
                                                </template>
                                            </el-input>
                                        </div>
                                        <span class="selected-count" v-if="getSelectedCount(item.value)">
                                            已选
                                            {{ getSelectedCount(item.value) }}
                                            项
                                        </span>
                                    </div>
                                </template>
                                <template v-if="item.value === 'vipLevels'">
                                    <EnumCheckbox
                                        :model-value="form.vipLevels || []"
                                        :title="item.label"
                                        :enum-option="vipLevelsOption"
                                        :keyword="searchKeywords[item.value]"
                                        @update:model-value="handleVipLevelsChange" />
                                </template>
                                <template v-else-if="item.value === 'userGroup'">
                                    <SelectParamChildren
                                        :model-value="form.userGroup || []"
                                        :title="item.label"
                                        :enum-option="userGroupOption"
                                        :keyword="searchKeywords[item.value]"
                                        @update:model-value="handleUserGroupChange" />
                                </template>
                                <template v-else-if="item.value === 'apkVersion'">
                                    <SelectParamChildren
                                        :model-value="form.versionName || []"
                                        :title="item.label"
                                        :enum-option="apkVersionOption"
                                        :keyword="searchKeywords[item.value]"
                                        @update:model-value="handleVersionsChange" />
                                </template>
                                <template v-else-if="item.value === 'stbType'">
                                    <SelectParamChildren
                                        :model-value="form.stbType || []"
                                        :title="item.label"
                                        :enum-option="stbTypesOption"
                                        :keyword="searchKeywords[item.value]"
                                        @update:model-value="handleStbTypesChange" />
                                </template>
                                <template v-else-if="item.value === 'areaCode'">
                                    <AreaSelect
                                        :model-value="cityCodes"
                                        :title="item.label"
                                        :enum-option="areaCodesOption"
                                        :keyword="searchKeywords[item.value]"
                                        @update:model-value="handleCityCodesChange" />
                                </template>
                            </el-collapse-item>
                        </el-collapse>
                    </el-card>
                </div>
            </div>
            <template #footer>
                <div class="dialog-footer">
                    <el-button @click="handleCancel">取消</el-button>
                    <el-button type="primary" @click="handleConfirm">保存 </el-button>
                    <el-button type="success" v-if="isEdit" @click="handleConfirmSync">保存并同步引用 </el-button>
                </div>
            </template>
        </el-drawer>
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import type { FormInstance } from 'element-plus';
    import { dimensionApi, userGroupApi } from '@smartdesk/common/api';
    import { Enumeration, LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { Dimension, PersonalRule } from '@smartdesk/common/types';
    import SelectParamChildren from './select-param-children.vue';
    import EnumCheckbox from './enum-checkbox.vue';
    import { useFeedback } from '@smartdesk/common/composables';
    import AreaSelect from './area-select.vue';

    // 定义props
    const props = defineProps<{
        visible: boolean;
        isEdit?: boolean;
        currentData?: any;
    }>();

    // 定义emit
    const emit = defineEmits(['update:visible', 'save', 'sync']);

    const enumStore = useEnumStore();
    const feedback = useFeedback();

    const activeNames = ref<string[]>([]);

    const areaCodesOption = ref<Enumeration[]>(enumStore.getEnumsByKey('regionEnum') || []);

    // apk版本号
    const apkVersionOption = ref<Enumeration[]>(enumStore.getEnumsByKey('apkVersionEnum') || []);

    // 用户分组
    const userGroupOption = ref<Enumeration[]>(enumStore.getEnumsByKey('userGroupEnum') || []);

    // 获取枚举
    const stbTypesOption = ref<Enumeration[]>(enumStore.getEnumsByKey('deviceTypeEnum') || []);
    // 地市
    const subAreaCodesOption = ref<Enumeration[]>(enumStore.getEnumsByKey('regionEnum') || []);
    // 用户分群
    const epgGroupsOption = ref<LabelValue[]>([]);
    // 会员等级
    const vipLevelsOption = ref<Enumeration[]>(enumStore.getEnumsByKey('membershipEnum') || []);
    // 用户尾号奇偶
    const userIdTypeOption = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('userIdType') || []);

    const paramSelected = ref<string[]>([]);
    const paramList = ref<LabelValue[]>([
        {
            label: '会员等级',
            value: 'vipLevels',
        },
        {
            label: '用户分组',
            value: 'userGroup',
        },
        {
            label: 'apk版本号',
            value: 'apkVersion',
        },
        {
            label: '机顶盒型号',
            value: 'stbType',
        },
        {
            label: '区域',
            value: 'areaCode',
        },
    ]);

    const personalRule = ref<PersonalRule>({} as PersonalRule);

    interface RuleForm {
        orgId: number;
        // 名称
        name: string;
        stbType: string[];
        versionName: string[];
        // 用户分组
        userGroup: string[];
        // 用户分群
        epgGroups: string[];
        // 会员等级
        vipLevels: string[];
        // 地级市
        subAreaCode: string[];
        // 区域
        areaCode: string[];
        // 用户尾号奇偶
        userIdType: number | undefined;
    }

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 表单数据
    const form = ref<RuleForm>({} as RuleForm);

    const initialForm: RuleForm = {} as RuleForm;

    // 表单引用
    const formRef = ref<FormInstance>();

    // 控制抽屉显示
    const visible = ref(props.visible);

    // 地市与区域
    const cityCodes = ref<any>({
        categories: [],
        versions: [],
    });

    // 表单校验规则
    const rules = {
        name: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
    };

    // 取消
    const handleCancel = () => {
        activeNames.value = [];
        paramSelected.value = [];
        // 重置表单验证及数据
        formRef.value?.resetFields();
        // 手动重置表单数据到初始状态
        Object.assign(form.value, initialForm);
        // 重置搜索关键字
        Object.keys(searchKeywords.value).forEach((key) => {
            searchKeywords.value[key] = '';
        });
        // 重置地区和区域
        cityCodes.value = {
            categories: [],
            versions: [],
        };
        emit('update:visible', false);
    };

    // 确认
    const handleConfirm = async () => {
        if (!formRef.value) return;

        try {
            const valid = await formRef.value.validate();
            if (valid) {
                // 拼装数据
                // 动态构建 rule 对象，过滤空值
                const ruleJson = Object.entries({
                    epgGroups: form.value.epgGroups,
                    areaCode: form.value.areaCode,
                    stbType: form.value.stbType,
                    subAreaCode: form.value.subAreaCode,
                    userGroup: form.value.userGroup,
                    versionName: form.value.versionName,
                    vipLevels: form.value.vipLevels,
                    userIdType: form.value.userIdType,
                }).reduce((acc: any, [key, value]) => {
                    // 自定义空值过滤逻辑
                    const isEmpty =
                        value === null ||
                        value === undefined ||
                        (Array.isArray(value) && value.length === 0) ||
                        (typeof value === 'object' && Object.keys(value).length === 0);
                    if (!isEmpty) {
                        acc[key] = value;
                    }
                    return acc;
                }, {});
                personalRule.value.rules = ruleJson;
                personalRule.value.name = form.value.name;
                personalRule.value.scope = 1;
                personalRule.value.orgId = form.value.orgId;

                emit('save', personalRule.value);
                handleCancel();
                return true;
            } else {
                feedback.error('请填写完整信息');
                return false;
            }
        } catch (error) {
            feedback.error('表单验证失败');
            return false;
        }
    };

    // 保存并同步引用
    const handleConfirmSync = async () => {
        try {
            const saveSuccess = await handleConfirm();
            if (saveSuccess) {
                emit('sync');
            }
        } catch (error) {
            feedback.error('保存失败');
        }
    };

    // 获取用户分群
    const getUserGroupOption = async () => {
        const res = await userGroupApi.getUserGroupList();
        if (res.status === 200) {
            epgGroupsOption.value = res.resultSet.map((item: any) => {
                return {
                    value: item.code,
                    label: item.name,
                };
            });
        }
    };

    // apk版本号
    const handleVersionsChange = (value: string[]) => {
        form.value.versionName = value;
    };
    // 机顶盒型号
    const handleStbTypesChange = (value: string[]) => {
        form.value.stbType = value;
    };
    // 区域
    const handleAreaCodesChange = (value: string[]) => {
        form.value.areaCode = value;
    };
    // 用户分组
    const handleUserGroupChange = (value: string[]) => {
        form.value.userGroup = value;
    };
    // 会员等级
    const handleVipLevelsChange = (value: string[]) => {
        form.value.vipLevels = value;
    };

    // 获取选中数量
    const getSelectedCount = (type: string) => {
        switch (type) {
            case 'vipLevels':
                return form.value.vipLevels?.length || 0;
            case 'userGroup':
                return form.value.userGroup?.length || 0;
            case 'apkVersion':
                return form.value.versionName?.length || 0;
            case 'stbType':
                return form.value.stbType?.length || 0;
            case 'subAreaCode':
                return form.value.subAreaCode?.length || 0;
            case 'areaCode':
                return form.value.areaCode?.length || 0;
            default:
                return 0;
        }
    };

    // 滚动到目标位置
    const scrollToTarget = (id: string) => {
        const element = document.getElementById(id);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
            // 展开对应的collapse
            if (!activeNames.value.includes(id)) {
                activeNames.value = [...activeNames.value, id];
            }
        }
    };

    // 搜索关键字
    const searchKeywords = ref<Record<string, string>>({});

    // 搜索处理
    const handleSearch = () => {
        // 可以添加额外的搜索逻辑
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    const handleCityCodesChange = (data: any) => {
        form.value.subAreaCode = data.categories;
        form.value.areaCode = data.versions;
    };

    // 监听paramSelected变化,展开新选中的项
    watch(paramSelected, (newVal, oldVal) => {
        if (!oldVal) return;
        // 找出新增的选项
        const newlySelected = newVal.filter((item) => !oldVal.includes(item));
        if (newlySelected.length > 0) {
            // 将新选中的项添加到activeNames中
            activeNames.value = [...activeNames.value, ...newlySelected];
        }
    });

    // 监听visible变化
    watch(
        () => props.visible,
        (val) => {
            visible.value = val;
            if (val && props.isEdit && props.currentData) {
                // 解析 currentData
                form.value = {
                    ...props.currentData.rules,
                };
                form.value.name = props.currentData.name;
                form.value.orgId = props.currentData.orgId;

                // 根据form中的数据自动选中paramSelected
                const selectedParams: string[] = [];

                // 检查各个数组是否有值
                if (props.currentData.rules.vipLevels?.length) {
                    selectedParams.push('vipLevels');
                }
                if (props.currentData.rules.userGroup?.length) {
                    selectedParams.push('userGroup');
                }
                if (props.currentData.rules.versionName?.length) {
                    selectedParams.push('apkVersion');
                }
                if (props.currentData.rules.stbType?.length) {
                    selectedParams.push('stbType');
                }
                if (props.currentData.rules.subAreaCode?.length) {
                    selectedParams.push('subAreaCode');
                }
                if (props.currentData.rules.areaCode?.length) {
                    selectedParams.push('areaCode');
                }

                // 更新paramSelected
                paramSelected.value = selectedParams;
                cityCodes.value.categories = form.value.subAreaCode;
                cityCodes.value.versions = form.value.areaCode;
            }
        }
    );

    onMounted(() => {
        getUserGroupOption();
        getOrgTree();
    });
</script>

<style scoped>
    .drawer-content {
        display: flex;
        height: 100%;
        border-radius: 10px;
    }

    .anchor-nav {
        position: sticky;
        top: 0;
        width: 200px;
        height: fit-content;
        min-height: 800px;
        padding: 20px 0;
        border-right: 1px solid var(--el-border-color-light);
        background: white;
    }

    .anchor-item {
        padding: 8px 16px;
        cursor: pointer;
        font-size: 14px;
        color: var(--el-text-color-regular);
        transition: all 0.3s;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: right;
    }

    .anchor-nav-span {
        display: block;
        text-align: center;
        font-size: 16px;
        font-weight: bold;
        color: #409eff;
        margin: 10px 0;
    }

    .anchor-item:hover {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
    }

    .anchor-item.active {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
        border-right: 2px solid var(--el-color-primary);
    }

    .anchor-item .count {
        margin-left: 4px;
        font-size: 12px;
        color: var(--el-text-color-secondary);
    }

    .main-content {
        flex: 1;
        padding: 0 20px;
        overflow-y: auto;
    }

    .rule-form {
        padding: 20px;
    }

    .checkbox-line {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .versions-container {
        margin-left: 20px;
    }

    :deep(.el-checkbox-group) {
        display: flex;
        flex-wrap: wrap;
        gap: 12px 20px; /* 垂直间距12px，水平间距20px */
        width: 100%;
    }

    :deep(.el-checkbox) {
        margin-right: 0 !important;
        margin-bottom: 0 !important;
        width: 200px; /* 固定宽度 */
        flex: 0 0 auto;
        display: flex;
        align-items: center;
    }

    :deep(.el-checkbox__label) {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .collapse-header {
        display: flex;
        align-items: center;
        gap: 16px;
        width: 100%;
    }

    .collapse-title {
        font-size: 16px;
        font-weight: 500;
        color: var(--el-text-color-primary);
        flex-shrink: 0;
    }

    .search-box {
        flex: 1;
        max-width: 300px;
        margin-right: auto;
    }

    .selected-count {
        font-size: 14px;
        color: var(--el-text-color-secondary);
    }

    :deep(.el-collapse-item__header) {
        font-size: inherit;
        padding: 16px 20px;
        background-color: #f8fafc;
        border-bottom: 1px solid #e4e7ed;
    }

    :deep(.el-collapse-item__content) {
        padding: 0;
    }

    :deep(.el-collapse-item__arrow) {
        margin-left: 8px;
    }

    :deep(.el-input__wrapper) {
        background-color: white;
    }

    .rule-card {
        margin-bottom: 10px;
        border-radius: 10px;
    }

    .drawer-title {
        font-size: 24px;
        font-weight: bold;
    }
</style>
