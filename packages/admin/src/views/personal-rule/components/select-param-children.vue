<template>
    <div class="select-param">
        <el-table :data="filteredVersions" border stripe style="width: 100%">
            <!-- 第一列: 一级枚举 -->
            <el-table-column prop="name" label="分类" width="200">
                <template #header>
                    <div class="category-header">
                        <el-checkbox
                            :model-value="globalCheckStatus.checkAll"
                            :indeterminate="globalCheckStatus.isIndeterminate"
                            @change="handleGlobalCheckAll">
                            分类
                        </el-checkbox>
                    </div>
                </template>
                <template #default="{ row }">
                    <div class="category-cell">
                        <el-checkbox
                            :model-value="getCategoryCheckStatus(row).checkAll"
                            :indeterminate="getCategoryCheckStatus(row).isIndeterminate"
                            @change="(val: any) => handleCategoryCheckAll(val, row)">
                            {{ row.name }}
                        </el-checkbox>
                    </div>
                </template>
            </el-table-column>

            <!-- 第二列: 二级枚举 -->
            <el-table-column prop="children" label="选项">
                <template #default="{ row }">
                    <div class="options-cell">
                        <el-checkbox-group v-model="selectedVersions" @change="handleVersionsChange">
                            <el-tooltip
                                v-for="item in row.children"
                                :key="item.code"
                                :content="item.name"
                                placement="top"
                                :show-after="500"
                                :hide-after="200"
                                effect="dark">
                                <el-checkbox :value="item.code" :label="item.name">
                                    {{ item.name }}
                                </el-checkbox>
                            </el-tooltip>
                        </el-checkbox-group>
                    </div>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup lang="ts">
    import { computed, ref, watch } from 'vue';
    import { Enumeration } from '@chances/portal_common_core';

    interface Props {
        modelValue?: string[];
        title?: string;
        enumOption?: Enumeration[];
        keyword?: string;
    }

    const props = withDefaults(defineProps<Props>(), {
        modelValue: () => [],
        keyword: '',
    });

    const emit = defineEmits(['update:modelValue']);

    const selectedVersions = ref<string[]>(props.modelValue);

    // 获取版本数据
    const versionData = computed(() => {
        const data = props.enumOption || [];
        return data.map((item) => ({
            ...item,
            children: item.children || [],
        }));
    });

    // 根据关键字过滤版本
    const filteredVersions = computed(() => {
        if (!props.keyword) return versionData.value;

        const searchText = props.keyword.toLowerCase();
        return versionData.value
            .map((category) => ({
                ...category,
                children: category.children.filter(
                    (item) =>
                        item.name.toLowerCase().includes(searchText) || item.code.toLowerCase().includes(searchText)
                ),
            }))
            .filter((category) => category.children.length > 0);
    });

    // 获取所有可选项的code
    const allVersionCodes = computed(() => {
        return filteredVersions.value.reduce((codes, category) => {
            return [...codes, ...(category.children?.map((item) => item.code) || [])];
        }, [] as string[]);
    });

    // 获取全局选中状态
    const globalCheckStatus = computed(() => {
        const allCodes = allVersionCodes.value;
        if (allCodes.length === 0) {
            return { checkAll: false, isIndeterminate: false };
        }

        const selectedCount = allCodes.filter((code) => selectedVersions.value.includes(code)).length;
        return {
            checkAll: selectedCount === allCodes.length,
            isIndeterminate: selectedCount > 0 && selectedCount < allCodes.length,
        };
    });

    // 获取分类的选中状态
    const getCategoryCheckStatus = (category: Enumeration) => {
        if (!category.children?.length) {
            return { checkAll: false, isIndeterminate: false };
        }

        const categoryVersions = category.children.map((item) => item.code);
        const selectedCount = categoryVersions.filter((v) => selectedVersions.value.includes(v)).length;

        return {
            checkAll: selectedCount === categoryVersions.length,
            isIndeterminate: selectedCount > 0 && selectedCount < categoryVersions.length,
        };
    };

    // 处理分类全选/取消全选
    const handleCategoryCheckAll = (checked: boolean, category: Enumeration) => {
        const categoryVersions = category.children?.map((item) => item.code) || [];

        if (checked) {
            // 添加当前分类的所有版本到选中列表
            selectedVersions.value = Array.from(new Set([...selectedVersions.value, ...categoryVersions]));
        } else {
            // 从选中列表中移除当前分类的所有版本
            selectedVersions.value = selectedVersions.value.filter((v) => !categoryVersions.includes(v));
        }

        emit('update:modelValue', selectedVersions.value);
    };

    // 处理版本选择变化
    const handleVersionsChange = (value: string[]) => {
        selectedVersions.value = value;
        emit('update:modelValue', value);
    };

    // 处理全局全选/取消全选
    const handleGlobalCheckAll = (checked: boolean) => {
        const allCodes = allVersionCodes.value;
        selectedVersions.value = checked ? allCodes : [];
        emit('update:modelValue', selectedVersions.value);
    };

    // 监听外部值变化
    watch(
        () => props.modelValue,
        (newVal) => {
            if (newVal) {
                selectedVersions.value = newVal;
            }
        },
        { immediate: true }
    );

    defineExpose({
        selectedVersions,
    });
</script>

<style scoped>
    .select-param {
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
    }

    .category-cell {
        padding: 16px;
        transition: background-color 0.2s ease;
    }

    .category-cell :deep(.el-checkbox__label) {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
    }

    .options-cell {
        padding: 16px;
        min-height: 48px;
        transition: background-color 0.2s ease;
    }

    :deep(.el-checkbox-group) {
        display: flex;
        flex-wrap: wrap;
        gap: 16px 32px;
    }

    :deep(.el-checkbox) {
        margin-right: 0;
        margin-bottom: 0;
        transition: all 0.2s ease;
        width: 320px !important;
    }

    :deep(.el-checkbox__label) {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: #475569;
        font-size: 14px;
        transition: color 0.2s ease;
        max-width: 280px; /* 留出一些空间给checkbox本身 */
    }

    :deep(.el-checkbox:hover) {
        transform: translateY(-1px);
    }

    :deep(.el-table) {
        --el-table-border-color: #e2e8f0;
        background-color: #ffffff;
        border-radius: 8px;
        overflow: hidden;
        border: 1px solid #e2e8f0;
        box-shadow:
            0 1px 3px 0 rgba(0, 0, 0, 0.1),
            0 1px 2px -1px rgba(0, 0, 0, 0.1);
    }

    :deep(.el-table__header th) {
        background-color: #f8fafc !important;
        font-weight: 600;
        color: #1e293b;
        padding: 16px !important;
        border-bottom: 1px solid #e2e8f0;
        transition: background-color 0.2s ease;
    }

    :deep(.el-table__row) {
        background-color: white;
        transition: background-color 0.2s ease;
    }

    :deep(.el-table__row.el-table__row--striped) {
        background-color: #f8fafc;
    }

    :deep(.el-table__row:hover > td) {
        background-color: #f1f5f9 !important;
        transform: translateZ(0);
    }

    :deep(.el-table__cell) {
        padding: 0 !important;
        color: #475569;
        transition: all 0.2s ease;
    }

    :deep(.el-table__cell.is-leaf) {
        border-right: none;
    }

    :deep(.el-table__column:last-child .el-table__cell) {
        border-right: none;
    }

    :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
        transition: all 0.2s ease;
    }

    :deep(.el-checkbox__input.is-indeterminate .el-checkbox__inner) {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
        transition: all 0.2s ease;
    }

    :deep(.el-checkbox__inner) {
        transition: all 0.2s ease;
    }

    :deep(.el-checkbox__inner:hover) {
        border-color: var(--el-color-primary);
        transform: scale(1.05);
    }

    :deep(.el-checkbox.is-checked .el-checkbox__label) {
        color: var(--el-color-primary);
    }

    .category-header {
        display: flex;
        align-items: center;
        padding: 0 16px;
    }

    :deep(.category-header .el-checkbox__label) {
        font-size: 16px;
        font-weight: 600;
        color: #1e293b;
    }
</style>
