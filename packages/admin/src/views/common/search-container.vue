<template>
    <div ref="container" class="expand-contract-container">
        <div class="slot-container">
            <div v-for="item in visibleItems" :key="item.index">
                <div>
                    <slot :name="'item-' + item.index" :field="item.field"></slot>
                </div>
            </div>
        </div>
        <div v-if="arrowVisible" class="expand-contract-container-toggle">
            <span
                @click="toggleExpand"
                style="cursor: pointer; color: #409eff; display: inline-flex; align-items: center">
                <el-icon style="margin-right: 4px; display: flex; align-items: center">
                    <component :is="expanded ? ArrowUpBold : ArrowDownBold" />
                </el-icon>
                {{ expanded ? '收起' : '展开' }}
            </span>
        </div>
    </div>
    <div v-if="activeFields && activeFields.length" class="collapsed-values">
        <span v-for="field in activeFields" :key="field">
            <template v-if="isValueNotEmpty && isValueNotEmpty(searchForm[field])">
                <el-tag
                    type="primary"
                    :closable="field !== 'siteCode' && field !== 'desktopCode'"
                    @close="
                        () => {
                            if (field !== 'siteCode' && handleTagClose) {
                                handleTagClose(field, searchForm);
                                const index = activeFields.indexOf(field);
                                if (index !== -1) activeFields.splice(index, 1);
                            }
                        }
                    ">
                    {{ getKeyFormat ? getKeyFormat(field) : field }}:
                    {{ getValueFormat ? getValueFormat(field, searchForm[field], orgOptions) : searchForm[field] }}
                </el-tag>
            </template>
        </span>
    </div>
    <!--
      测试使用
      <div>
        maxItemsPerRow: {{ maxItemsPerRow }} totalRows:
        {{ totalRows }} itemsPerRow: {{ itemsPerRow }} itemWidth:
        {{ itemWidth }}
    </div> -->
</template>

<script setup lang="ts">
    import { computed, nextTick, onBeforeUnmount, onMounted, ref, useSlots } from 'vue';
    // @ts-ignore
    import debounce from 'lodash-es/debounce';
    import { ArrowDownBold, ArrowUpBold } from '@element-plus/icons-vue';

    const props = defineProps<{
        initialWidth?: number;
        searchForm?: any;
        isValueNotEmpty?: (val: any) => boolean;
        getKeyFormat?: (field: string) => string;
        getValueFormat?: (field: string, value: any, orgOptions?: any[]) => string;
        handleTagClose?: (field: string, form: any) => void;
        orgOptions?: any[];
        activeFields: string[];
    }>();

    const expanded = ref(false);
    const containerWidth = ref(0);
    const itemWidth = ref(props.initialWidth ?? 180);
    const maxItemsPerRow = ref(1);
    const container = ref<HTMLElement | null>(null);
    const slots = useSlots();

    const fieldKeys = computed(() => {
        return props.searchForm ? Object.keys(props.searchForm) : [];
    });

    const slotContentLength = computed(() => {
        return Math.min(slots ? Object.keys(slots).length : 0, fieldKeys.value.length);
    });
    const gap = 10;
    const itemsPerRow = computed(() => {
        return Math.max(1, Math.floor((containerWidth.value + gap) / (itemWidth.value + gap)));
    });
    const totalRows = computed(() => Math.ceil(slotContentLength.value / itemsPerRow.value));

    const visibleItems = computed(() => {
        const length = expanded.value ? fieldKeys.value.length : Math.min(itemsPerRow.value, fieldKeys.value.length);
        return Array.from({ length }, (_, i) => ({
            index: i,
            field: fieldKeys.value[i] || '',
        }));
    });
    const arrowVisible = computed(() => {
        return totalRows.value > 1;
    });
    const toggleExpand = () => {
        expanded.value = !expanded.value;
    };
    const calculateContainerWidth = () => {
        if (container.value) {
            containerWidth.value = container.value.clientWidth - 60;
            maxItemsPerRow.value = Math.max(1, Math.floor(containerWidth.value / itemWidth.value));
        }
    };
    const debouncedCalculateContainerWidth = debounce(() => {
        calculateContainerWidth();
    }, 300);

    onMounted(() => {
        window.addEventListener('resize', debouncedCalculateContainerWidth);
        nextTick(() => {
            calculateContainerWidth();
        });
    });
    onBeforeUnmount(() => {
        window.removeEventListener('resize', debouncedCalculateContainerWidth);
    });

    defineExpose({ expanded });
</script>
<style scoped>
    .expand-contract-container {
        display: flex;
        align-items: flex-start;
        margin: 10px 0 0 0;
    }

    .slot-container {
        width: calc(100% - 80px);
        display: flex;
        flex-wrap: wrap;
        gap: 0 10px;
    }

    .expand-contract-container-toggle {
        width: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 32px;
        margin-left: 4px;
    }

    .collapsed-values {
        margin-top: 8px;
        margin-left: 10px;
        color: #666;
        font-size: 14px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
    }
</style>
