<template>
    <div class="page-container">
        <div class="search-style" v-if="showSearch">
            <slot name="search" />
        </div>
        <div class="toolbars-style" v-if="showToolbars">
            <slot name="toolbar" />
        </div>
        <div class="body-style" v-if="showList">
            <div class="table-scroll-area">
                <slot name="list" />
            </div>
            <div class="pagination-area">
                <slot name="pagination" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    const props = defineProps({
        showToolbars: {
            type: Boolean,
            default: true,
        },
        showSearch: {
            type: Boolean,
            default: true,
        },
        showList: {
            type: <PERSON>olean,
            default: true,
        },
    });
</script>
<style scoped>
    .page-container {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 105px);
        box-sizing: border-box;
        background: #f5f5f5;
    }

    .search-style,
    .toolbars-style {
        background: white;
        padding: 10px;
        border-radius: 10px;
        margin-bottom: 10px;
        flex-shrink: 0;
    }

    .search-style {
        border-radius: 0 0 10px 10px;
    }

    .body-style {
        background: white;
        border-radius: 10px;
        padding: 10px;
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 0;
    }

    .table-scroll-area {
        flex: 1;
        min-height: 0;
        overflow: auto;
    }

    .pagination-area {
        flex-shrink: 0;
        margin-top: 10px;
    }
</style>
