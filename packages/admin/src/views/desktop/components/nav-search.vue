<template>
    <el-form inline label-width="100" :label-suffix="':'" :size="'default'" class="mt-2">
        <search-container
            :initialWidth="315"
            :searchForm="searchForm"
            :isValueNotEmpty="isValueNotEmpty"
            :getKeyFormat="getKeyFormat"
            :getValueFormat="getValueFormat"
            :handleTagClose="handleTagClose"
            :activeFields="activeFields">
            <template #item-0>
                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.statuses"
                        @update:modelValue="(val: any) => handleFieldUpdate('statuses', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in enableStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-1>
                <el-form-item label="上下线状态">
                    <el-select
                        v-model="searchForm.onlineStatuses"
                        @update:modelValue="(val: any) => handleFieldUpdate('onlineStatuses', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in onlineStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-2>
                <el-form-item label="审核状态">
                    <el-select
                        v-model="searchForm.auditStatuses"
                        @update:modelValue="(val: any) => handleFieldUpdate('auditStatuses', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in auditStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-3>
                <el-form-item label="关键词">
                    <el-input
                        v-model="searchForm.title"
                        @update:modelValue="(val: any) => handleFieldUpdate('title', val)"
                        placeholder="请输入"
                        style="width: 180px"
                        clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </template>
            <template #item-4>
                <el-form-item label="可见状态">
                    <el-select
                        v-model="searchForm.visibleStatuses"
                        @update:modelValue="(val: any) => handleFieldUpdate('visibleStatuses', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in visibleStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-5>
                <el-form-item label="删除状态">
                    <el-select
                        v-model="searchForm.delFlags"
                        @update:modelValue="(val: any) => handleFieldUpdate('delFlags', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in delFlagOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
        </search-container>
    </el-form>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref } from 'vue';
    import { NavSearchForm } from '@smartdesk/common/types';
    import { useEnumStore } from '@chances/portal_common_core';
    import SearchContainer from '@smartdesk/admin/views/common/search-container.vue';
    import { getKeyFormat, getValueFormat, handleTagClose, isValueNotEmpty } from '@smartdesk/common/utils';

    // 枚举
    const enumStore = useEnumStore();

    // 用户设置表单字段的顺序
    const activeFields = ref<string[]>([]);

    // 删除状态枚举
    const delFlagOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('delFlag') || []);
    // 启用状态枚举
    const enableStatusOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('enableStatus') || []);
    // 审核状态枚举
    const auditStatusOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('auditStatus') || []);
    // 可见状态枚举
    const visibleStatusOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('visibleStatus') || []);
    // 上线状态枚举
    const onlineStatusOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('onlineStatus') || []);
    // 参数
    const props = defineProps<{
        modelValue: Partial<NavSearchForm>;
    }>();

    const emit = defineEmits<{
        // 送审当前
        (e: 'update:modelValue', item: Partial<NavSearchForm>): void;
        (e: 'search'): void;
    }>();

    // 查询表单
    const searchForm = computed({
        get: () => props.modelValue,
        set: (value) => emit('update:modelValue', value),
    });

    function handleFieldUpdate(field: keyof NavSearchForm, value: any) {
        searchForm.value[field] = value;

        // 判断值是否有效（非空/非空数组）
        const isEmpty =
            value === '' || value === null || value === undefined || (Array.isArray(value) && value.length === 0);

        if (!isEmpty) {
            if (!activeFields.value.includes(field as string)) {
                activeFields.value.push(field as string);
            }
        } else {
            // 如果用户清空值了，移除 field
            const index = activeFields.value.indexOf(field as string);
            if (index !== -1) {
                activeFields.value.splice(index, 1);
            }
        }
    }

    // 初始化字段
    const initActiveFields = () => {
        Object.entries(searchForm.value).forEach(([key, val]) => {
            if (isValueNotEmpty(val)) {
                if (!activeFields.value.includes(key)) {
                    activeFields.value.push(key);
                }
            }
        });
    };
    // 组件挂载时请求数据
    onMounted(() => {
        initActiveFields();
    });
</script>
