<template>
    <el-drawer
        :title="desktop?.name + '导航'"
        v-model="visible"
        destroy-on-close
        direction="rtl"
        size="80%"
        append-to-body
        :z-index="1000">
        <PageContainer>
            <template #search>
                <nav-search v-model="searchForm" @search="handleSearch" />
            </template>

            <template #toolbar>
                <nav-toolbars :selected-navs="selectedNavs" @add="handleAdd" @refresh="handleSearch" />
            </template>

            <template #list>
                <nav-list
                    ref="navListRef"
                    :section-form="searchForm"
                    :desktop="desktop"
                    @edit="handleEdit"
                    @add-child-nav="handleAddChildNav"
                    @update:selection="handleUpdateSelection" />
            </template>
        </PageContainer>
    </el-drawer>
</template>

<script setup lang="ts">
    import { computed, onActivated, PropType, ref, watch } from 'vue';
    import { Nav, NavSearchForm, Page } from '@smartdesk/common/types';
    import NavToolbars from './nav-toolbars.vue';
    import NavList from './nav-list.vue';
    import NavSearch from './nav-search.vue';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { useRouter } from 'vue-router';
    import { useRouteReturnHandler } from '@smartdesk/common/composables';
    import PageContainer from '@smartdesk/admin/views/common/page-container.vue';

    // 桌面导航抽屉
    defineOptions({
        name: 'DesktopNavDrawer',
    });

    const props = defineProps({
        modelValue: {
            type: Boolean,
            required: true,
        },
        desktop: {
            type: Object as PropType<Page>,
            required: true,
        },
    });

    // 事件
    const emit = defineEmits(['update:modelValue', 'save', 'addNav', 'editNav', 'addChildNav']);

    // pinia store
    const siteStore = useSiteStore();
    const router = useRouter();

    // ref
    const navListRef = ref<HTMLElement | null>(null);
    const selectedNavs = ref<Nav[]>([]);

    // 导航查询表单
    const searchForm = ref<Partial<NavSearchForm>>({
        siteCode: siteStore.currentSiteCode,
        desktopCode: props.desktop.code,
        delFlags: [0] as number[],
        statuses: [],
        onlineStatuses: [],
        auditStatuses: [],
        visibleStatuses: [],
        title: '',
    });

    // useRouteReturnHandler，state 状态默认为 false
    const { setState, onActivated: handleActivated } = useRouteReturnHandler<boolean>({
        targetRoute: '/desktop',
        initialState: false,
        onReturn: (state) => {
            if (state.value) {
                // 返回时，如果 state 状态为 true，则打开抽屉
                visible.value = true;
            }
        },
    });
    // 抽屉的显隐
    const visible = computed({
        get: () => props.modelValue,
        set: (value) => emit('update:modelValue', value),
    });

    // 监听 visible 变化：关闭时重置 state 状态
    watch(visible, (newVal, oldVal) => {
        if (oldVal && !newVal && router.currentRoute.value.path === '/desktop') {
            setState(false);
        }
    });

    // 监听路由变化：非目标路由强制关闭
    watch(
        () => router.currentRoute.value.path,
        (newPath) => {
            if (newPath !== '/desktop') {
                visible.value = false;
            }
        }
    );

    // 支持 keep-alive：组件激活时触发返回逻辑
    onActivated(() => {
        handleActivated();
    });

    // 用户主动打开抽屉
    const openDrawer = () => {
        setState(true);
        visible.value = true;
    };

    // 用户主动关闭抽屉
    const closeDrawer = () => {
        visible.value = false;
    };

    const handleSearch = () => {
        (navListRef.value as any)?.getNavTree();
        (navListRef.value as any)?.getPageList();
    };

    const handleAdd = () => {
        (navListRef.value as any)?.addBaseNav();
    };

    const handleEdit = (data: Nav) => {
        emit('editNav', data);
    };

    const handleAddChildNav = (data: Nav) => {
        emit('addChildNav', data);
    };

    const handleUpdateSelection = (data: Nav[]) => {
        selectedNavs.value = data;
    };

    // 监听 siteStore 和 desktop
    watch(
        () => siteStore.currentSiteCode,
        (newVal) => {
            searchForm.value.siteCode = newVal;
        }
    );

    watch(
        () => props.desktop,
        (newVal) => {
            searchForm.value.desktopCode = newVal.code;
        }
    );

    defineExpose({
        handleSearch,
        openDrawer,
        closeDrawer,
    });
</script>
