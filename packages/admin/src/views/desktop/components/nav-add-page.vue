<template>
    <el-dialog v-model="props.visible" width="30%" @close="onClickCancel" title="新增页面">
        <el-form
            ref="pageFormRef"
            :model="pageForm"
            :rules="pageRules"
            label-width="100px"
            label-suffix=":"
            key="formKey">
            <el-form-item label="页面名称" prop="name">
                <el-input v-model="pageForm.name" placeholder="请输入页面名称" clearable />
            </el-form-item>

            <el-form-item label="页面分辨率" prop="resolution">
                <el-select v-model="pageForm.resolution" placeholder="请选择分辨率" size="default" clearable>
                    <el-option
                        v-for="item in resolutionOption"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="onClickCancel">取消</el-button>
            <el-button type="primary" @click="onAddPageConfirm">确认</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
    import { ref,watch } from 'vue';
    import { ElMessage, ElForm } from 'element-plus';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { useEnumStore } from '@chances/portal_common_core';

    const emit = defineEmits(['update:visible', 'confirm']);

    // props
    const props = defineProps<{
        visible: boolean;
    }>();

    // 网站 pinia
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // 分辨率选项
    const resolutionOption = ref(enumStore.getEnumsByKey('resolution') || []);

    // 默认表单值
    const defaultPage = () => ({
        name: '',
        resolution: '',
        siteCode: siteStore.currentSite?.code,
    });

    // 表单数据
    const pageForm = ref(defaultPage());

    // 表单 ref
    const pageFormRef = ref<InstanceType<typeof ElForm> | null>(null);

    // 表单校验规则
    const pageRules = {
        name: [
            {
                required: true,
                message: '请输入页面名称',
                trigger: 'blur',
            },
        ],
        resolution: [
            {
                required: true,
                message: '请选择分辨率',
                trigger: 'change',
            },
        ],
    };

    // 关闭时重置表单
    const onClickCancel = () => {
        emit('update:visible', false);
        resetForm();
    };

    // 重置表单
    const resetForm = () => {
        pageForm.value = defaultPage();
        pageFormRef.value?.clearValidate();
    };

    // 提交
    const onAddPageConfirm = async () => {
        await pageFormRef.value?.validate((valid) => {
            if (!valid) return;

            // 模拟提交操作，比如 emit 或请求
            emit('confirm', pageForm.value); // 向父组件提交数据

            ElMessage.success('新增页面成功');
            onClickCancel(); // 关闭弹窗
        });
    };
</script>
