<template>
    <div class="m-5">
        <icon-text-button
            :disabled="!permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.NAV.CREATE)"
            :icon="Plus"
            text="新建导航"
            color="#23bcca"
            @click="onClickAdd" />
        <icon-text-button
            :disabled="
                !canBatchEnable(selectedNavs) ||
                !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.NAV.BATCH_ENABLE, {
                    type: 'org',
                    value: selectedNavs.filter((nav) => nav.orgId).map((nav) => nav.orgId),
                })
            "
            :icon="CircleCheck"
            text="批量启用"
            color="#23bcca"
            @click="onClickBatchEnable" />
        <icon-text-button
            :disabled="
                !canBatchDisable(selectedNavs) ||
                !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.NAV.BATCH_DISABLE, {
                    type: 'org',
                    value: selectedNavs.filter((nav) => nav.orgId).map((nav) => nav.orgId),
                })
            "
            :icon="CircleClose"
            text="批量禁用"
            color="#ff9f25"
            @click="onClickBatchDisable" />
        <icon-text-button
            :disabled="
                !canBatchAudit(selectedNavs) ||
                !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.NAV.BATCH_AUDIT, {
                    type: 'org',
                    value: selectedNavs.filter((nav) => nav.orgId).map((nav) => nav.orgId),
                })
            "
            :icon="CircleCheck"
            text="批量送审"
            color="#23bcca"
            @click="onClickBatchPublish" />
        <icon-text-button
            :disabled="
                !canBatchOnline(selectedNavs) ||
                !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.NAV.BATCH_ONLINE, {
                    type: 'org',
                    value: selectedNavs.filter((nav) => nav.orgId).map((nav) => nav.orgId),
                })
            "
            :icon="CircleClose"
            text="批量上线"
            color="#23bcca"
            @click="onClickBatchOnline" />
        <icon-text-button
            :disabled="
                !canBatchOffline(selectedNavs) ||
                !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.NAV.BATCH_OFFLINE, {
                    type: 'org',
                    value: selectedNavs.filter((nav) => nav.orgId).map((nav) => nav.orgId),
                })
            "
            :icon="CircleClose"
            text="批量下线"
            color="#ff9f25"
            @click="onClickBatchOffline" />
        <icon-text-button
            :disabled="
                !canBatchDelete(props.selectedNavs) ||
                !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.NAV.BATCH_DELETE, {
                    type: 'org',
                    value: props.selectedNavs.filter((nav) => nav.orgId).map((nav) => nav.orgId),
                })
            "
            :icon="Delete"
            text="批量删除"
            color="#ed5665"
            @click="onClickBatchDelete" />
    </div>
</template>

<script setup lang="ts">
    import { CircleCheck, CircleClose, Delete, Plus } from '@element-plus/icons-vue';
    import { Nav } from '@smartdesk/common/types';
    import { navApi, publishApi } from '@smartdesk/common/api';
    import {
        ADMIN_BIZ_PERMISSION,
        canBatchAudit,
        canBatchDelete,
        canBatchDisable,
        canBatchEnable,
        canBatchOffline,
        canBatchOnline,
    } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';
    import { computed } from 'vue';

    defineOptions({
        name: 'NavToolbars',
    });

    // 参数
    const props = defineProps<{
        selectedNavs: Nav[];
    }>();

    // 事件
    const emit = defineEmits(['add', 'refresh']);

    // pinia store
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 选中导航的编码列表
    const codes = computed(() => {
        return props.selectedNavs.map((item: Nav) => item.code) ?? [];
    });

    // 新建导航
    const onClickAdd = () => {
        emit('add');
    };

    // 刷新列表
    const refresh = () => {
        emit('refresh');
    };

    // 点击批量删除
    const onClickBatchDelete = async () => {
        if (await feedback.confirm('确定要批量删除导航吗？', '确认操作', 'warning')) {
            const response = await publishApi.batchPublishComplete('Nav', codes.value, 'DELETE');
            if (response.code === 200) {
                feedback.success('批量删除成功');
                refresh();
            } else {
                feedback.error('批量删除失败：' + response.msg);
            }
        }
    };

    // 点击批量启用
    const onClickBatchEnable = async () => {
        if (await feedback.confirm('确定要批量启用导航吗？', '确认操作', 'warning')) {
            const response = await navApi.batchEnableNav(codes.value);
            if (response.code === 200) {
                feedback.success('批量启用成功');
                refresh();
            } else {
                feedback.error('批量启用失败：' + response.msg);
            }
        }
    };

    // 点击批量禁用
    const onClickBatchDisable = async () => {
        if (await feedback.confirm('确定要批量禁用导航吗？', '确认操作', 'warning')) {
            const response = await navApi.batchDisableNav(codes.value);
            if (response.code === 200) {
                feedback.success('批量禁用成功');
                refresh();
            } else {
                feedback.error('批量禁用失败：' + response.msg);
            }
        }
    };

    // 点击批量送审
    const onClickBatchPublish = async () => {
        if (await feedback.confirm('确定要批量送审导航吗？', '确认操作', 'warning')) {
            const response = await publishApi.batchPublishSelf('Nav', codes.value, 'CREATE');
            if (response.code === 200) {
                feedback.success('批量送审成功');
                refresh();
            } else {
                feedback.error('批量送审失败：' + response.msg);
            }
        }
    };

    // 点击批量上线
    const onClickBatchOnline = async () => {
        if (await feedback.confirm('确定要批量上线导航吗？', '确认操作', 'warning')) {
            const response = await publishApi.batchPublishSelf('Nav', codes.value, 'ONLINE');
            if (response.code === 200) {
                feedback.success('批量上线成功');
                refresh();
            } else {
                feedback.error('批量上线失败：' + response.msg);
            }
        }
    };

    // 点击批量下线
    const onClickBatchOffline = async () => {
        if (await feedback.confirm('确定要批量下线导航吗？', '确认操作', 'warning')) {
            const response = await publishApi.batchPublishSelf('Nav', codes.value, 'OFFLINE');
            if (response.code === 200) {
                feedback.success('批量下线成功');
                refresh();
            } else {
                feedback.error('批量下线失败：' + response.msg);
            }
        }
    };
</script>
