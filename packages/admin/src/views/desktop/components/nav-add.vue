<template>
    <el-dialog v-model="props.modelValue" width="30%" @close="onClickCancel">
        <template #header>
            <span class="text-lg">{{ isEdit ? '编辑导航' : '新建导航' }}</span>
            {{ siteStore.currentSite?.name }}
        </template>

        <el-form ref="formRef" :model="form" :rules="rules" label-width="100px" label-suffix=":">
            <el-form-item v-if="parentNav.id" label="父导航">
                <div>{{ parentNav.title }}</div>
            </el-form-item>
            <el-form-item label="导航标题" prop="title">
                <el-input v-model="form.title" placeholder="请输入导航标题" />
            </el-form-item>
            <el-form-item label="导航类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择导航类型" size="default" clearable>
                    <el-option v-for="item in navTypeOption" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="菜单类型" prop="menuType">
                <el-select v-model="form.menuType" placeholder="请选择菜单类型" size="default" clearable>
                    <el-option v-for="item in menuTypeOption" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="默认页面" prop="defaultPageFlag">
                <el-switch v-model="form.defaultPageFlag" />
            </el-form-item>
            <el-form-item label="推荐策略" prop="ruleCode">
                <el-select v-model="form.ruleCode" placeholder="请选择推荐策略" size="default" clearable>
                    <el-option
                        v-for="item in personalRuleList"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="是否反选" prop="inverted">
                <el-switch v-model="form.inverted" />
            </el-form-item>
            <el-form-item label="关联页面">
                <el-radio-group v-model="pageType" @change="handlePageTypeChange">
                    <el-radio value="selectExistPage">选择已有页面</el-radio>
                    <el-radio value="createNewPage">新建页面</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item
                v-if="pageType === 'selectExistPage'"
                label="页面名称"
                prop="pageCode"
                class="flex items-center"
                :rules="
                    form.type !== 1
                        ? [
                              {
                                  required: true,
                                  message: '请选择页面',
                                  trigger: 'blur',
                              },
                          ]
                        : []
                ">
                <el-select
                    v-model="form.pageCode"
                    placeholder="请选择页面"
                    size="default"
                    clearable
                    @click="getPageList"
                    style="flex: 1">
                    <el-option v-for="item in pageList" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item
                v-if="form.page && pageType === 'createNewPage'"
                label="页面名称"
                prop="page.name"
                :rules="
                    form.type !== 1
                        ? [
                              {
                                  required: true,
                                  message: '请输入页面名称',
                                  trigger: 'blur',
                              },
                          ]
                        : []
                ">
                <el-input v-model="form.page.name" placeholder="请输入页面名称" clearable />
            </el-form-item>
            <el-form-item
                v-if="form.page && pageType === 'createNewPage'"
                label="页面分辨率"
                prop="page.resolution"
                :rules="
                    form.type !== 1
                        ? [
                              {
                                  required: true,
                                  message: '请选择分辨率',
                                  trigger: 'blur',
                              },
                          ]
                        : []
                ">
                <el-select v-model="form.page.resolution" placeholder="请选择分辨率" size="default" clearable>
                    <el-option
                        v-for="item in resolutionOption"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="顺序" prop="orderNo">
                <el-input-number
                    v-model="form.orderNo"
                    style="width: 100%"
                    placeholder="请输入顺序"
                    :min="0"
                    clearable />
            </el-form-item>

            <el-form-item
                v-for="imageType in navImageTypes"
                :key="imageType.code"
                :label="imageType.name + '：'"
                :prop="'icons.' + imageType.code">
                <image-editor v-model="(form.icons as any)[imageType.code]" />
            </el-form-item>
            <el-form-item label="组织" prop="orgId">
                <dimension-selector
                    v-model="form.orgId"
                    placeholder="请选择组织"
                    :data="orgTree"
                    label-key="name"
                    value-key="id"
                    width="370" />
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="onClickCancel">取消</el-button>
            <el-button type="primary" @click="onClickConfirm">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { FormInstance } from 'element-plus';
    import { Dimension, Nav, NavForm, Page, PersonalRule } from '@smartdesk/common/types';
    import { dimensionApi, pageApi, personalRuleApi } from '@smartdesk/common/api';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import { extractDimensions } from '@smartdesk/common/utils';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';

    // 参数
    const props = defineProps<{
        modelValue: boolean;
        desktop: Page;
        nav: Partial<Nav>;
        parentNav: Partial<Nav>;
    }>();

    // 事件
    const emit = defineEmits(['update:modelValue', 'submit']);

    // pinia store
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // 导航类型枚举
    const navTypeOption = ref<Partial<Enumeration>[]>(enumStore.getNameCodeNumberOptionsByKey('navType') || []);

    // 菜单类型枚举
    const menuTypeOption = ref<Partial<Enumeration>[]>(enumStore.getNameCodeNumberOptionsByKey('menuType') || []);

    // 图片类型
    const navImageTypes = ref<Enumeration[]>(enumStore.getEnumsByKey('navImageTypeEnum') || []);

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 表单引用
    const formRef = ref<FormInstance | null>(null);

    // 表单校验规则
    const rules = {
        title: [{ required: true, message: '请输入导航标题', trigger: 'blur' }],
        type: [{ required: true, message: '请选择导航类型', trigger: 'blur' }],
        menuType: [{ required: true, message: '请选择菜单类型', trigger: 'blur' }],
        orderNo: [{ required: true, message: '请输入顺序', trigger: 'blur' }],
    };

    // 默认页面
    const defaultPage: Partial<Page> = {
        siteCode: siteStore.currentSite?.code,
        name: '',
        resolution: '',
    };

    // 默认导航表单
    const defaultNavForm: Partial<NavForm> = {
        siteCode: siteStore.currentSite?.code,
        ownerPageCode: props.desktop.code,
        type: 0,
        icons: {},
        page: defaultPage,
    };

    // 导航表单
    const form = ref<Partial<NavForm>>(defaultNavForm);

    // 关联页面的类型
    const pageType = ref<string>('selectExistPage');

    // 分辨率状态枚举
    const resolutionOption = ref<Enumeration[]>(enumStore.getEnumsByKey('resolution') || []);

    // 是否是编辑
    const isEdit = ref<boolean>(false);

    // 页面列表
    const pageList = ref<Page[]>([]);

    // 推荐策略列表
    const personalRuleList = ref<PersonalRule[]>([]);

    // 处理页面类型变化
    const handlePageTypeChange = (val: string) => {
        if (val === 'selectExistPage') {
            // 选择已有页面
            form.value.page = defaultPage;
        } else {
            // 新建页面
            form.value.pageCode = '';
        }
    };

    // 重制表单
    const resetForm = () => {
        form.value = defaultNavForm;
        isEdit.value = false;
        pageType.value = 'selectExistPage';
    };

    // 关闭对话框
    const onClickCancel = () => {
        if (!isEdit.value) {
            resetForm();
        }
        emit('update:modelValue', false);
    };

    // 提交表单
    const onClickConfirm = () => {
        formRef.value?.validate((valid) => {
            if (valid) {
                // 处理页面布局
                if (form.value.page) {
                    const rect = extractDimensions(form.value.page.resolution ?? '');
                    form.value.page.layout = {
                        component: 'page',
                        rect: {
                            top: 0,
                            left: 0,
                            width: rect ? rect.width : 0,
                            height: rect ? rect.height : 0,
                        },
                    } as any;
                }

                emit('submit', form.value);
            }
        });
    };

    // 查询页面列表
    const getPageList = async () => {
        const res = await pageApi.getPageList(
            {
                siteCode: siteStore.currentSiteCode,
                delFlags: [0],
                statuses: [1],
            },
            { paged: false }
        );
        if (res.code === 200) {
            pageList.value = res.result;
        }
    };

    // 查询推荐策略列表
    const getPersonalRuleList = async () => {
        const res = await personalRuleApi.getPersonalRuleList(
            {
                delFlag: 0,
                status: 1,
            },
            { paged: false }
        );
        if (res.code === 200) {
            personalRuleList.value = res.result;
        }
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 监控 nav
    watch(
        () => props.nav,
        (newVal) => {
            if (newVal && newVal.id) {
                isEdit.value = true;
                form.value = { ...newVal };
                if (!form.value.icons) {
                    form.value.icons = {};
                }
                if (!form.value.page) {
                    form.value.page = defaultPage;
                }
            } else {
                isEdit.value = false;
                resetForm();
            }
        },
        { immediate: true }
    );

    onMounted(() => {
        getPageList();
        getPersonalRuleList();
        getOrgTree();
    });
</script>
