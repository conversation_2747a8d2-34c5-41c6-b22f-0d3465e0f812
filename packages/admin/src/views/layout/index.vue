<template>
    <PageContainer>
        <template #search>
            <layout-search @search="handleSearchLayout" />
        </template>

        <template #toolbar>
            <layout-toolbars :layoutSelection="layoutSelection" @refreshLayoutList="refreshLayoutList" />
        </template>

        <template #list>
            <layout-list
                ref="layoutListRef"
                :layoutForm="layoutSearchForm"
                @update:selection="handleSelectionLayout"
                @edit-layout="editLayout" />
        </template>
    </PageContainer>
    <layout-add v-model="layoutAddDialogVisible" :layoutData="currentLayout" @submit="handleLayoutAddSubmit" />
</template>

<script setup lang="ts">
    import LayoutToolbars from './components/layout-toolbars.vue';
    import LayoutList from './components/layout-list.vue';
    import LayoutSearch from './components/layout-search.vue';
    import layoutAdd from './components/layout-add.vue';
    import { Layout, LayoutParam } from '@smartdesk/common/types';
    import { ref } from 'vue';
    import { layoutApi } from '@smartdesk/common/api';
    import { useFeedback } from '@smartdesk/common/composables';
    import PageContainer from '../common/page-container.vue';

    // 组件名称：Layout
    defineOptions({
        name: 'Layout',
    });

    // pinia store
    const feedback = useFeedback();

    // 新增布局对话框
    const layoutAddDialogVisible = ref(false);

    const layoutListRef = ref<InstanceType<typeof LayoutList> | null>(null);

    // 楼层布局查询表单
    const layoutSearchForm = ref<LayoutParam>({} as LayoutParam);

    // 当前选中的楼层布局
    const currentLayout = ref<Layout>({} as Layout);

    // 已选中的楼层布局列表
    const layoutSelection = ref<Layout[]>([]);

    // 刷新楼层布局列表
    const refreshLayoutList = () => {
        layoutListRef.value?.findLayoutList();
    };

    // 中转楼层布局查询表单
    const handleSearchLayout = (data: LayoutParam) => {
        layoutSearchForm.value = { ...data };
    };

    // 布局选择事件
    const handleSelectionLayout = (selection: Layout[]) => {
        layoutSelection.value = selection;
    };

    // 修改布局
    const editLayout = (layout: Layout) => {
        currentLayout.value = layout;
        layoutAddDialogVisible.value = true;
    };

    // 更新布局
    const handleLayoutAddSubmit = async (data: Layout) => {
        const response = await layoutApi.updateLayout(data.code, data);
        if (response.code === 200) {
            feedback.success('更新楼层布局成功');
            layoutAddDialogVisible.value = false;
        } else {
            feedback.error('更新楼层布局失败：' + response.msg);
        }
    };
</script>
