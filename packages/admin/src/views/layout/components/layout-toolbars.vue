<template>
    <icon-text-button
        :disabled="
            !canBatchEnable(layoutSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.LAYOUT.BATCH_ENABLE, {
                type: 'org',
                value: layoutSelection.filter((layout) => layout.orgId).map((layout) => layout.orgId),
            })
        "
        :icon="CircleCheck"
        text="批量启用"
        color="#23bcca"
        @click="handleBatchEnable" />
    <icon-text-button
        :disabled="
            !canBatchDisable(layoutSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.LAYOUT.BATCH_DISABLE, {
                type: 'org',
                value: layoutSelection.filter((layout) => layout.orgId).map((layout) => layout.orgId),
            })
        "
        :icon="CircleClose"
        text="批量禁用"
        color="#ff9f25"
        @click="handleBatchDisEnable" />
    <icon-text-button
        :disabled="
            !canBatchDeleteByAdmin(layoutSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.LAYOUT.BATCH_DELETE, {
                type: 'org',
                value: layoutSelection.filter((layout) => layout.orgId).map((layout) => layout.orgId),
            })
        "
        :icon="Delete"
        text="批量删除"
        color="#ed5665"
        @click="handleBatchDelete" />
</template>

<script setup lang="ts">
    import { CircleCheck, CircleClose, Delete } from '@element-plus/icons-vue';
    import { Layout } from '@smartdesk/common/types';
    import { layoutApi } from '@smartdesk/common/api';
    import {
        ADMIN_BIZ_PERMISSION,
        canBatchDeleteByAdmin,
        canBatchDisable,
        canBatchEnable,
    } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';
    import { computed } from 'vue';

    // 参数
    const props = defineProps<{
        layoutSelection: Layout[];
    }>();

    // 事件
    const emit = defineEmits(['refreshLayoutList']);

    // pinia store
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 楼层布局编码列表
    const codes = computed(() => {
        return props.layoutSelection.map((item) => item.code);
    });

    // 批量删除
    const handleBatchDelete = async () => {
        if (await feedback.confirm(`确定要批量删除布局吗？`, '确认操作', 'warning')) {
            const response = await layoutApi.batchDeleteLayout(codes);
            if (response.code === 200) {
                feedback.success('批量删除成功');
                refreshLayoutList();
            } else {
                feedback.error('批量删除失败：' + response.msg);
            }
        }
    };

    // 批量启用
    const handleBatchEnable = async () => {
        if (await feedback.confirm(`确定要批量启用布局吗？`, '确认操作', 'warning')) {
            const response = await layoutApi.batchEnableLayout(codes);
            if (response.code === 200) {
                feedback.success('批量启用成功');
                refreshLayoutList();
            } else {
                feedback.error('批量启用失败：' + response.msg);
            }
        }
    };

    // 批量禁用
    const handleBatchDisEnable = async () => {
        if (await feedback.confirm(`确定要批量禁用布局吗？`, '确认操作', 'warning')) {
            const response = await layoutApi.batchDisableLayout(codes);
            if (response.code === 200) {
                feedback.success('批量禁用成功');
                refreshLayoutList();
            } else {
                feedback.error('批量禁用失败：' + response.msg);
            }
        }
    };

    // 刷新列表
    const refreshLayoutList = () => {
        emit('refreshLayoutList');
    };
</script>

<style scoped></style>
