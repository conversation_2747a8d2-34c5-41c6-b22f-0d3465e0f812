<template>
    <el-dialog v-model="visible" width="500px" @close="handleClose">
        <template #header>
            <span class="dialog-title">编辑楼层布局</span>
            {{ siteStore.currentSite?.name }}
        </template>

        <el-form ref="formRef" :model="layoutForm" :rules="rules" label-width="80px">
            <el-form-item label="布局名称" prop="name">
                <el-input v-model="layoutForm.name" placeholder="请输入布局名称" />
            </el-form-item>
            <el-form-item label="组织" prop="orgId">
                <dimension-selector
                    v-model="layoutForm.orgId"
                    placeholder="请选择组织"
                    :data="orgTree"
                    label-key="name"
                    value-key="id"
                    width="370" />
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { FormInstance } from 'element-plus';
    import { Dimension, Layout } from '@smartdesk/common/types';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { dimensionApi } from '@smartdesk/common/api';
    import { useFeedback } from '@smartdesk/common/composables';

    // 参数
    const props = defineProps<{
        modelValue: boolean;
        layoutData: Layout;
    }>();

    // 事件
    const emit = defineEmits(['update:modelValue', 'submit']);

    // pinia store
    const siteStore = useSiteStore();
    const feedback = useFeedback();

    // 判断是新增还是编辑
    const isEdit = ref(false);

    const visible = ref(false);

    const formRef = ref<FormInstance | null>(null);

    const layoutForm = ref<Partial<Layout>>({} as Layout);

    // 表单校验规则
    const rules = {
        name: [{ required: true, message: '请输入站点名称', trigger: 'blur' }],
    };

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 重置表单
    const resetForm = () => {
        layoutForm.value = {
            name: '',
        };
    };

    // 提交表单
    const handleSubmit = () => {
        formRef.value?.validate((valid) => {
            if (valid) {
                emit('submit', { ...layoutForm.value });
                handleClose();
                feedback.success('操作成功！');
            }
        });
    };

    // 关闭对话框
    const handleClose = () => {
        resetForm();
        emit('update:modelValue', false);
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 监听 userData 变化，填充表单
    watch(
        () => props.layoutData,
        (newLayout) => {
            if (newLayout) {
                isEdit.value = true;
                layoutForm.value = { ...newLayout };
            } else {
                isEdit.value = false;
                layoutForm.value = { name: '' };
            }
        },
        { immediate: true }
    );

    watch(
        () => props.modelValue,
        (val) => {
            visible.value = val;
        }
    );

    onMounted(() => {
        getOrgTree();
    });
</script>

<style scoped>
    .dialog-title {
        font-size: 18px;
    }
</style>
