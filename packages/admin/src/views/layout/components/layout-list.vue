<template>
    <BaseTableWithPagination
        :total="totalElements"
        :current-page="currentPage"
        :page-size="pageSize"
        @update:currentPage="handleCurrentChange"
        @update:pageSize="handleSizeChange">
        <template #table>
            <el-table
                ref="multipleTableRef"
                :data="layoutList"
                row-key="id"
                :header-cell-style="{ color: 'black', height: '50px' }"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="70" label="全选" />
                <el-table-column property="name" label="布局名称" min-width="240" show-overflow-tooltip />
                <el-table-column label="布局类型" min-width="100">
                    <template #default="{ row }">
                        <el-tag type="primary">{{ enumStore.getLabelByKeyAndValue('layoutType', row.type) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column property="icon" label="缩略图" width="150">
                    <template #default="{ row }">
                        <el-image
                            :src="row.icon"
                            :preview-src-list="[row.icon]"
                            hide-on-click-modal
                            preview-teleported
                            style="width: 100px; height: 50px">
                            <template #error>
                                <image-error-fallback text="图片损坏" />
                            </template>
                        </el-image>
                    </template>
                </el-table-column>
                <el-table-column property="orgId" label="组织" min-width="180">
                    <template #default="{ row }">
                        <div>
                            {{ orgOptions.find((org) => org.value === row.orgId)?.label }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="可用状态" min-width="120">
                    <template #default="{ row }">
                        <el-switch
                            v-if="row.status === 1"
                            :disabled="
                                !canDisable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.LAYOUT.DISABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                        <el-switch
                            v-else
                            :disabled="
                                !canEnable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.LAYOUT.ENABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                    </template>
                </el-table-column>
                <el-table-column label="修改时间" min-width="180">
                    <template #default="{ row }">
                        {{ row.modifiedTime ? format(row.modifiedTime, 'yyyy-MM-dd HH:mm:ss') : '' }}
                    </template>
                </el-table-column>
                <el-table-column property="modifiedBy" label="修改人" min-width="160" />
                <el-table-column label="操作" fixed="right" min-width="150">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-button
                                :disabled="
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.LAYOUT.EDIT, {
                                        type: 'org',
                                        value: row.orgId,
                                    })
                                "
                                type="primary"
                                text
                                @click="onClickEdit(row)">
                                编辑
                            </el-button>
                            <el-button
                                :disabled="
                                    !canDeleteByAdmin(row) ||
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.LAYOUT.DELETE, {
                                        type: 'org',
                                        value: row.orgId,
                                    })
                                "
                                type="danger"
                                text
                                @click="onClickDelete(row)">
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </BaseTableWithPagination>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { Layout } from '@smartdesk/common/types';
    import { format } from 'date-fns';
    import { dimensionApi, layoutApi } from '@smartdesk/common/api';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';
    import { LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import { ADMIN_BIZ_PERMISSION, canDeleteByAdmin, canDisable, canEnable } from '@smartdesk/common/permission';
    import { useFeedback } from '@smartdesk/common/composables';

    // 参数
    const props = defineProps<{
        layoutForm: any;
    }>();

    // 事件
    const emit = defineEmits(['update:selection', 'editLayout']);

    // pinia store
    const enumStore = useEnumStore();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 楼层布局列表
    const layoutList = ref<Layout[]>([]);

    // 选中的布局
    const layoutSelection = ref<Layout[]>([]);

    // 处理页码变化
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1;
        findLayoutList();
    };

    // 处理当前页变化
    const handleCurrentChange = (val: number) => {
        currentPage.value = val;
        findLayoutList();
    };

    // 处理布局选中
    const handleSelectionChange = (val: Layout[]) => {
        layoutSelection.value = val;
        emit('update:selection', val);
    };

    // 启用/禁用楼层布局
    const handleStatusChange = async (row: Layout) => {
        const newStatus = row.status;
        const originalStatus = newStatus === 1 ? 0 : 1;
        const confirmed = await feedback.confirm(
            `确定要${newStatus === 0 ? '不可用' : '可用'}该布局吗？`,
            '确定操作',
            'warning'
        );
        if (confirmed) {
            if (newStatus === 0) {
                await handleDisableLayout(row.code);
            } else {
                await handleEnableLayout(row.code);
            }
        } else {
            // 用户取消，恢复原状态
            row.status = originalStatus;
        }
    };

    // 不可用楼层
    const handleDisableLayout = async (code: string) => {
        try {
            const response = await layoutApi.disableLayout(code);
            if (response.code === 200) {
                feedback.success('不可用成功');
                await findLayoutList();
            } else {
                feedback.error('不可用失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('不可用失败');
        }
    };

    // 可用楼层
    const handleEnableLayout = async (code: string) => {
        try {
            const response = await layoutApi.enableLayout(code);
            if (response.code === 200) {
                feedback.success('可用成功');
                await findLayoutList();
            } else {
                feedback.error('可用失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('可用失败');
        }
    };

    // 点击编辑按钮
    const onClickEdit = (row: Layout) => {
        emit('editLayout', row);
    };

    // 点击删除按钮
    const onClickDelete = async (row: Layout) => {
        if (await feedback.confirm(`确定要删除该楼层布局吗？`, '确认操作', 'warning')) {
            const res = await layoutApi.batchDeleteLayout([row.code]);
            if (res.code === 200) {
                feedback.success(`删除楼层布局成功`);
                await findLayoutList();
            } else {
                feedback.error(`删除楼层布局失败：` + res.msg);
            }
        }
    };

    // 请求后台获取数据
    const findLayoutList = async () => {
        const response = await layoutApi.findLayouts(props.layoutForm, {
            page: currentPage.value - 1,
            size: pageSize.value,
            sort: 'id,desc',
        });
        layoutList.value = response.result;
        totalElements.value = Number(response.page.totalElements);
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    // 监听查询表单
    watch(
        () => props.layoutForm,
        () => {
            findLayoutList();
        },
        { deep: true }
    );

    onMounted(() => {
        getOrgOptions();
    });

    // 公开方法给父组件调用
    defineExpose({
        findLayoutList,
    });
</script>
