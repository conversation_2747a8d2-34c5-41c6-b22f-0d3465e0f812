<template>
    <icon-text-button
        :disabled="!permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SITE.CREATE)"
        :icon="Plus"
        text="新建网站"
        color="#23bcca"
        @click="onClickAdd" />
    <icon-text-button
        :disabled="
            !canBatchEnable(selectedSites) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.SITE.BATCH_ENABLE, {
                type: 'org',
                value: selectedSites.filter((site) => site.orgId).map((site) => site.orgId),
            })
        "
        :icon="CircleCheck"
        text="批量启用"
        color="#23bcca"
        @click="onClickBatchEnable" />
    <icon-text-button
        :disabled="
            !canBatchDisable(selectedSites) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.SITE.BATCH_DISABLE, {
                type: 'org',
                value: selectedSites.filter((site) => site.orgId).map((site) => site.orgId),
            })
        "
        :icon="CircleClose"
        text="批量禁用"
        color="#ff9f25"
        @click="onClickBatchDisable" />
    <icon-text-button
        :disabled="
            !canBatchDeleteByAdmin(selectedSites) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.SITE.BATCH_DELETE, {
                type: 'org',
                value: selectedSites.filter((site) => site.orgId).map((site) => site.orgId),
            })
        "
        :icon="Delete"
        text="批量删除"
        color="#ed5665"
        @click="onClickBatchDelete" />
</template>

<script setup lang="ts">
    import { CircleCheck, CircleClose, Delete, Plus } from '@element-plus/icons-vue';
    import { Site } from '@smartdesk/common/types';
    import { siteApi } from '@smartdesk/common/api';
    import { usePermissionStore } from '@chances/portal_common_core';
    import {
        ADMIN_BIZ_PERMISSION,
        canBatchDeleteByAdmin,
        canBatchDisable,
        canBatchEnable,
    } from '@smartdesk/common/permission';
    import { useFeedback } from '@smartdesk/common/composables';

    // 参数
    const props = defineProps<{
        selectedSites: Site[];
    }>();

    // 事件
    const emit = defineEmits(['add', 'refresh']);

    // pinia store
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 新建网站
    const onClickAdd = () => {
        emit('add');
    };

    // 刷新列表
    const refresh = () => {
        emit('refresh');
    };

    // 点击批量删除
    const onClickBatchDelete = async () => {
        if (await feedback.confirm('确定要批量删除网站吗？', '确认操作', 'warning')) {
            const codes = props.selectedSites.map((item: Site) => {
                return item.code;
            });

            const response = await siteApi.batchDeleteSite(codes);
            if (response.code === 200) {
                feedback.success('批量删除成功');
                refresh();
            } else {
                feedback.error('批量删除失败：' + response.msg);
            }
        }
    };

    // 点击批量启用
    const onClickBatchEnable = async () => {
        if (await feedback.confirm('确定要批量启用网站吗？', '确认操作', 'warning')) {
            const codes = props.selectedSites.map((item: Site) => {
                return item.code;
            });

            const response = await siteApi.batchEnableSite(codes);
            if (response.code === 200) {
                feedback.success('批量启用成功');
                refresh();
            } else {
                feedback.error('批量启用失败：' + response.msg);
            }
        }
    };

    // 点击批量禁用
    const onClickBatchDisable = async () => {
        if (await feedback.confirm('确定要批量禁用网站吗？', '确认操作', 'warning')) {
            const codes = props.selectedSites.map((item: Site) => {
                return item.code;
            });

            const response = await siteApi.batchDisableSite(codes);
            if (response.code === 200) {
                feedback.success('批量禁用成功');
                refresh();
            } else {
                feedback.error('批量禁用失败：' + response.msg);
            }
        }
    };
</script>
