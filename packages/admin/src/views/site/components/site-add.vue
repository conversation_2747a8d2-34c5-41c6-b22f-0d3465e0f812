<template>
    <el-dialog v-if="visible" v-model="visible" width="35%" @close="onClickCancel">
        <template #header>
            <span class="text-lg">{{ isEdit ? '编辑网站' : '新建网站' }}</span>
        </template>

        <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" label-suffix=":">
            <div class="mb-4">
                <div class="text-sm font-medium text-gray-700 mb-3">基础信息</div>
                <el-form-item label="网站名称" prop="name">
                    <el-input v-model="form.name" placeholder="请输入网站名称" />
                </el-form-item>
                <el-form-item label="数据域" prop="domain">
                    <el-input v-model="form.domain" placeholder="请输入数据域" />
                </el-form-item>
                <el-form-item label="组织" prop="orgId">
                    <dimension-selector
                        v-model="form.orgId"
                        placeholder="请选择组织"
                        :data="orgTree"
                        label-key="name"
                        value-key="id"
                        width="370" />
                </el-form-item>
            </div>

            <div class="border-t pt-4">
                <div class="text-sm font-medium text-gray-700 mb-3">网站配置</div>
                <el-form-item label="可编辑组件样式">
                    <div class="flex items-center space-x-3">
                        <el-switch v-model="canEditComponentStyle" />
                    </div>
                </el-form-item>
            </div>
        </el-form>

        <template #footer>
            <el-button @click="onClickCancel">取消</el-button>
            <el-button type="primary" @click="onClickConfirm">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref, watch } from 'vue';
    import { FormInstance } from 'element-plus';
    import { Dimension, Site } from '@smartdesk/common/types';
    import { dimensionApi } from '@smartdesk/common/api';
    import { nameDuplicateValidator } from '@smartdesk/common/utils';

    // 参数
    const props = defineProps<{
        modelValue: boolean;
        site: Partial<Site>;
    }>();

    // 事件
    const emit = defineEmits(['update:modelValue', 'submit']);

    // 模态框显隐
    const visible = ref(false);

    // 表单引用
    const formRef = ref<FormInstance | null>(null);

    // 表单校验规则
    const rules = {
        name: [
            { required: true, message: '请输入网站名称', trigger: 'blur' },
            {
                validator: nameDuplicateValidator(() => {
                    return {
                        entityType: 'Site',
                        name: form.value.name,
                        excludeId: form.value.id,
                    };
                }),
                trigger: ['blur', 'change'],
            },
        ],
    };

    // 网站表单
    const form = ref<Partial<Site>>({});

    // 是否是编辑
    const isEdit = ref(false);

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 是否可以编辑组件样式
    const canEditComponentStyle = computed({
        get: () => form.value.config?.canEditComponentStyle,
        set: (val) => {
            if (!form.value.config) {
                form.value.config = {};
            }
            form.value.config.canEditComponentStyle = val;
        },
    });

    // 重制表单
    const resetForm = () => {
        form.value = {
            config: {
                canEditComponentStyle: true,
            },
        };
        isEdit.value = false;
    };

    // 关闭对话框
    const onClickCancel = () => {
        if (!isEdit.value) {
            resetForm();
        }
        emit('update:modelValue', false);
    };

    // 提交表单
    const onClickConfirm = () => {
        formRef.value?.validate((valid) => {
            if (valid) {
                emit('submit', form.value);
            }
        });
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 监控 site
    watch(
        () => props.site,
        (newVal) => {
            if (newVal && newVal.id) {
                isEdit.value = true;
                form.value = { ...newVal };
            } else {
                isEdit.value = false;
                resetForm();
            }
        },
        { immediate: true }
    );

    // 监控 modelValue
    watch(
        () => props.modelValue,
        (val) => {
            visible.value = val;
        }
    );

    onMounted(() => {
        getOrgTree();
    });
</script>
