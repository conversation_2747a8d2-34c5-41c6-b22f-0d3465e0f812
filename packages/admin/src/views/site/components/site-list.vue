<template>
    <BaseTableWithPagination
        :total="totalElements"
        :current-page="currentPage"
        :page-size="pageSize"
        @update:currentPage="handleCurrentChange"
        @update:pageSize="handleSizeChange">
        <template #table>
            <el-table
                :data="siteList"
                row-key="id"
                style="width: 100%"
                @selection-change="handleSelectionChange"
                :header-cell-style="{ color: 'black', height: '50px' }"
                height="100%">
                <el-table-column type="selection" width="70" label="全选" />
                <el-table-column property="name" label="网站名称" width="240" show-overflow-tooltip />
                <el-table-column property="domain" label="数据域" width="180" />
                <el-table-column property="orgId" label="组织" width="180">
                    <template #default="{ row }">
                        <div>
                            {{ orgOptions.find((org) => org.value === row.orgId)?.label }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="删除状态" width="160">
                    <template #default="{ row }">
                        <div>
                            {{ enumStore.getLabelByKeyAndValue('delFlag', row.delFlag) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="可用状态" width="160">
                    <template #default="{ row }">
                        <el-switch
                            v-if="row.status === 1"
                            :disabled="
                                !canDisable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SITE.DISABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                        <el-switch
                            v-else
                            :disabled="
                                !canEnable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SITE.ENABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                    </template>
                </el-table-column>
                <el-table-column label="修改时间" width="180">
                    <template #default="{ row }">
                        {{ row.modifiedTime ? format(row.modifiedTime, 'yyyy-MM-dd HH:mm:ss') : '' }}
                    </template>
                </el-table-column>
                <el-table-column property="modifiedBy" label="修改人" width="160" />
                <el-table-column label="操作" fixed="right" width="300">
                    <template #default="{ row }">
                        <el-button
                            :disabled="
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SITE.EDIT, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            type="warning"
                            text
                            @click="onClickEdit(row)">
                            编辑
                        </el-button>
                        <el-button
                            v-if="row.delFlag === 0 && row.status === 1"
                            type="primary"
                            text
                            @click="onClickDesktop(row)">
                            桌面
                        </el-button>
                        <el-button
                            v-if="row.delFlag === 0 && row.status === 1"
                            type="primary"
                            text
                            @click="onClickPage(row)">
                            页面
                        </el-button>
                        <el-button
                            :disabled="
                                !canDeleteByAdmin(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SITE.DELETE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            type="danger"
                            text
                            @click="onClickDelete(row)">
                            删除
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </BaseTableWithPagination>
</template>

<script setup lang="ts">
    import { onMounted, PropType, ref, watch } from 'vue';
    import { Site, SiteSearchForm } from '@smartdesk/common/types';
    import { format } from 'date-fns';
    import { dimensionApi, siteApi } from '@smartdesk/common/api';
    import { useRouter } from 'vue-router';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';
    import { ADMIN_BIZ_PERMISSION, canDeleteByAdmin, canDisable, canEnable } from '@smartdesk/common/permission';
    import { useFeedback } from '@smartdesk/common/composables';

    // 参数
    const props = defineProps({
        sectionForm: {
            type: Object as PropType<Partial<SiteSearchForm>>,
            default: () => ({}),
        },
    });

    // 事件
    const emits = defineEmits(['update:selection', 'edit']);

    // pinia store
    const feedback = useFeedback();
    const enumStore = useEnumStore();
    const router = useRouter();
    const siteStore = useSiteStore();
    const permissionStore = usePermissionStore();

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);

    // 处理页码变化
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1;
        getSiteList();
    };

    // 处理当前页变化
    const handleCurrentChange = (val: number) => {
        currentPage.value = val;
        getSiteList();
    };

    // 网站列表
    const siteList = ref<Site[]>([]);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 选中的网站
    const selectedSites = ref<Site[]>([]);
    const handleSelectionChange = (val: Site[]) => {
        selectedSites.value = val;
        emits('update:selection', val);
    };

    // 启用/禁用网站
    const handleStatusChange = async (row: Site) => {
        const newStatus = row.status;
        const originalStatus = newStatus === 1 ? 0 : 1;
        const confirmed = await feedback.confirm(
            `确定要${newStatus === 0 ? '不可用' : '可用'}该网站吗？`,
            '确定操作',
            'warning'
        );
        if (confirmed) {
            if (newStatus === 0) {
                await handleDisableSite(row.code);
            } else {
                await handleEnableSite(row.code);
            }
        } else {
            // 用户取消，恢复原状态
            row.status = originalStatus;
        }
    };

    // 点击编辑
    const onClickEdit = (row: Site) => {
        emits('edit', row);
    };

    // 点击桌面
    const onClickDesktop = (row: Site) => {
        siteStore.switchSite(row.code);

        router.push({
            path: '/desktop',
            query: {
                siteCode: row.code,
            },
        });
    };

    // 点击页面
    const onClickPage = (row: Site) => {
        siteStore.switchSite(row.code);

        router.push({
            path: '/page-list',
            query: {
                siteCode: row.code,
            },
        });
    };

    // 点击删除
    const onClickDelete = async (row: Site) => {
        if (await feedback.confirm(`确定要删除该网站吗？`, '确认操作', 'warning')) {
            await siteApi.deleteSite(row.code);
            getSiteList();
            feedback.success('删除成功');
        }
    };

    // 处理禁用网站
    const handleDisableSite = async (code: string) => {
        try {
            const response = await siteApi.disableSite(code);
            if (response.code === 200) {
                feedback.success('不可用成功');
                await getSiteList();
            } else {
                feedback.error('不可用失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('不可用失败');
        }
    };

    // 处理启用网站
    const handleEnableSite = async (code: string) => {
        try {
            const response = await siteApi.enableSite(code);
            if (response.code === 200) {
                feedback.success('可用成功');
                await getSiteList();
            } else {
                feedback.error('可用失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('可用失败');
        }
    };

    // 请求后台获取数据
    const getSiteList = async () => {
        try {
            const response = await siteApi.findSites(props.sectionForm, {
                page: currentPage.value - 1,
                size: pageSize.value,
                sort: 'id,desc',
            });
            if (response.code === 200) {
                siteList.value = response.result;
                totalElements.value = Number(response.page.totalElements);
            } else {
                feedback.error('获取网站数据失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('获取网站数据失败');
        } finally {
            await siteStore.fetchSite();
        }
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    watch(
        () => props.sectionForm,
        () => {
            getSiteList();
        },
        { deep: true }
    );

    onMounted(() => {
        getSiteList();
        getOrgOptions();
    });

    defineExpose({
        getSiteList,
        selectedSites,
    });
</script>
