<template>
    <PageContainer>
        <template #search>
            <site-search v-model="searchForm" @search="handleSearch" />
        </template>

        <template #toolbar>
            <site-toolbars :selected-sites="selectedSites" @add="handleAdd" @refresh="handleRefresh" />
        </template>

        <template #list>
            <site-list
                ref="siteListRef"
                :section-form="searchForm"
                @edit="handleEdit"
                @update:selection="handleUpdateSelection" />
        </template>
    </PageContainer>
    <site-add v-model="siteDialogVisible" :site="currentSite" @submit="handleSubmit" />
</template>

<script setup lang="ts">
    import { LabelValue } from '@chances/portal_common_core';
    import { onMounted, ref } from 'vue';
    import { siteApi } from '@smartdesk/common/api';
    import SiteSearch from './components/site-search.vue';
    import SiteList from './components/site-list.vue';
    import SiteAdd from './components/site-add.vue';
    import SiteToolbars from './components/site-toolbars.vue';
    import { Site, SiteSearchForm } from '@smartdesk/common/types';
    import { useFeedback } from '@smartdesk/common/composables';
    import PageContainer from '../common/page-container.vue';

    defineOptions({
        name: 'Site',
    });

    const feedback = useFeedback();

    // 网站列表引用
    const siteListRef = ref<HTMLElement | null>(null);

    // 站点数据
    const siteList = ref<LabelValue[]>([]);

    // 当前选中的站点
    const site = ref<LabelValue>({
        label: '网站1',
        value: 'site_001',
    });

    // 查询表单
    const searchForm = ref<Partial<SiteSearchForm>>({
        delFlags: [0] as number[],
        orgIds: [] as number[],
        statuses: [] as number[],
        name: '',
    });

    // 被选中的网站列表
    const selectedSites = ref<Site[]>([]);

    // 模态框显隐
    const siteDialogVisible = ref<boolean>(false);

    // 当前网站
    const currentSite = ref<Partial<Site>>({});

    // 获取站点列表
    const getSiteList = async () => {
        const res = await siteApi.optionsSite();
        siteList.value = res.result;
        if (siteList.value.length) {
            site.value = siteList.value[0];
        }
    };

    // 处理工具条 add 事件
    const handleAdd = () => {
        currentSite.value = {};
        siteDialogVisible.value = true;
    };

    // 处理工具条 refresh 事件
    const handleRefresh = () => {
        (siteListRef.value as any)?.getSiteList();
    };

    // 处理查询事件
    const handleSearch = () => {
        (siteListRef.value as any)?.getSiteList();
    };

    // 处理多选事件
    const handleUpdateSelection = (data: Site[]) => {
        selectedSites.value = data;
    };

    // 处理编辑
    const handleEdit = (data: Site) => {
        currentSite.value = data;
        siteDialogVisible.value = true;
    };

    // 处理提交事件
    const handleSubmit = async (siteForm: Partial<Site>) => {
        if (siteForm.id && siteForm.code) {
            // 走更新
            const res = await siteApi.updateSite(siteForm.code, siteForm);
            if (res.code === 200) {
                feedback.success('更新网站成功');
                siteDialogVisible.value = false;
                handleSearch();
            } else {
                feedback.error('更新网站失败');
            }
        } else {
            // 走新增
            const res = await siteApi.createSite(siteForm);
            if (res.code === 200) {
                feedback.success('新增网站成功');
                siteDialogVisible.value = false;
                handleSearch();
            } else {
                feedback.error('新增网站失败');
            }
        }
    };

    // 组件挂载
    onMounted(() => {
        getSiteList();
    });
</script>
