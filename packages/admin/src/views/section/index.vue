<template>
    <page-container>
        <template #search>
            <section-search @search="handleSearchSection" />
        </template>

        <template #toolbar>
            <section-toolbars
                :sectionSelection="sectionSelection"
                @handleUpload="handleSectionUpload"
                @handleExport="handleSectionExport"
                @handleAdd="handleSectionAdd"
                @refreshSectionList="refreshSectionList" />
        </template>

        <template #list>
            <section-list
                ref="sectionListRef"
                :sectionForm="sectionSearchForm"
                @update:selection="handleSelectionSection"
                @edit-section="handleSectionEdit"
                @copy-section="handleSectionCopy" />
        </template>
    </page-container>

    <section-dialog
        v-if="sectionDialogVisible"
        v-model:modelValue="sectionDialogVisible"
        :mode="sectionDialogMode"
        :section="section"
        @submit="handleSectionSubmit" />

    <file-upload
        v-if="sectionImportDialogVisible"
        v-model:model-value="sectionImportDialogVisible"
        title="上传楼层定义包"
        :subtitle="siteStore?.currentSite?.name"
        accept=".zip,.rar"
        :max-size="20"
        tip-text="请上传zip/rar格式的楼层定义包文件，文件大小不能超过20MB"
        :upload-function="handleSectionUploadFile"
        @upload-success="handleSectionUploadSuccess"
        @upload-error="handleSectionUploadError" />
</template>

<script lang="ts" setup>
    import { ref } from 'vue';
    import SectionSearch from './components/section-search.vue';
    import SectionList from './components/section-list.vue';
    import sectionToolbars from './components/section-toolbars.vue';
    import { Section, SectionSearchForm } from '@smartdesk/common/types';
    import { sectionApi } from '@smartdesk/common/api';
    import SectionDialog from './components/section-dialog.vue';
    import { useFeedback, useJumpToDesigner } from '@smartdesk/common/composables';
    import { extractDimensions } from '@smartdesk/common/utils';
    import { useSiteStore } from '@smartdesk/common/stores';
    import PageContainer from '../common/page-container.vue';

    // 组件名称：Section
    defineOptions({
        name: 'Section',
    });

    // pinia store
    const siteStore = useSiteStore();
    const { jumpToDesigner } = useJumpToDesigner();
    const feedback = useFeedback();

    // 列表组件引用
    const sectionListRef = ref<InstanceType<typeof SectionList> | null>(null);

    // 当前楼层定义
    const section = ref<Section>({} as Section);

    // 楼层查询表单
    const sectionSearchForm = ref<Partial<SectionSearchForm>>({});

    // 已选中的楼层定义列表
    const sectionSelection = ref<Section[]>([]);

    // 楼层对话框显隐
    const sectionDialogVisible = ref(false);

    // 楼层定义弹窗模式
    const sectionDialogMode = ref<'create' | 'update' | 'copy'>('create');

    // 导入楼层对话框
    const sectionImportDialogVisible = ref(false);

    // 中转楼层定义查询表单
    const handleSearchSection = (from: Partial<SectionSearchForm>) => {
        sectionSearchForm.value = { ...from };
    };

    // 选择楼层定义
    const handleSelectionSection = (selection: Section[]) => {
        sectionSelection.value = selection;
    };

    // 新增楼层定义
    const handleSectionAdd = () => {
        sectionDialogVisible.value = true;
        sectionDialogMode.value = 'create';
        section.value = {} as Section;
    };

    // 导入楼层定义
    const handleSectionUpload = () => {
        sectionImportDialogVisible.value = true;
    };

    // 导出楼层定义
    const handleSectionExport = async () => {
        if (await feedback.confirm('确定要批量导出楼层定义吗？', '确认操作', 'warning')) {
            const blob = await sectionApi.exportSectionPackage(
                siteStore.currentSiteCode,
                sectionSelection.value.map((item) => item.code)
            );

            // 创建浏览器下载行为
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `sections.zip`;
            a.click();

            // 清理资源
            URL.revokeObjectURL(url);
            feedback.success(`组件导出成功`);

            refreshSectionList();
        }
    };

    // 处理上传楼层定义
    const handleSectionUploadFile = (file: File): Promise<any> => {
        return sectionApi.uploadSectionPackage(file, siteStore.currentSiteCode);
    };

    // 上传成功
    const handleSectionUploadSuccess = () => {
        feedback.success('楼层定义上传成功');
        sectionImportDialogVisible.value = false;
        refreshSectionList();
    };

    // 上传失败
    const handleSectionUploadError = (errorMsg: string) => {
        feedback.error('楼层定义上传失败：' + errorMsg);
    };

    // 处理编辑楼层定义
    const handleSectionEdit = (data: Section) => {
        sectionDialogVisible.value = true;
        sectionDialogMode.value = 'update';
        section.value = data;
    };

    // 处理复制楼层定义
    const handleSectionCopy = (data: Section) => {
        sectionDialogVisible.value = true;
        sectionDialogMode.value = 'copy';
        section.value = data;
    };

    // 确认处理楼层定义弹窗提交
    const handleSectionSubmit = async (data: Section) => {
        switch (sectionDialogMode.value) {
            case 'create':
                await handleSectionSubmitAdd(data);
                break;
            case 'update':
                await handleSectionSubmitUpdate(data);
                break;
            case 'copy':
                await handleSectionSubmitCopy(data);
                break;
        }
    };

    // 新增楼层定义提交
    const handleSectionSubmitAdd = async (data: Section) => {
        const rect = extractDimensions(data.resolution ?? '');
        const section = {
            siteCode: siteStore.currentSiteCode,
            name: data.name,
            resolution: data.resolution,
            type: data.type,
            layout: {
                layout: {
                    rect: {
                        top: 0,
                        left: 0,
                        width: rect ? rect.width : 0,
                        height: rect ? rect.height : 0,
                    },
                },
                props: {},
            },
        };
        Object.assign(data, section);
        const response = await sectionApi.addSection(data);
        if (response && response.code === 200) {
            feedback.success('新增楼层定义成功');
            refreshSectionList();
            sectionDialogVisible.value = false;
            jumpToDesigner('section', response?.result?.code);
        } else {
            feedback.error('新增楼层定义失败：' + response.msg);
        }
    };

    // 更新楼层定义提交
    const handleSectionSubmitUpdate = async (data: Section) => {
        const response = await sectionApi.updateSection(data.code, data);
        if (response?.code === 200) {
            refreshSectionList();
            sectionDialogVisible.value = false;
            feedback.success('更新楼层定义成功');
        } else {
            feedback.error('更新楼层定义失败：' + response.msg);
        }
    };

    // 复制楼层定义提交
    const handleSectionSubmitCopy = async (data: Section) => {
        const response = await sectionApi.copySection(data);
        if (response?.code === 200) {
            refreshSectionList();
            sectionDialogVisible.value = false;
            feedback.success('复制楼层定义成功');
        } else {
            feedback.error('复制楼层定义失败：' + response.msg);
        }
    };

    // 刷新楼层定义列表
    const refreshSectionList = () => {
        sectionListRef.value?.findSectionList();
    };
</script>
