<template>
    <BaseTableWithPagination
        :total="totalElements"
        :current-page="currentPage"
        :page-size="pageSize"
        @update:currentPage="handleCurrentChange"
        @update:pageSize="handleSizeChange">
        <template #table>
            <el-table
                :data="sectionList"
                row-key="id"
                :header-cell-style="{ color: 'black', height: '50px' }"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="60" label="全选" />
                <el-table-column property="name" label="名称" width="160" show-overflow-tooltip />
                <el-table-column property="code" label="编码" width="160" show-overflow-tooltip />
                <el-table-column property="type" label="类型" width="80">
                    <template #default="{ row }">
                        <el-tag v-if="row.type" type="success">
                            {{ enumStore.getLabelByKeyAndValue('sectionType', row.type) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="resolution" label="分辨率" width="140">
                    <template #default="{ row }">
                        <el-tag type="primary"
                            >{{ enumStore.getLabelByKeyAndValue('resolution', row.resolution) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="orderNo" label="顺序" width="80" />
                <el-table-column label="预览图" width="150">
                    <template #default="{ row }">
                        <el-image
                            :src="row.icon"
                            :preview-src-list="[row.icon]"
                            hide-on-click-modal
                            preview-teleported
                            style="width: 100px; height: 50px">
                            <template #error>
                                <image-error-fallback text="图片损坏" />
                            </template>
                        </el-image>
                    </template>
                </el-table-column>
                <el-table-column property="orgId" label="组织" width="140">
                    <template #default="{ row }">
                        <div>
                            {{ orgOptions.find((org) => org.value === row.orgId)?.label }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="可用状态" width="120">
                    <template #default="{ row }">
                        <el-switch
                            v-if="row.status === 1"
                            :disabled="
                                !canDisable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SECTION.DISABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                        <el-switch
                            v-else
                            :disabled="
                                !canEnable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SECTION.ENABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                    </template>
                </el-table-column>
                <el-table-column label="审核状态" width="100">
                    <template #default="{ row }">
                        <div>
                            {{ enumStore.getLabelByKeyAndValue('auditStatus', row.auditStatus) }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="修改时间" width="180">
                    <template #default="{ row }">
                        {{ row.modifiedTime ? format(row.modifiedTime, 'yyyy-MM-dd HH:mm:ss') : '' }}
                    </template>
                </el-table-column>
                <el-table-column property="modifiedBy" label="修改人" width="140" />
                <el-table-column label="操作" min-width="280" fixed="right">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-button type="primary" text @click="onClickDesign(row)"> 配置 </el-button>
                            <el-button
                                :disabled="
                                    !canAudit(row) ||
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SECTION.AUDIT, {
                                        type: 'org',
                                        value: row.orgId,
                                    })
                                "
                                type="primary"
                                text
                                @click="onClickAudit(row)">
                                送审
                            </el-button>
                            <el-button
                                :disabled="
                                    !canEdit(row) ||
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SECTION.EDIT, {
                                        type: 'org',
                                        value: row.orgId,
                                    })
                                "
                                type="warning"
                                text
                                @click="onClickEdit(row)">
                                编辑
                            </el-button>
                            <el-dropdown class="ml-3" :hide-on-click="false">
                                <el-button link type="primary">
                                    更多
                                    <el-icon class="el-icon--right">
                                        <ArrowDown />
                                    </el-icon>
                                </el-button>
                                <template #dropdown>
                                    <el-dropdown-menu>
                                        <el-dropdown-item>
                                            <el-button
                                                :disabled="
                                                    !canDelete(row) ||
                                                    !permissionStore.hasPermission(
                                                        ADMIN_BIZ_PERMISSION.SECTION.DELETE,
                                                        {
                                                            type: 'org',
                                                            value: row.orgId,
                                                        }
                                                    )
                                                "
                                                type="danger"
                                                link
                                                @click="onClickDelete(row)">
                                                删除
                                            </el-button>
                                        </el-dropdown-item>
                                        <el-dropdown-item>
                                            <el-button
                                                :disabled="
                                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SECTION.COPY, {
                                                        type: 'org',
                                                        value: row.orgId,
                                                    })
                                                "
                                                link
                                                type="primary"
                                                @click="onClickCopy(row)">
                                                复制
                                            </el-button>
                                        </el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </BaseTableWithPagination>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { Section } from '@smartdesk/common/types';
    import { format } from 'date-fns';
    import { dimensionApi, publishApi, sectionApi } from '@smartdesk/common/api';
    import { useFeedback, useJumpToDesigner, useRouteReturnHandler } from '@smartdesk/common/composables';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';
    import { LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import {
        ADMIN_BIZ_PERMISSION,
        canAudit,
        canDelete,
        canDisable,
        canEdit,
        canEnable,
    } from '@smartdesk/common/permission';
    import { ArrowDown } from '@element-plus/icons-vue';

    // 参数
    const props = defineProps<{
        sectionForm: any;
    }>();

    // 事件
    const emits = defineEmits(['update:selection', 'editSection', 'copySection']);

    // pinia store
    const enumStore = useEnumStore();
    const { jumpToDesigner } = useJumpToDesigner();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // useRouteReturnHandler，state 状态默认为 true
    useRouteReturnHandler<boolean>({
        targetRoute: '/section',
        initialState: true,
        onReturn: (state) => {
            if (state.value) {
                // 返回时，如果 state 状态为 true，则刷新数据
                findSectionList();
            }
        },
    });

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);

    // 楼层定义列表
    const sectionList = ref<Section[] | any>([]);

    // 选中的楼层
    const layoutSelection = ref<Section[]>([]);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 处理页面变化
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1;
        findSectionList();
    };

    // 处理当前页变化
    const handleCurrentChange = (val: number) => {
        currentPage.value = val;
        findSectionList();
    };

    // 处理选中楼层定义
    const handleSelectionChange = (val: Section[]) => {
        layoutSelection.value = val;
        emits('update:selection', val);
    };

    // 启用/禁用楼层定义
    const handleStatusChange = async (row: Section) => {
        const newStatus = row.status;
        const originalStatus = newStatus === 1 ? 0 : 1;
        const confirmed = await feedback.confirm(
            `确定要${newStatus === 0 ? '不可用' : '可用'}该楼层吗？`,
            '确定操作',
            'warning'
        );
        if (confirmed) {
            if (newStatus === 0) {
                await handleDisableSite(row.code);
            } else {
                await handleEnableSite(row.code);
            }
        } else {
            // 用户取消，恢复原状态
            row.status = originalStatus;
        }
    };

    // 不可用楼层
    const handleDisableSite = async (code: string) => {
        try {
            const response = await sectionApi.disableSection(code);
            if (response.code === 200) {
                feedback.success('不可用成功');
                await findSectionList();
            } else {
                feedback.error('不可用失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('不可用失败');
        }
    };

    // 可用楼层
    const handleEnableSite = async (code: string) => {
        try {
            const response = await sectionApi.enableSection(code);
            if (response.code === 200) {
                feedback.success('可用成功');
                await findSectionList();
            } else {
                feedback.error('可用失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('可用失败');
        }
    };

    // 点击编辑
    const onClickEdit = (row: Section) => {
        emits('editSection', row);
    };

    // 跳转楼层定义设计器
    const onClickDesign = (row: Section) => {
        jumpToDesigner('section', row.code);
    };

    // 点击送审
    const onClickAudit = async (row: Section) => {
        if (await feedback.confirm('确定要送审该楼层定义吗？', '确认操作', 'warning')) {
            const res = await publishApi.publishSelf('Section', row.code, 'CREATE');
            if (res.code === 200) {
                feedback.success('送审楼层定义成功');
                await findSectionList();
            } else {
                feedback.error('送审楼层定义失败：' + res.msg);
            }
        }
    };

    // 点击删除
    const onClickDelete = async (row: Section) => {
        if (await feedback.confirm(`确定要删除该楼层定义吗？`, '确认操作', 'warning')) {
            const res = await publishApi.publishSelf('Section', row.code, 'DELETE');
            if (res.code === 200) {
                feedback.success('删除楼层定义成功');
                await findSectionList();
            } else {
                feedback.success('删除楼层定义失败：' + res.msg);
            }
        }
    };

    // 点击复制
    const onClickCopy = async (row: Section) => {
        emits('copySection', row);
    };

    // 获取楼层定义列表
    const findSectionList = async () => {
        const response = await sectionApi.findLayoutPage(props.sectionForm, {
            page: currentPage.value - 1,
            size: pageSize.value,
            sort: 'orderNo,asc;id,desc',
        });
        sectionList.value = response.result;
        totalElements.value = Number(response.page.totalElements);
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    watch(
        () => props.sectionForm,
        () => {
            findSectionList();
        },
        { deep: true }
    );

    onMounted(() => {
        getOrgOptions();
    });

    // 公开方法给父组件调用
    defineExpose({
        findSectionList,
    });
</script>
