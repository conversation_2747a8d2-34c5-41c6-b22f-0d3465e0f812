<template>
    <icon-text-button
        :disabled="!permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.SECTION.CREATE)"
        :icon="Plus"
        text="新建楼层"
        color="#23bcca"
        @click="handleAdd" />
    <icon-text-button
        :disabled="!permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.COMPONENT.IMPORT)"
        :icon="Upload"
        text="导入楼层"
        color="#66cccc"
        @click="handleUpload" />
    <icon-text-button
        :disabled="
            !canBatchExport(sectionSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.COMPONENT.EXPORT, {
                type: 'org',
                value: sectionSelection.filter((section) => section.orgId).map((section) => section.orgId),
            })
        "
        :icon="Download"
        text="导出楼层"
        color="#66cccc"
        @click="handleExport" />
    <icon-text-button
        :disabled="
            !canBatchEnable(sectionSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.SECTION.BATCH_ENABLE, {
                type: 'org',
                value: sectionSelection.filter((section) => section.orgId).map((section) => section.orgId),
            })
        "
        :icon="CircleCheck"
        text="批量启用"
        color="#23bcca"
        @click="handleBatchEnable" />
    <icon-text-button
        :disabled="
            !canBatchDisable(sectionSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.SECTION.BATCH_DISABLE, {
                type: 'org',
                value: sectionSelection.filter((section) => section.orgId).map((section) => section.orgId),
            })
        "
        :icon="CircleClose"
        text="批量禁用"
        color="#ff9f25"
        @click="handleBatchDisEnable" />
    <icon-text-button
        :disabled="
            !canBatchAudit(sectionSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.SECTION.BATCH_AUDIT, {
                type: 'org',
                value: sectionSelection.filter((section) => section.orgId).map((section) => section.orgId),
            })
        "
        :icon="Promotion"
        text="批量送审"
        color="#23bcca"
        @click="handleBatchAudit" />
    <icon-text-button
        :disabled="
            !canBatchDelete(sectionSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.SECTION.BATCH_DELETE, {
                type: 'org',
                value: sectionSelection.filter((section) => section.orgId).map((section) => section.orgId),
            })
        "
        :icon="Delete"
        text="批量删除"
        color="#ed5665"
        @click="handleBatchDelete" />
</template>

<script setup lang="ts">
    import { CircleCheck, CircleClose, Delete, Download, Plus, Promotion, Upload } from '@element-plus/icons-vue';
    import { Section } from '@smartdesk/common/types';
    import { publishApi, sectionApi } from '@smartdesk/common/api';
    import {
        ADMIN_BIZ_PERMISSION,
        canBatchAudit,
        canBatchDelete,
        canBatchDisable,
        canBatchEnable,
        canBatchExport,
    } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';
    import { computed } from 'vue';

    // 参数
    const props = defineProps<{
        sectionSelection: Section[];
    }>();

    // 事件
    const emits = defineEmits(['handleUpload', 'handleExport', 'handleAdd', 'refreshSectionList']);

    // pinia store
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 已选中楼层定义的编码列表
    const codes = computed(() => {
        return props.sectionSelection.map((item) => item.code);
    });

    // 批量送审
    const handleBatchAudit = async () => {
        if (await feedback.confirm('确定要批量送审楼层定义吗？', '确认操作', 'warning')) {
            const res = await publishApi.batchPublishSelf('Section', codes.value, 'CREATE');
            if (res.code === 200) {
                feedback.success('批量送审成功');
                refreshSectionList();
            } else {
                feedback.error('批量送审失败：' + res.msg);
            }
        }
    };

    // 批量删除
    const handleBatchDelete = async () => {
        if (await feedback.confirm(`确定要批量删除楼层定义吗？`, '确认操作', 'warning')) {
            const response = await publishApi.batchPublishSelf('Section', codes.value, 'DELETE');
            if (response.code === 200) {
                feedback.success('批量删除成功');
                refreshSectionList();
            } else {
                feedback.error('批量删除失败：' + response.msg);
            }
        }
    };

    // 批量启用
    const handleBatchEnable = async () => {
        if (await feedback.confirm(`确定要批量启用楼层定义吗？`, '确认操作', 'warning')) {
            const response = await sectionApi.batchEnableSection(codes.value);
            if (response.code === 200) {
                feedback.success('批量启用成功');
                refreshSectionList();
            } else {
                feedback.error('批量启用失败：' + response.msg);
            }
        }
    };

    // 批量禁用
    const handleBatchDisEnable = async () => {
        if (await feedback.confirm(`确定要批量禁用楼层定义吗？`, '确认操作', 'warning')) {
            const response = await sectionApi.batchDisableSection(codes.value);
            if (response.code === 200) {
                feedback.success('批量禁用成功');
                refreshSectionList();
            } else {
                feedback.error('批量禁用失败：' + response.msg);
            }
        }
    };

    // 新建楼层
    const handleAdd = () => {
        emits('handleAdd');
    };

    // 导入楼层
    const handleUpload = () => {
        emits('handleUpload');
    };

    // 导出楼层
    const handleExport = () => {
        emits('handleExport');
    };

    // 刷新列表
    const refreshSectionList = () => {
        emits('refreshSectionList');
    };
</script>
