<template>
    <el-dialog v-model="visible" width="40%" @close="handleClose">
        <template #header>
            <span class="dialog-title">{{ getModeDesc() }}</span>
            {{ getSiteName() }}
        </template>

        <el-form ref="formRef" :model="sectionForm" :rules="rules" label-width="auto" label-suffix=":">
            <el-form-item label="名称" prop="name">
                <el-input v-model="sectionForm.name" placeholder="请输入楼层名称" />
            </el-form-item>
            <el-form-item label="编码" prop="code">
                <el-input v-model="sectionForm.code" placeholder="请输入楼层编码" />
            </el-form-item>
            <el-form-item label="类型" prop="type">
                <el-select
                    :disabled="mode === 'copy'"
                    v-model="sectionForm.type"
                    placeholder="请选择"
                    size="default"
                    clearable>
                    <el-option
                        v-for="item in sectionTypeOption"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="分辨率" prop="resolution">
                <el-select
                    :disabled="mode === 'copy'"
                    v-model="sectionForm.resolution"
                    placeholder="请选择"
                    size="default"
                    clearable>
                    <el-option
                        v-for="item in resolutionOption"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="顺序" prop="orderNo">
                <el-input-number
                    v-model="sectionForm.orderNo"
                    style="width: 100%"
                    placeholder="请输入"
                    :min="0"
                    clearable />
            </el-form-item>
            <el-form-item label="组织" prop="orgId">
                <dimension-selector
                    v-model="sectionForm.orgId"
                    placeholder="请选择组织"
                    :data="orgTree"
                    label-key="name"
                    value-key="id"
                    width="370" />
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import { computed, onMounted, ref, watch } from 'vue';
    import { FormInstance } from 'element-plus';
    import { Dimension, Section, ValidateCodeForm, ValidateNameForm } from '@smartdesk/common/types';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { dimensionApi } from '@smartdesk/common/api';
    import { codeDuplicateValidator, nameDuplicateValidator } from '@smartdesk/common/utils';

    // 楼层定义弹框
    defineOptions({
        name: 'SectionDialog',
    });

    // 参数
    const props = defineProps<{
        modelValue: boolean;
        mode: string;
        section: Partial<Section>;
    }>();

    // 事件
    const emit = defineEmits(['update:modelValue', 'update:section', 'submit']);

    // pinia store
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // 控制弹窗显示
    const visible = computed({
        get: () => props.modelValue,
        set: (value) => {
            emit('update:modelValue', value);
        },
    });

    // 楼层定义表单
    const sectionForm = ref<Partial<Section>>({});

    // 表单引用
    const formRef = ref<FormInstance | null>(null);

    // 表单校验规则
    const rules = {
        name: [
            { required: true, message: '请输入楼层名称', trigger: 'blur' },
            {
                validator: nameDuplicateValidator(() => {
                    const validateNameForm: Partial<ValidateNameForm> = {
                        entityType: 'Section',
                        siteCode: sectionForm.value.siteCode,
                        name: sectionForm.value.name,
                    };
                    if (props.mode !== 'add') {
                        validateNameForm.excludeId = sectionForm.value.id;
                    }
                    return validateNameForm;
                }),
                trigger: ['blur', 'change'],
            },
        ],
        code: [
            { required: true, message: '请输入楼层编码', trigger: 'blur' },
            {
                validator: codeDuplicateValidator(() => {
                    const validateCodeForm: Partial<ValidateCodeForm> = {
                        entityType: 'Section',
                        siteCode: sectionForm.value.siteCode,
                        code: sectionForm.value.code,
                    };
                    if (props.mode !== 'add') {
                        validateCodeForm.excludeId = sectionForm.value.id;
                    }
                    return validateCodeForm;
                }),
                trigger: ['blur', 'change'],
            },
        ],
        resolution: [{ required: true, message: '请选择分辨率', trigger: 'blur' }],
        type: [{ required: true, message: '请选择类型', trigger: 'blur' }],
    };

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 分辨率枚举
    const resolutionOption = ref<Enumeration[]>(enumStore.getEnumsByKey('resolution') || []);

    // 楼层定义类型枚举
    const sectionTypeOption = ref<Enumeration[]>(enumStore.getEnumsByKey('sectionType') || []);

    // 根据 mode 获取 desc
    const getModeDesc = () => {
        switch (props.mode) {
            case 'create':
                return '新增楼层';
            case 'update':
                return '编辑楼层';
            case 'copy':
                return '复制楼层';
            default:
                return '';
        }
    };

    // 关闭对话框
    const handleClose = () => {
        emit('update:modelValue', false);
    };

    // 获取网站名称
    const getSiteName = () => {
        return siteStore.currentSite?.name;
    };

    // 提交表单
    const handleSubmit = () => {
        formRef.value?.validate((valid) => {
            if (valid) {
                emit('submit', sectionForm.value);
            }
        });
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 监听 section 变化，填充表单
    watch(
        () => props.section,
        (newSection) => {
            sectionForm.value = { ...newSection };
        },
        { immediate: true }
    );

    onMounted(() => {
        getOrgTree();
    });
</script>

<style scoped>
    .dialog-title {
        font-size: 18px;
    }
</style>
