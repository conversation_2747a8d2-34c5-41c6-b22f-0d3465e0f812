<template>
    <div class="content-style">
        <div class="button-style">
            <icon-text-button :icon="Plus" text="新建" color="#23bcca" @click="handleAdd" />
        </div>
        <div class="body-style">
            <el-table :data="tableData" row-key="id" :header-cell-style="{ color: 'black', height: '50px' }">
                <el-table-column prop="name" label="名称" />
                <el-table-column prop="code" label="编码" />
                <el-table-column prop="orderNo" label="排序" width="80" />
                <el-table-column label="操作" width="150" fixed="right">
                    <template #default="{ row }">
                        <el-button type="primary" link :icon="Edit" @click="handleEdit(row)"> 编辑 </el-button>
                        <el-button type="danger" link :icon="Delete" @click="handleDelete(row)"> 删除 </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-dialog
                v-model="dialogVisible"
                :title="dialogType === 'add' ? `新增${groupLabel}` : `编辑${groupLabel}`"
                width="500px">
                <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" label-suffix=":">
                    <el-form-item label="名称" prop="name">
                        <el-input v-model="form.name" />
                    </el-form-item>
                    <el-form-item label="编码" prop="code">
                        <el-input v-model="form.code" :disabled="dialogType === 'edit'" />
                    </el-form-item>
                    <el-form-item label="排序" prop="orderNo">
                        <el-input-number v-model="form.orderNo" :min="0" style="width: 100%" />
                    </el-form-item>
                </el-form>
                <template #footer>
                    <el-button @click="handleCancel">取消</el-button>
                    <el-button type="primary" @click="handleSubmit">确定 </el-button>
                </template>
            </el-dialog>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref } from 'vue';
    import { Enumeration } from '@chances/portal_common_core';
    import { enumApi } from '@smartdesk/common/api';
    import type { FormInstance, FormRules } from 'element-plus';
    import { Delete, Edit, Plus } from '@element-plus/icons-vue';
    import { useFeedback } from '@smartdesk/common/composables';

    // 定义props
    interface Props {
        enumCode: string; // 枚举类型编码
        groupLabel?: string; // 分组标签名称
        module?: string; // 模块名称
    }

    const props = withDefaults(defineProps<Props>(), {
        groupLabel: '分组',
        module: 'visualModule',
    });

    // pinia store
    const feedback = useFeedback();

    const tableData = ref<Enumeration[]>([] as Enumeration[]);

    const enumeration = ref<Enumeration>({} as Enumeration);
    // 表单相关
    const dialogVisible = ref(false);
    const dialogType = ref('add');
    const formRef = ref<FormInstance>();
    const form = ref<Enumeration>({
        id: '',
        parentId: '',
        code: '',
        name: '',
        module: props.module,
        orderNo: 0,
        children: [],
    });

    // 表单校验函数
    type ValidateCallback = (error?: Error) => void;

    const validateName = (_: any, value: any, callback: ValidateCallback) => {
        if (!value) {
            callback(new Error('请输入名称'));
            return;
        }
        const existForm = {
            parentId: form.value.parentId,
            name: value,
            id: form.value.id,
        };
        enumApi
            .checkEnum(existForm)
            .then((res) => {
                if (!res.result) {
                    callback();
                } else {
                    callback(new Error(res.msg));
                }
            })
            .catch(() => {
                callback(new Error('验证失败，请重试'));
            });
    };

    const validateCode = (_: any, value: any, callback: ValidateCallback) => {
        if (!value) {
            callback(new Error('请输入编码'));
            return;
        }
        const existForm = {
            parentId: form.value.parentId,
            code: value,
            id: form.value.id,
        };
        enumApi
            .checkEnum(existForm)
            .then((res) => {
                if (res.code === 200) {
                    callback();
                } else {
                    callback(new Error(res.msg));
                }
            })
            .catch(() => {
                callback(new Error('验证失败，请重试'));
            });
    };

    const rules: FormRules = {
        name: [{ required: true, trigger: 'blur', validator: validateName }],
        code: [{ required: true, trigger: 'blur', validator: validateCode }],
        orderNo: [
            { required: true, message: '请输入排序号', trigger: 'blur' },
            {
                type: 'number',
                min: 0,
                message: '排序号必须大于等于0',
                trigger: 'blur',
            },
        ],
    };

    // 处理新增
    const handleAdd = () => {
        dialogType.value = 'add';
        form.value = {
            id: '',
            name: '',
            code: '',
            module: props.module,
            orderNo: 0,
            parentId: enumeration.value?.id || '',
            children: [],
        };
        dialogVisible.value = true;
    };

    // 处理编辑
    const handleEdit = (row: any) => {
        dialogType.value = 'edit';
        form.value = { ...row };
        dialogVisible.value = true;
    };

    // 处理删除
    const handleDelete = async (row: any) => {
        if (await feedback.confirm(`确定要删除该分组吗？`, '确认操作', 'warning')) {
            if (row.children && row.children.length > 0) {
                feedback.error('该分组下有子分组，无法删除');
                return;
            }
            const res = await enumApi.deleteEnum(row.id);
            if (res.code === 200) {
                feedback.success('删除成功');
            }
        }
    };
    // 处理取消
    const handleCancel = () => {
        dialogVisible.value = false;
        form.value = {
            id: '',
            name: '',
            code: '',
            module: props.module,
            orderNo: 0,
            parentId: '',
            children: [],
        };
        formRef.value?.resetFields();
    };
    const handleSubmit = async () => {
        if (!formRef.value) return;

        await formRef.value.validate(async (valid, fields) => {
            if (valid) {
                try {
                    const params = {
                        ...form.value,
                        type: 'enum',
                    };
                    if (dialogType.value === 'add') {
                        const res = await enumApi.createEnum(params);
                        if (res.code === 200) {
                            feedback.success('新增成功');
                        }
                    } else {
                        const res = await enumApi.updateEnum(params);
                        if (res.code === 200) {
                            feedback.success('修改成功');
                        }
                    }
                    dialogVisible.value = false;
                    // 重新加载数据
                    await initData();
                } catch (error) {
                    console.error('保存失败:', error);
                    feedback.error('保存失败');
                }
            }
        });
    };
    // 初始化数据
    const initData = async () => {
        const res = await enumApi.getEnumByCode(props.enumCode);
        if (res.code === 200) {
            enumeration.value = res.result;
            tableData.value = enumeration.value.children;
        }
    };
    onMounted(() => {
        initData();
    });
</script>

<style scoped>
    .content-style {
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .button-style {
        background: white;
        border-radius: 10px;
        padding: 10px;
        margin-bottom: 10px;
    }

    .body-style {
        background: white;
        border-radius: 10px;
        padding: 10px;
        flex: 1;
        display: flex;
        flex-direction: column;
    }
</style>
