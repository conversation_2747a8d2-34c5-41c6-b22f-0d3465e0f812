<template>
    <div class="group-manager-container">
        <!-- 左侧树 -->
        <div class="left-panel">
            <div class="tree-header">
                <span class="title">{{ enumeration.name }}</span>
                <el-button type="primary" size="small" @click="handleAdd"> 新增{{ groupLabel }} </el-button>
            </div>
            <div class="search-box">
                <el-input
                    v-model="keyword"
                    :placeholder="`搜索${groupLabel}...`"
                    prefix-icon="Search"
                    clearable
                    @keyup.enter="handleSearch"
                    @clear="handleClear" />
            </div>
            <el-tree
                ref="treeRef"
                :data="treeData"
                :props="defaultProps"
                node-key="code"
                :highlight-current="true"
                :expand-on-click-node="false"
                @node-click="handleNodeClick">
                <template #default="{ node, data }">
                    <div class="custom-tree-node">
                        <span class="node-label">{{ node.label }}</span>
                        <span class="operation-buttons">
                            <el-button type="primary" link :icon="Edit" @click.stop="handleEdit(data)" />
                            <el-button type="danger" link :icon="Delete" @click.stop="handleDelete(data)" />
                        </span>
                    </div>
                </template>
            </el-tree>
        </div>

        <!-- 右侧内容 -->
        <div class="right-panel">
            <!-- 上部分：节点信息 -->
            <div class="node-info">
                <div class="info-header">
                    <h3>{{ groupLabel }}信息</h3>
                    <el-button
                        type="primary"
                        link
                        label-class-name="my-label"
                        :icon="Edit"
                        v-if="currentNode"
                        @click="handleEdit(currentNode)">
                        编辑
                    </el-button>
                </div>
                <div class="info-content" v-if="currentNode">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="名称" label-width="120" width="300px">
                            {{ currentNode.name }}
                        </el-descriptions-item>
                        <el-descriptions-item label="编码" label-width="120" width="300px">
                            {{ currentNode.code }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
                <el-empty v-else :description="`请选择${groupLabel}`" />
            </div>

            <!-- 下部分：子数据表格 -->
            <div class="sub-data">
                <div class="table-header">
                    <h3>子{{ groupLabel }}列表</h3>
                    <el-button type="primary" size="small" v-if="currentNode" @click="handleAddSub">
                        新增子{{ groupLabel }}
                    </el-button>
                </div>
                <el-table
                    v-loading="tableLoading"
                    :data="tableData"
                    :header-cell-style="{ color: 'black', height: '50px' }">
                    <el-table-column prop="name" label="名称" />
                    <el-table-column prop="code" label="编码" />
                    <el-table-column prop="orderNo" label="排序" width="80" />
                    <el-table-column label="操作" width="150" fixed="right">
                        <template #default="{ row }">
                            <el-button type="primary" link :icon="Edit" @click="handleEdit(row)"> 编辑 </el-button>
                            <el-button type="danger" link :icon="Delete" @click="handleDelete(row)"> 删除 </el-button>
                        </template>
                    </el-table-column>
                </el-table>
                <el-empty v-if="!tableLoading && !tableData.length" :description="`暂无子${groupLabel}数据`" />
            </div>
        </div>

        <!-- 新增/编辑对话框 -->
        <el-dialog
            v-model="dialogVisible"
            :title="dialogType === 'add' ? `新增${groupLabel}` : `编辑${groupLabel}`"
            width="500px">
            <el-form ref="formRef" :model="form" :rules="rules" label-width="80px" label-suffix=":">
                <el-form-item label="名称" prop="name">
                    <el-input v-model="form.name" />
                </el-form-item>
                <el-form-item label="编码" prop="code">
                    <el-input v-model="form.code" :disabled="dialogType === 'edit'" />
                </el-form-item>
                <el-form-item label="排序" prop="orderNo">
                    <el-input-number v-model="form.orderNo" :min="0" style="width: 100%" />
                </el-form-item>
            </el-form>
            <template #footer>
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSubmit">确定 </el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref } from 'vue';
    import { Delete, Edit } from '@element-plus/icons-vue';
    import type { FormInstance, FormRules } from 'element-plus';
    import { enumApi } from '@smartdesk/common/api';
    import { Enumeration } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';

    // 定义props
    interface Props {
        enumCode: string; // 枚举类型编码
        groupLabel?: string; // 分组标签名称
        module?: string; // 模块名称
    }

    const props = withDefaults(defineProps<Props>(), {
        groupLabel: '分组',
        module: 'visualModule',
    });

    // pinia store
    const feedback = useFeedback();

    // 树相关
    const treeRef = ref();
    const treeData = ref<Enumeration[]>([] as Enumeration[]);
    const keyword = ref('');
    const currentNode = ref<Enumeration>({} as Enumeration);
    const enumeration = ref<Enumeration>({} as Enumeration);
    const defaultProps = {
        children: 'children',
        label: 'name',
    };

    // 表单相关
    const dialogVisible = ref(false);
    const dialogType = ref('add');
    const formRef = ref<FormInstance>();
    const form = ref<Enumeration>({
        id: '',
        parentId: '',
        code: '',
        name: '',
        module: props.module,
        orderNo: 0,
        children: [],
    });

    // 表单校验函数
    type ValidateCallback = (error?: Error) => void;

    const validateName = (_: any, value: any, callback: ValidateCallback) => {
        if (!value) {
            callback(new Error('请输入名称'));
            return;
        }
        const existForm = {
            parentId: form.value.parentId,
            name: value,
            id: form.value.id,
        };
        enumApi
            .checkEnum(existForm)
            .then((res) => {
                if (!res.result) {
                    callback();
                } else {
                    callback(new Error(res.msg));
                }
            })
            .catch(() => {
                callback(new Error('验证失败，请重试'));
            });
    };

    const validateCode = (_: any, value: any, callback: ValidateCallback) => {
        if (!value) {
            callback(new Error('请输入编码'));
            return;
        }
        if (!/^[a-zA-Z0-9_]*$/.test(value)) {
            callback(new Error('编码只能包含字母、数字和下划线'));
            return;
        }
        const existForm = {
            parentId: currentNode.value?.id,
            code: value,
            id: (form.value as any).id,
        };
        enumApi
            .checkEnum(existForm)
            .then((res) => {
                if (res.code === 200) {
                    callback();
                } else {
                    callback(new Error(res.msg));
                }
            })
            .catch(() => {
                callback(new Error('验证失败，请重试'));
            });
    };

    const rules: FormRules = {
        name: [{ required: true, trigger: 'blur', validator: validateName }],
        code: [{ required: true, trigger: 'blur', validator: validateCode }],
        orderNo: [
            { required: true, message: '请输入排序号', trigger: 'blur' },
            {
                type: 'number',
                min: 0,
                message: '排序号必须大于等于0',
                trigger: 'blur',
            },
        ],
    };

    // 表格数据
    const tableData = ref<Enumeration[]>([]);
    const tableLoading = ref(false);

    // 处理节点点击
    const handleNodeClick = async (data: any) => {
        currentNode.value = data;
        await loadTableData();
    };

    // 处理搜索
    const handleSearch = () => {
        if (!keyword.value) {
            initData();
            return;
        }
        treeData.value = treeData.value.filter((item) => item.name.includes(keyword.value));
    };
    // 处理清除
    const handleClear = () => {
        initData();
    };
    // 处理新增
    const handleAdd = () => {
        dialogType.value = 'add';
        form.value = {
            id: '',
            name: '',
            code: '',
            module: props.module,
            orderNo: 0,
            parentId: enumeration.value?.id || '',
            children: [],
        };
        dialogVisible.value = true;
    };

    // 处理新增子分组
    const handleAddSub = () => {
        dialogType.value = 'add';
        form.value = {
            id: '',
            name: '',
            code: '',
            module: props.module,
            orderNo: 0,
            parentId: currentNode.value?.id || '',
            children: [],
        };
        dialogVisible.value = true;
    };

    // 处理编辑
    const handleEdit = (row: any) => {
        dialogType.value = 'edit';
        form.value = { ...row };
        dialogVisible.value = true;
    };

    // 处理删除
    const handleDelete = async (row: any) => {
        if (await feedback.confirm(`确定要删除该分组吗？`, '确认操作', 'warning')) {
            if (row.children && row.children.length > 0) {
                feedback.error('该分组下有子分组，无法删除');
                return;
            }
            const res = await enumApi.deleteEnum(row.id);
            if (res.code === 200) {
                feedback.success('删除成功');
            }
            // 重新加载数据
            await initData();
            if (currentNode.value?.code) {
                await loadTableData();
            }
        }
    };

    // 加载表格数据
    const loadTableData = async () => {
        if (!currentNode.value?.code) return;

        try {
            tableLoading.value = true;
            const res = await enumApi.childEnums(currentNode.value.id);
            tableData.value = res.result;
        } catch (error) {
            console.error('获取子分组数据失败:', error);
            feedback.error('获取子分组数据失败');
            tableData.value = [];
        } finally {
            tableLoading.value = false;
        }
    };

    // 处理取消
    const handleCancel = () => {
        dialogVisible.value = false;
        form.value = {
            id: '',
            name: '',
            code: '',
            module: props.module,
            orderNo: 0,
            parentId: '',
            children: [],
        };
        formRef.value?.resetFields();
    };

    // 处理表单提交
    const handleSubmit = async () => {
        if (!formRef.value) return;

        await formRef.value.validate(async (valid, fields) => {
            if (valid) {
                try {
                    const params = {
                        ...form.value,
                        type: 'enum',
                    };
                    if (dialogType.value === 'add') {
                        const res = await enumApi.createEnum(params);
                        if (res.code === 200) {
                            feedback.success('新增成功');
                        }
                    } else {
                        const res = await enumApi.updateEnum(params);
                        if (res.code === 200) {
                            feedback.success('修改成功');
                        }
                    }
                    dialogVisible.value = false;
                    // 重新加载数据
                    await initData();
                    if (currentNode.value?.code) {
                        await loadTableData();
                    }
                } catch (error) {
                    console.error('保存失败:', error);
                    feedback.error('保存失败');
                }
            }
        });
    };

    // 初始化数据
    const initData = async () => {
        const res = await enumApi.getEnumByCode(props.enumCode);
        if (res.code === 200) {
            enumeration.value = res.result;
            treeData.value = enumeration.value.children;
        }
    };
    onMounted(() => {
        initData();
    });

    // 暴露方法给父组件
    defineExpose({
        initData,
        loadTableData,
    });
</script>

<style scoped>
    .group-manager-container {
        height: calc(100vh - 100px);
        min-height: 800px;
        display: flex;
        gap: 20px;
        border-radius: 4px;
    }

    .left-panel {
        width: 300px;
        background: white !important;
        border-radius: 4px;
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 16px;
    }

    .tree-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0; /* 防止头部被压缩 */
    }

    .tree-header .title {
        font-size: 16px;
        font-weight: bold;
    }

    .right-panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 20px;
        min-width: 0; /* 防止溢出 */
    }

    .node-info,
    .sub-data {
        border-radius: 4px;
        background: white;
        padding: 20px;
    }

    .sub-data {
        height: 100%;
    }

    .info-header,
    .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .info-header h3,
    .table-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: bold;
    }

    .search-box {
        margin-bottom: 8px;
        flex-shrink: 0; /* 防止搜索框被压缩 */
    }

    /* 树容器样式 */
    :deep(.el-tree) {
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
        height: 0; /* 配合flex:1实现剩余空间自动占满 */
        padding-right: 8px; /* 为滚动条预留空间 */
    }

    /* 自定义滚动条样式 */
    :deep(.el-tree::-webkit-scrollbar) {
        width: 6px;
        height: 6px;
    }

    :deep(.el-tree::-webkit-scrollbar-thumb) {
        background: white;
        border-radius: 3px;
    }

    :deep(.el-tree::-webkit-scrollbar-track) {
        background: white;
    }

    :deep(.my-label) {
        background: white;
    }

    :deep(.el-tree-node__content) {
        height: 32px;
    }

    .custom-tree-node {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 8px;
    }

    .operation-buttons {
        display: none;
    }

    .custom-tree-node:hover .operation-buttons {
        display: inline-flex;
        align-items: center;
    }

    .is-bordered-label {
        background: none !important;
    }
</style>
