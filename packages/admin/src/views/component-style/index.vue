<template>
    <div class="component-style">
        {{ component.name }}
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="组件样式列表" name="first">
                <component-style-toolbars
                    :component="component"
                    :componentStyles="componentStyles"></component-style-toolbars>
                <component-style-list
                    :component="component"
                    @update:selection="handleSelection"
                    :componentStyleForm="componentStyleForm"></component-style-list>
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script lang="ts" setup>
    import { ref } from 'vue';
    import type { TabsPaneContext } from 'element-plus';
    import { Component, ComponentStyle } from '@smartdesk/common/types';
    import componentStyleList from './compoents/component-style-list.vue';
    import ComponentStyleToolbars from './compoents/compoent-style-toolbars.vue';

    defineOptions({
        name: 'ComponentStyle',
    });

    const activeName = ref<string>('first');
    const component = ref<Partial<Component>>({
        name: '山海观雾',
        code: '70',
        type: 'nisi',
        icon: 'https://picsum.photos/200/100',
        delFlag: -2,
        status: 0,
        createdTime: new Date(),
        modifiedTime: new Date(),
        modifiedBy: 'admin',
        id: 50,
        version: 66,
        tags: 'aute ea commodo',
        orgId: 56,
        createdBy: 'admin',
        styles: [
            {
                siteId: 88,
                siteCode: '59',
                componentId: 32,
                componentCode: '70',
                name: '所持划进',
                code: '67',
                type: 'dolor commodo consequat',
                category: 'base',
                layout: {},
                icon: 'http://dummyimage.com/100x100',
                delFlag: -2,
                status: 0,
                createdTime: new Date('2016-02-27 05:26:55'),
                modifiedTime: new Date('2016-02-27 05:26:55'),
                createdBy: 'dolor mollit',
                modifiedBy: 'mollit qui minim voluptate',
                id: 29,
                layoutVersion: 24,
                orgId: 43,
            },
            {
                siteId: 88,
                siteCode: '59',
                componentId: 32,
                componentCode: '70',
                name: '所持划进',
                code: '67',
                type: 'dolor commodo consequat',
                icon: 'http://dummyimage.com/100x100',
                category: 'base',
                layout: {},
                delFlag: -2,
                status: 1,
                createdTime: new Date('2016-02-27 05:26:55'),
                modifiedTime: new Date('2016-02-27 05:26:55'),
                createdBy: 'dolor mollit',
                modifiedBy: 'mollit qui minim voluptate',
                id: 29,
                layoutVersion: 24,
                orgId: 43,
            },
        ],
    });
    const componentStyles = ref<ComponentStyle[]>([]);
    const handleClick = (tab: TabsPaneContext, event: Event) => {};
    const componentStyleForm = ref({
        name: '',
    });
    // 选中的组件样式
    const handleSelection = (val: ComponentStyle[]) => {
        componentStyles.value = val;
    };
</script>

<style>
    .component-style {
        margin: 20px;
    }

    /* .demo-tabs > .el-tabs__content {
      padding: 32px;
      color: #6b778c;
      font-size: 32px;
      font-weight: 600;
    } */
</style>
