<template>
    <div class="component-toolbars">
        <icon-text-button :icon="Plus" text="新建组件样式" color="#23bcca" @click="handleAdd" />
        <icon-text-button
            :icon="CircleCheck"
            text="批量启用"
            color="#23bcca"
            :disabled="componentStyles.length == 0"
            @click="handleBatchEnable" />

        <icon-text-button
            :icon="CircleClose"
            text="批量禁用"
            color="#ff9f25"
            :disabled="componentStyles.length == 0"
            @click="handleBatchDisEnable" />

        <icon-text-button
            :icon="Delete"
            text="批量删除"
            color="#ed5665"
            :disabled="componentStyles.length == 0"
            @click="handleBatchDelete" />
    </div>
</template>

<script setup lang="ts">
    import { CircleCheck, CircleClose, Delete, Plus } from '@element-plus/icons-vue';
    import { defineComponent } from 'vue';
    import { ComponentStyle } from '@smartdesk/common/types';
    import { ElMessage, ElMessageBox } from 'element-plus';

    defineComponent({
        name: 'ComponentStyleToolbars',
    });
    const props = defineProps<{
        componentStyles: ComponentStyle[];
    }>();

    const handleAdd = () => {
        ElMessage.info('新增组件样式');
    };
    // 批量删除
    const handleBatchDelete = () => {
        ElMessageBox.confirm(`确定要批量删除组件吗？`, '确认操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(() => {
                ElMessage.success('删除成功');
            })
            .catch(() => {
                // 取消操作
                ElMessage.info('操作取消');
            });
    };
    // 批量启用
    const handleBatchEnable = () => {
        ElMessageBox.confirm(`确定要批量启用组件吗？`, '确认操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(() => {
                ElMessage.success('操作成功');
            })
            .catch(() => {
                // 取消操作
                ElMessage.info('操作取消');
            });
    };

    // 批量禁用
    const handleBatchDisEnable = () => {
        ElMessageBox.confirm(`确定要批量禁用组件吗？`, '确认操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(() => {
                ElMessage.success('操作成功');
            })
            .catch(() => {
                // 取消操作
                ElMessage.info('操作取消');
            });
    };
</script>
<style scoped>
    .component-toolbars {
        margin: 20px;
    }
</style>
