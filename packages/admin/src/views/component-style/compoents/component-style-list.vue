<template>
    <div>
        <el-table
            ref="multipleTableRef"
            :data="componentStyleList"
            row-key="id"
            @selection-change="handleSelectionChange"
            :header-cell-style="{ color: 'black', height: '50px' }">
            <el-table-column type="selection" width="70" label="全选" />
            <el-table-column property="name" label="样式名称" />
            <el-table-column label="预览图" width="240">
                <template #default="{ row }">
                    <el-image style="width: 200px; height: 100px" :src="row.icon">
                        <template #error>
                            <image-error-fallback text="图片损坏" />
                        </template>
                    </el-image>
                </template>
            </el-table-column>
            <el-table-column label="修改时间">
                <template #default="{ row }">
                    {{ row.modifiedTime ? format(row.modifiedTime, 'yyyy-MM-dd HH:mm:ss') : '' }}
                </template>
            </el-table-column>
            <el-table-column property="modifiedBy" label="修改人" />
            <el-table-column label="是否启用" width="120">
                <template #default="{ row }">
                    <el-switch
                        v-model="row.status"
                        size="small"
                        :active-value="1"
                        :inactive-value="0"
                        :inactive-text="row.status ? '启用' : '禁用'"
                        @change="handleStatusChange(row)" />
                </template>
            </el-table-column>
            <el-table-column label="操作" width="300">
                <template #default="{ row }">
                    <el-button type="primary" text @click="onEditBtn(row)">编辑</el-button>
                    <el-button type="primary" text @click="onConfigBtn(row)">配置</el-button>
                    <el-button v-if="row.status === 0" type="primary" text @click="onDisableBtn(row)">启用</el-button>
                    <el-button v-if="row.status === 1" type="primary" text @click="onUnDisableBtn(row)">禁用</el-button>
                    <el-button type="danger" text @click="onDeleteBtn(row)"> 删除 </el-button>
                </template>
            </el-table-column>
        </el-table>
    </div>
    <div>
        <pagination
            :total="totalElements"
            :current-page-value="currentPage"
            @update:currentPageValue="handleCurrentChange"
            @update:pageSizeValue="handleSizeChange" />
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { Component, ComponentStyle } from '@smartdesk/common/types';
    import { ElMessage, ElMessageBox } from 'element-plus';
    import { format } from 'date-fns';
    import { componentStyleApi } from '@smartdesk/common/api';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';

    const props = defineProps<{
        componentStyleForm: any;
    }>();

    const emits = defineEmits(['update:selection']);

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1; // 切换 pageSize 需要回到第一页
        findComponentStyleList();
    };

    const handleCurrentChange = (val: number) => {
        currentPage.value = val;
        findComponentStyleList();
    };

    const componentStyleList = ref<any>();

    // 选中的组件样式
    const componentStyleSelection = ref<ComponentStyle[]>([]);
    const handleSelectionChange = (val: ComponentStyle[]) => {
        componentStyleSelection.value = val;
        // 触发事件，将选中的数据传给父组件
        emits('update:selection', val);
    };
    const findComponentStyleList = async () => {
        try {
            const response = await componentStyleApi.findComponentStylePage(props.componentStyleForm, {
                page: currentPage.value - 1,
                size: pageSize.value,
            });
            componentStyleList.value = response.result;
            totalElements.value = Number(response.page.totalElements);
        } catch (error) {
            ElMessage.error('获取楼层数据失败');
            console.error(error);
        }
    };

    // 启用/禁用组件
    const handleStatusChange = (row: ComponentStyle) => {
        ElMessageBox.confirm(`确定要${row.status === 0 ? '禁用' : '启用'}该组件吗？`, '确认操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(() => {
                ElMessage.success('状态更新成功');
            })
            .catch(() => {
                // 取消操作
                ElMessage.info('操作取消');
                row.status = row.status === 1 ? 0 : 1;
            });
    };

    // 编辑按钮
    const onEditBtn = (row: ComponentStyle) => {
        ElMessage.success('执行编辑操作');
    };
    // 配置按钮
    const onConfigBtn = (row: ComponentStyle) => {
        ElMessage.success('执行配置操作');
    };

    // 启用
    const onDisableBtn = (row: ComponentStyle) => {
        ElMessageBox.confirm(`确定要启用该组件样式吗？`, '确认操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(() => {
                row.status = row.status === 1 ? 0 : 1;
                ElMessage.success('启用成功');
            })
            .catch(() => {
                // 取消操作
                ElMessage.info('操作取消');
            });
    };

    // 禁用
    const onUnDisableBtn = (row: ComponentStyle) => {
        ElMessageBox.confirm(`确定要禁用该组件样式吗？`, '确认操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(() => {
                row.status = row.status === 1 ? 0 : 1;
                ElMessage.success('禁用成功');
            })
            .catch(() => {
                // 取消操作
                ElMessage.info('操作取消');
            });
    };
    // 删除
    const onDeleteBtn = (row: Component) => {
        ElMessageBox.confirm(`确定要删除该组件样式吗？`, '确认操作', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
            .then(() => {
                ElMessage.success('删除成功');
            })
            .catch(() => {
                // 取消操作
                ElMessage.info('操作取消');
            });
    };
    watch(
        () => props.componentStyleForm,
        () => {
            findComponentStyleList();
        },
        { deep: true }
    );

    // 组件挂载时请求数据
    onMounted(() => {
        findComponentStyleList();
    });
</script>

<style scoped></style>
