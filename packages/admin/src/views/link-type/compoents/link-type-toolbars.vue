<template>
    <icon-text-button
        :disabled="!permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.LINK_TYPE.CREATE)"
        :icon="Plus"
        text="新建跳转链接"
        color="#23bcca"
        @click="onClickAdd" />
    <icon-text-button
        :disabled="
            !canBatchEnable(selectedLinkType) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.LINK_TYPE.BATCH_ENABLE, {
                type: 'org',
                value: selectedLinkType.filter((linkType) => linkType.orgId).map((linkType) => linkType.orgId),
            })
        "
        :icon="CircleCheck"
        text="批量启用"
        color="#23bcca"
        @click="onClickBatchEnable" />
    <icon-text-button
        :disabled="
            !canBatchDisable(selectedLinkType) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.LINK_TYPE.BATCH_DISABLE, {
                type: 'org',
                value: selectedLinkType.filter((linkType) => linkType.orgId).map((linkType) => linkType.orgId),
            })
        "
        :icon="CircleClose"
        text="批量禁用"
        color="#ff9f25"
        @click="onClickBatchDisable" />
    <icon-text-button
        :disabled="
            !canBatchDeleteByAdmin(selectedLinkType) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.LINK_TYPE.BATCH_DELETE, {
                type: 'org',
                value: selectedLinkType.filter((linkType) => linkType.orgId).map((linkType) => linkType.orgId),
            })
        "
        :icon="Delete"
        text="批量删除"
        color="#ed5665"
        @click="onClickBatchDelete" />
</template>

<script setup lang="ts">
    import { CircleCheck, CircleClose, Delete, Plus } from '@element-plus/icons-vue';
    import { LinkType } from '@smartdesk/common/types';
    import { linkTypeApi } from '@smartdesk/common/api';
    import {
        ADMIN_BIZ_PERMISSION,
        canBatchDeleteByAdmin,
        canBatchDisable,
        canBatchEnable,
    } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';
    import { computed } from 'vue';

    // 参数
    const props = defineProps<{
        selectedLinkType: LinkType[];
    }>();

    // 事件
    const emit = defineEmits(['add', 'refresh']);

    // pinia store
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 已选中跳转链接的编码列表引用
    const codes = computed(() => props.selectedLinkType.map((item: LinkType) => item.code));

    // 新建网站
    const onClickAdd = () => {
        emit('add');
    };

    // 点击批量启用
    const onClickBatchEnable = async () => {
        if (await feedback.confirm('确定要批量启用跳转链接吗？', '确认操作', 'warning')) {
            const response = await linkTypeApi.batchEnableLinkType(codes.value);
            if (response.code === 200) {
                feedback.success('批量启用成功');
                refresh();
            } else {
                feedback.error('批量启用失败：' + response.msg);
            }
        }
    };

    // 点击批量禁用
    const onClickBatchDisable = async () => {
        if (await feedback.confirm('确定要批量禁用跳转链接吗？', '确认操作', 'warning')) {
            const response = await linkTypeApi.batchDisableLinkType(codes.value);
            if (response.code === 200) {
                feedback.success('批量禁用成功');
                refresh();
            } else {
                feedback.error('批量禁用失败：' + response.msg);
            }
        }
    };

    // 点击批量删除
    const onClickBatchDelete = async () => {
        if (await feedback.confirm('确定要批量删除跳转链接吗？', '确认操作', 'warning')) {
            const response = await linkTypeApi.batchDeleteLinkType(codes.value);
            if (response.code === 200) {
                feedback.success('批量删除成功');
                refresh();
            } else {
                feedback.error('批量删除失败：' + response.msg);
            }
        }
    };

    // 刷新列表
    const refresh = () => {
        emit('refresh');
    };
</script>
