<template>
    <el-dialog
        :title="isEdit ? '编辑跳转路径' : '新增跳转路径'"
        v-model="visible"
        width="50%"
        append-to-body
        :z-index="10"
        @close="handleClose">
        <el-form ref="linkTypeFormRef" :model="linkTypeForm" :rules="rules" label-width="120px" label-suffix=":">
            <el-form-item label="跳转类型" prop="code">
                <el-input v-model="linkTypeForm.code" placeholder="请输入跳转类型" :disabled="isEdit" />
            </el-form-item>
            <el-form-item label="跳转名称" prop="name">
                <el-input v-model="linkTypeForm.name" placeholder="请输入跳转名称" />
            </el-form-item>
            <el-form-item label="数据类型" prop="dataType">
                <el-select v-model="linkTypeForm.dataType" placeholder="数据类型">
                    <el-option v-for="item in dataTypeOptions" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="网站" prop="siteCode">
                <el-select v-model="linkTypeForm.siteCode" placeholder="请选择网站" clearable>
                    <el-option v-for="item in siteOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="分类" prop="genre">
                <el-input v-model="linkTypeForm.genre" placeholder="请输入分类" />
            </el-form-item>
            <el-form-item label="顺序" prop="orderNo">
                <el-input-number v-model="linkTypeForm.orderNo" :min="1" placeholder="请输入顺序" style="width: 100%" />
            </el-form-item>
            <el-form-item label="使用说明" prop="summary">
                <el-input v-model="linkTypeForm.summary" placeholder="请输入使用说明" type="text" />
            </el-form-item>
            <el-form-item label="业务分组" prop="bizGroup">
                <el-select v-model="linkTypeForm.bizGroups" placeholder="请选择" size="default" clearable>
                    <el-option v-for="item in bizGroupOption" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="组织" prop="orgId">
                <dimension-selector
                    v-model="linkTypeForm.orgId"
                    placeholder="请选择组织"
                    :data="orgTree"
                    label-key="name"
                    value-key="id"
                    width="370" />
            </el-form-item>
        </el-form>

        <link-type-param v-model:params="linkTypeForm.extraParams" />

        <template #footer>
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleConfirm">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import LinkTypeParam from './link-type-param.vue';
    import { onMounted, ref, watch } from 'vue';
    import type { FormInstance } from 'element-plus';
    import { Dimension, LinkType } from '@smartdesk/common/types';
    import { Enumeration, LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { dimensionApi, siteApi } from '@smartdesk/common/api';
    import { useFeedback } from '@smartdesk/common/composables';

    // 参数
    const props = defineProps<{
        visible: boolean;
        linkTypeData?: LinkType;
    }>();

    // 事件
    const emit = defineEmits(['update:visible', 'save', 'update:linkTypeData']);

    // pinia store
    const enumStore = useEnumStore();
    const feedback = useFeedback();

    // 网站选项
    const siteOptions = ref<LabelValue[]>([] as LabelValue[]);

    // 数据类型选项
    const dataTypeOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('dataType') || []);

    // 业务分组枚举
    const bizGroupOption = ref<Enumeration[]>(enumStore.getEnumsByKey('bizGroup') || []);

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // 控制弹窗显示
    const visible = ref(props.visible);

    // 判断是新增还是编辑
    const isEdit = ref(false);

    // 表单数据
    const linkTypeForm = ref<LinkType>({} as LinkType);

    // 表单ref
    const linkTypeFormRef = ref<FormInstance>();

    // 表单校验规则
    const rules = {
        code: [
            {
                required: true,
                message: '跳转链接类型不能为空',
                trigger: 'blur',
            },
        ],
        name: [
            {
                required: true,
                message: '跳转链接名称不能为空',
                trigger: 'blur',
            },
        ],
        dataType: [{ required: true, message: '数据类型不能为空', trigger: 'change' }],
        siteCode: [{ required: true, message: '网站不能为空', trigger: 'change' }],
    };

    // 获取站点列表
    const getSiteList = async () => {
        const res = await siteApi.optionsSite();
        siteOptions.value = res.result;
    };

    // 关闭 Dialog
    const handleClose = () => {
        // 重置表单数据
        linkTypeForm.value = {} as LinkType;
        // 重置编辑状态
        isEdit.value = false;
        // 重置表单校验
        linkTypeFormRef.value?.resetFields();
        // 通知父组件关闭对话框
        emit('update:visible', false);
        // 通知父组件清空当前数据
        emit('update:linkTypeData', undefined);
    };

    // 确认操作
    const handleConfirm = async () => {
        if (!linkTypeFormRef.value) return;

        await linkTypeFormRef.value.validate((valid, fields) => {
            if (valid) {
                emit('save', { ...linkTypeForm.value });
                handleClose();
            } else {
                feedback.error('请填写完整信息');
            }
        });
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    watch(
        () => props.visible,
        (val) => (visible.value = val)
    );

    // 监听 linkTypeData 变化，填充表单
    watch(
        () => props.linkTypeData,
        (newLinkType) => {
            if (newLinkType?.code) {
                isEdit.value = true;
                linkTypeForm.value = { ...newLinkType };
            } else {
                isEdit.value = false;
                linkTypeForm.value = {} as LinkType;
            }
        },
        { immediate: true }
    );

    // 组件挂载时请求数据
    onMounted(() => {
        getSiteList();
        getOrgTree();
    });
</script>
