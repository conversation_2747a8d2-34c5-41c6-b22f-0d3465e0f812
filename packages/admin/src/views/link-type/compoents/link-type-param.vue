<template>
    <div class="dynamic-table">
        <span class="title">扩展参数: </span>
        <el-button type="primary" @click="addNewRow">新增参数 </el-button>

        <el-table :data="tableData" border style="width: 100%; margin-top: 20px">
            <el-table-column label="参数名称" prop="name" width="150">
                <template #default="{ row }">
                    <el-input v-if="row.isEditing" v-model="row.name" placeholder="请输入参数名称" />
                    <span v-else>{{ row.name }}</span>
                </template>
            </el-table-column>
            <el-table-column label="参数名称说明" width="150">
                <template #default="{ row }">
                    <el-input v-if="row.isEditing" v-model="row.nameExplain" placeholder="请输入参数名称说明" />
                    <span v-else>{{ row.nameExplain }}</span>
                </template>
            </el-table-column>
            <el-table-column label="参数类型" width="100">
                <template #default="{ row }">
                    <el-select
                        v-if="row.isEditing"
                        v-model="row.type"
                        placeholder="请选择类型"
                        @change="handleTypeChange(row)">
                        <el-option
                            v-for="option in typeOptions"
                            :key="option.value"
                            :label="option.label"
                            :value="option.value" />
                    </el-select>
                    <span v-else>{{ formatTypeLabel(row.type) }}</span>
                </template>
            </el-table-column>
            <el-table-column label="参数值" width="180">
                <template #default="{ row }">
                    <el-input
                        v-if="row.isEditing && row.type === 'text'"
                        v-model="row.value"
                        placeholder="请输入参数值" />
                    <el-input-number v-else-if="row.isEditing && row.type === 'number'" v-model="row.value" :min="0" />
                    <el-select
                        v-else-if="row.isEditing && row.type === 'boolean'"
                        v-model="row.value"
                        placeholder="请选择">
                        <el-option label="true" :value="true" />
                        <el-option label="false" :value="false" />
                    </el-select>
                    <span v-else>{{ row.value }}</span>
                </template>
            </el-table-column>
            <el-table-column label="示例">
                <template #default="{ row }">
                    <el-input v-if="row.isEditing" v-model="row.example" placeholder="请输入示例" />
                    <span v-else>{{ row.example }}</span>
                </template>
            </el-table-column>
            <el-table-column label="是否必填" width="100">
                <template #default="{ row }">
                    <el-switch v-if="row.isEditing" v-model="row.required" />
                    <el-tag
                        v-else
                        :type="row.required ? 'danger' : 'success'"
                        :color="row.required ? '#fef0f0' : '#f0f9eb'"
                        effect="plain">
                        {{ row.required ? '必填' : '选填' }}
                    </el-tag>
                </template>
            </el-table-column>
            <el-table-column label="操作" :width="160">
                <template #default="{ row, $index }">
                    <template v-if="row.isEditing">
                        <el-button type="success" @click="saveRow(row)">保存 </el-button>
                        <el-button @click="cancelEdit(row, $index)">取消 </el-button>
                    </template>
                    <template v-else>
                        <el-button type="primary" @click="editRow(row, $index)">编辑 </el-button>
                        <el-button type="danger" @click="deleteRow(row)">删除 </el-button>
                    </template>
                </template>
            </el-table-column>
        </el-table>
    </div>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import { useFeedback } from '@smartdesk/common/composables';

    // 参数
    const props = defineProps<{
        params?: Param[] | undefined;
    }>();

    // 事件
    const emit = defineEmits(['update:params']);

    // pinia store
    const feedback = useFeedback();

    // 类型定义
    interface Param {
        name: string;
        nameExplain: string;
        type: 'text' | 'number' | 'boolean';
        value: string | number | boolean;
        example: string;
        required: boolean;
        isEditing?: boolean;
    }

    // 表格数据（使用 ref 并指定类型）
    const tableData = ref<Param[]>([]);

    // 类型选项
    const typeOptions = ref([
        { label: '文本', value: 'text' },
        { label: '数字', value: 'number' },
        { label: '布尔值', value: 'boolean' },
    ]);

    // 类型标签映射
    const typeLabels = {
        text: '文本',
        number: '数字',
        boolean: '布尔值',
    };

    // 方法定义
    const addNewRow = () => {
        tableData.value.push({
            name: '',
            nameExplain: '',
            type: 'text',
            value: '',
            example: '',
            required: false,
            isEditing: true,
        });
    };

    // 快照记录
    const snapshots = ref<Record<number, Param>>({});

    // 新增统一更新方法
    const updateParams = () => {
        // 过滤掉正在编辑的数据（根据需求可选）
        const validData = tableData.value
            .filter((item) => item.isEditing === false)
            .map(({ isEditing, ...rest }) => rest);
        emit('update:params', validData);
    };

    // 编辑参数
    const editRow = (row: Param, index: number) => {
        // 保存快照（深拷贝）
        snapshots.value[index] = JSON.parse(JSON.stringify(row));
        row.isEditing = true;
    };

    // 保存参数
    const saveRow = (row: Param) => {
        if (!validateName(row)) {
            feedback.error('参数名称只能由字母、数字、下划线组成');
            return;
        }
        // 唯一性验证
        if (!validateUnique(row)) {
            feedback.error('参数名称不能重复');
            return;
        }
        if (row.value === '') {
            feedback.error('参数值不能为空');
            return;
        }
        row.isEditing = false;
        updateParams();
    };

    // 取消编辑
    const cancelEdit = (row: Param, index: number) => {
        const snapshot = snapshots.value[index];
        if (snapshot) {
            Object.assign(row, snapshot);
            row.isEditing = false;
        }
    };

    // 删除参数
    const deleteRow = async (row: Param) => {
        if (await feedback.confirm(`确定要删除请求参数吗？`, '确认操作', 'warning')) {
            tableData.value = tableData.value.filter((item) => item.name !== row.name);
            feedback.success('操作成功');
            updateParams();
        }
    };

    // 工具方法
    const formatTypeLabel = (type: Param['type']): string => {
        return typeLabels[type] || '';
    };

    // 切换类型时，根据类型设置默认值
    const handleTypeChange = (row: Param) => {
        // 根据类型设置默认值
        if (row.type === 'number') {
            row.value = 0;
        } else if (row.type === 'boolean') {
            row.value = false;
        } else {
            row.value = '';
        }
    };

    // 验证测试名称
    const validateName = (row: Param) => {
        const pattern = /^[a-zA-Z0-9_]+$/;
        return pattern.test(row.name);
    };

    // 验证名称唯一性
    const validateUnique = (row: Param) => {
        // 统计相同名称的数量
        const sameNameCount = tableData.value.filter((item) => item.name === row.name).length;
        // 判断逻辑
        return sameNameCount <= 1;
    };

    // 修改监听逻辑（防止循环更新）
    watch(
        () => props.params,
        (newParams: Param[] | undefined) => {
            try {
                const newData = newParams?.map((item: Param) => ({
                    ...item,
                    isEditing: false,
                }));
                // 比较数据是否相同，避免无限循环
                if (JSON.stringify(tableData.value) !== JSON.stringify(newData)) {
                    tableData.value = newData || [];
                }
            } catch (e) {
                tableData.value = [];
            }
        },
        { immediate: true }
    );
</script>

<style scoped>
    .dynamic-table {
        margin: 15px 0;
    }

    .title {
        margin: 0 10px;
        font-size: 16px;
    }
</style>
