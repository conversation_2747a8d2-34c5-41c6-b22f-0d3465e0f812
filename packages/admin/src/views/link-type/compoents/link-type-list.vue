<template>
    <BaseTableWithPagination
        :total="totalElements"
        :current-page="currentPage"
        :page-size="pageSize"
        @update:currentPage="handleCurrentChange"
        @update:pageSize="handleSizeChange">
        <template #table>
            <el-table
                height="100%"
                :data="tableData"
                row-key="id"
                :header-cell-style="{ color: 'black', height: '50px' }"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" />
                <el-table-column prop="name" label="跳转链接名称" show-overflow-tooltip width="160" />
                <el-table-column prop="code" label="链接类型" show-overflow-tooltip width="120" />
                <el-table-column prop="dataType" label="数据类型" width="100">
                    <template #default="{ row }">
                        <el-tag type="primary">{{ enumStore.getLabelByKeyAndValue('dataType', row.dataType) }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="genre" label="分类" width="100" />
                <el-table-column prop="orderNo" label="顺序" width="60" />
                <el-table-column property="orgId" label="组织" width="100">
                    <template #default="{ row }">
                        <div>
                            {{ orgOptions.find((org) => org.value === row.orgId)?.label }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="可用状态" width="100">
                    <template #default="{ row }">
                        <el-switch
                            v-if="row.status === 1"
                            :disabled="
                                !canDisable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.LINK_TYPE.DISABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                        <el-switch
                            v-else
                            :disabled="
                                !canEnable(row) ||
                                !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.LINK_TYPE.ENABLE, {
                                    type: 'org',
                                    value: row.orgId,
                                })
                            "
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                    </template>
                </el-table-column>
                <el-table-column prop="summary" label="使用规则" width="100" show-overflow-tooltip />
                <el-table-column prop="bizGroups" label="业务分组" width="120">
                    <template #default="{ row }">
                        <el-tag v-if="row.bizGroups" type="success">{{ row.bizGroups }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="extraParams" label="扩展参数" width="160">
                    <template #default="{ row }">
                        <el-popover placement="bottom" :width="row.extraParams ? '700px' : '100px'" trigger="click">
                            <template #reference>
                                <el-button style="margin-right: 16px">查看参数</el-button>
                            </template>
                            <el-table
                                v-if="row.extraParams"
                                :data="row.extraParams"
                                border
                                stripe
                                style="width: 650px; margin-top: 20px">
                                <el-table-column label="参数名称" prop="name" width="100" align="center" />
                                <el-table-column label="参数名称说明" prop="nameExplain" width="120" align="center" />
                                <el-table-column label="参数类型" prop="type" width="100" align="center">
                                    <template #default="{ row }">
                                        {{ formatTypeLabel(row.type) }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="参数值" prop="value" width="100" align="center" />
                                <el-table-column label="参数示例" prop="example" width="150" align="center" />
                                <el-table-column label="是否必填" width="100">
                                    <template #default="{ row }">
                                        <el-tag
                                            :type="row.required ? 'danger' : 'success'"
                                            :color="row.required ? '#fef0f0' : '#f0f9eb'"
                                            effect="plain">
                                            {{ row.required ? '必填' : '选填' }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <span v-else>暂无参数</span>
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column prop="modifiedBy" label="修改人" width="120" />
                <el-table-column prop="modifiedTime" label="修改日期" width="180">
                    <template #default="{ row }">
                        {{ row.modifiedTime ? format(parseISO(row.modifiedTime), 'yyyy-MM-dd HH:mm:ss') : '' }}
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="160" fixed="right">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-button
                                :disabled="
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.LINK_TYPE.EDIT, {
                                        type: 'org',
                                        value: row.orgId,
                                    })
                                "
                                link
                                type="warning"
                                @click="onClickEdit(row)">
                                编辑
                            </el-button>
                            <el-button
                                :disabled="
                                    !canDeleteByAdmin(row) ||
                                    !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.LINK_TYPE.DELETE, {
                                        type: 'org',
                                        value: row.orgId,
                                    })
                                "
                                link
                                type="danger"
                                @click="onClickDelete(row)">
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </BaseTableWithPagination>
</template>

<script setup lang="ts">
    import { LinkType, LinkTypeSearchForm } from '@smartdesk/common/types';
    import { format, parseISO } from 'date-fns';
    import { onMounted, ref, watch } from 'vue';
    import { dimensionApi, linkTypeApi } from '@smartdesk/common/api';
    import { LabelValue, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';
    import { ADMIN_BIZ_PERMISSION, canDeleteByAdmin, canDisable, canEnable } from '@smartdesk/common/permission';
    import { useFeedback } from '@smartdesk/common/composables';

    // 参数
    const props = defineProps<{
        searchForm: Partial<LinkTypeSearchForm>;
    }>();

    // 事件
    const emit = defineEmits(['edit', 'update:selection']);

    // pinia store
    const enumStore = useEnumStore();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 跳转链接列表数据
    const tableData = ref<LinkType[]>([]);

    // 已选中的跳转链接列表
    const linkTypeSelection = ref<LinkType[]>([]);

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);

    // 类型标签映射
    const typeLabels = {
        text: '文本',
        number: '数字',
        boolean: '布尔值',
    } as any;

    // 处理页码变化
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1;
        getLinkTypeList();
    };

    // 处理当前页变化
    const handleCurrentChange = (val: number) => {
        currentPage.value = val;
        getLinkTypeList();
    };

    // 中转已选中的跳转链接列表
    const handleSelectionChange = (val: LinkType[]) => {
        linkTypeSelection.value = val;
        emit('update:selection', val);
    };

    // 启用/禁用跳转链接
    const handleStatusChange = async (row: LinkType) => {
        const word = row.status === 0 ? '禁用' : '启用';
        if (await feedback.confirm(`确定要${word}该跳转链接吗？`, '确定操作', 'warning')) {
            const response =
                row.status === 0
                    ? await linkTypeApi.disableLinkType(row.code)
                    : await linkTypeApi.enableLinkType(row.code);
            if (response.code === 200) {
                feedback.success(`${word}跳转链接成功`);
                await getLinkTypeList();
            } else {
                feedback.error(`${word}跳转链接失败：` + response.msg);
            }
        } else {
            row.status = row.status === 1 ? 0 : 1;
        }
    };

    // 点击编辑
    const onClickEdit = (row: LinkType) => {
        emit('edit', row);
    };

    // 点击删除跳转链接
    const onClickDelete = async (row: LinkType) => {
        if (await feedback.confirm(`确定要删除该跳转链接吗？`, '确定操作', 'warning')) {
            const response = await linkTypeApi.batchDeleteLinkType([row.code]);
            if (response.code === 200) {
                feedback.success('删除跳转链接成功');
                await getLinkTypeList();
            } else {
                feedback.error('删除跳转链接失败：' + response.msg);
            }
        }
    };

    // 获取跳转链接列表
    const getLinkTypeList = async () => {
        const response = await linkTypeApi.getLinkTypes(props.searchForm, {
            page: currentPage.value - 1,
            size: pageSize.value,
            sort: 'id,desc',
        });
        if (response.code === 200) {
            tableData.value = response.result;
            totalElements.value = Number(response.page.totalElements);
        }
    };

    // 格式化
    const formatTypeLabel = (type: string): string => {
        return typeLabels[type] || '';
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    // 监听查询表单，查询跳转链接列表
    watch(
        () => props.searchForm,
        () => {
            getLinkTypeList();
        }
    );

    onMounted(() => {
        getOrgOptions();
    });

    defineExpose({
        getLinkTypeList,
    });
</script>
