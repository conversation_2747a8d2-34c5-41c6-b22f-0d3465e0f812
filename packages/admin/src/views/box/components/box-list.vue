<template>
    <BaseTableWithPagination
        :total="totalElements"
        :current-page="currentPage"
        :page-size="pageSize"
        @update:currentPage="handleCurrentChange"
        @update:pageSize="handleSizeChange">
        <template #table>
            <el-table
                ref="multipleTableRef"
                :data="boxList"
                row-key="id"
                :header-cell-style="{ color: 'black', height: '50px' }"
                @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="70" label="全选" />
                <el-table-column property="code" label="机顶盒类型编码" min-width="180" show-overflow-tooltip />
                <el-table-column property="name" label="机顶盒类型名称" min-width="180" show-overflow-tooltip />
                <el-table-column property="boxGroup" label="机顶盒厂商" min-width="180" show-overflow-tooltip />
                <el-table-column property="capability" label="机顶盒功能" min-width="240" show-overflow-tooltip>
                    <template #default="{ row }">
                        {{ getCapabilityName(row.capability) }}
                    </template>
                </el-table-column>
                <el-table-column prop="auditStatus" label="审核状态" width="120">
                    <template #default="{ row }">
                        {{ enumStore.getLabelByKeyAndValue('auditStatus', row.auditStatus) }}
                    </template>
                </el-table-column>
                <el-table-column label="可用状态" min-width="100">
                    <template #default="{ row }">
                        <el-switch
                            v-if="row.status === 1"
                            :disabled="!canDisable(row)"
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                        <el-switch
                            v-else
                            :disabled="!canEnable(row)"
                            v-model="row.status"
                            size="small"
                            :active-value="1"
                            :inactive-value="0"
                            :inactive-text="row.status ? '可用' : '不可用'"
                            @change="handleStatusChange(row)" />
                    </template>
                </el-table-column>
                <el-table-column label="修改时间" min-width="180">
                    <template #default="{ row }">
                        {{ row.modifiedTime ? format(row.modifiedTime, 'yyyy-MM-dd HH:mm:ss') : '' }}
                    </template>
                </el-table-column>
                <el-table-column property="modifiedBy" label="修改人" min-width="120" />
                <el-table-column label="操作" fixed="right" min-width="180">
                    <template #default="{ row }">
                        <div class="flex items-center">
                            <el-button type="primary" text @click="onClickEdit(row)"> 编辑 </el-button>
                            <el-button
                                :disabled="
                                    !canAudit(row) || !permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.BOX.AUDIT)
                                "
                                link
                                type="primary"
                                @click="handleAudit(row)">
                                送审
                            </el-button>
                            <el-button
                                :disabled="!canDeleteByAdmin(row)"
                                type="danger"
                                text
                                @click="onClickDelete(row)">
                                删除
                            </el-button>
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </BaseTableWithPagination>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { Box } from '@smartdesk/common/types';
    import { boxApi, publishApi } from '@smartdesk/common/api';
    import { useFeedback } from '@smartdesk/common/composables';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';
    import { Enumeration, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import {
        ADMIN_BIZ_PERMISSION,
        canAudit,
        canDeleteByAdmin,
        canDisable,
        canEnable,
    } from '@smartdesk/common/permission';
    import { format } from 'date-fns';

    // 参数
    const props = defineProps<{
        boxForm: any;
    }>();

    // 事件
    const emit = defineEmits(['update:selection', 'editBox']);

    // pinia store
    const enumStore = useEnumStore();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);

    // 机顶盒列表
    const boxList = ref<Box[]>([]);

    // 选中的机顶盒
    const boxSelection = ref<Box[]>([]);

    const boxCapabilityEnum = ref<Enumeration[]>(enumStore.getEnumsByKey('boxCapabilityEnum') || []);

    const getCapabilityName = (capability: string[]): string => {
        if (!capability) return '';

        return capability
            .map((code) => boxCapabilityEnum.value.find((item) => item.code === code)?.name)
            .filter((name): name is string => Boolean(name))
            .join('｜');
    };

    // 处理页码变化
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1;
        findBoxtList();
    };
    // 处理当前页变化
    const handleCurrentChange = (val: number) => {
        currentPage.value = val;
        findBoxtList();
    };

    // 处理机顶盒选中
    const handleSelectionChange = (val: Box[]) => {
        boxSelection.value = val;
        emit('update:selection', val);
    };

    // 启用/禁用机顶盒
    const handleStatusChange = async (row: Box) => {
        const newStatus = row.status;
        const originalStatus = newStatus === 1 ? 0 : 1;
        const confirmed = await feedback.confirm(
            `确定要${newStatus === 0 ? '不可用' : '可用'}该机顶盒吗？`,
            '确定操作',
            'warning'
        );
        if (confirmed) {
            if (newStatus === 0) {
                await handleDisableLayout(row.code);
            } else {
                await handleEnableLayout(row.code);
            }
        } else {
            // 用户取消，恢复原状态
            row.status = originalStatus;
        }
    };

    // 不可用楼层
    const handleDisableLayout = async (code: string) => {
        try {
            const response = await boxApi.disableBox(code);
            if (response.code === 200) {
                feedback.success('不可用成功');
                await findBoxtList();
            } else {
                feedback.error('不可用失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('不可用失败');
        }
    };

    // 可用楼层
    const handleEnableLayout = async (code: string) => {
        try {
            const response = await boxApi.enableBox(code);
            if (response.code === 200) {
                feedback.success('可用成功');
                await findBoxtList();
            } else {
                feedback.error('可用失败：' + response.msg);
            }
        } catch (error) {
            feedback.error('可用失败');
        }
    };

    // 送审
    const handleAudit = async (row: Box) => {
        if (await feedback.confirm('确定要送审吗？', '警告', 'warning')) {
            const res = await publishApi.publishSelf('Box', row.code, 'CREATE');
            if (res.code === 200) {
                feedback.success('送审成功');
                findBoxtList();
            } else {
                feedback.error('送审失败：' + res.msg);
            }
        }
    };
    // 点击编辑按钮
    const onClickEdit = (row: Box) => {
        emit('editBox', row);
    };

    // 点击删除按钮
    const onClickDelete = async (row: Box) => {
        if (await feedback.confirm(`确定要删除该机顶盒吗？`, '确认操作', 'warning')) {
            const res = await boxApi.deleteBox(row.code);
            if (res.code === 200) {
                feedback.success(`删除机顶盒成功`);
                await findBoxtList();
            } else {
                feedback.error(`删除机顶盒失败：` + res.msg);
            }
        }
    };

    // 请求后台获取数据
    const findBoxtList = async () => {
        const response = await boxApi.getBoxs(props.boxForm, {
            page: currentPage.value - 1,
            size: pageSize.value,
        });
        boxList.value = response.result;
        totalElements.value = Number(response.page.totalElements);
    };
    // 监听查询表单
    watch(
        () => props.boxForm,
        () => {
            findBoxtList();
        },
        { deep: true }
    );

    onMounted(() => {});

    // 公开方法给父组件调用
    defineExpose({
        findBoxtList,
    });
</script>
