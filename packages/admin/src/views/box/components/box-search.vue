<template>
    <el-form inline label-width="100" :label-suffix="':'" :size="'default'" class="mt-2">
        <search-container
            :initialWidth="315"
            :searchForm="searchForm"
            :isValueNotEmpty="isValueNotEmpty"
            :getKeyFormat="getKeyFormat"
            :getValueFormat="getValueFormat"
            :handleTagClose="handleTagClose"
            :activeFields="activeFields">
            <template #item-0>
                <el-form-item label="删除状态">
                    <el-select
                        v-model="searchForm.delFlags"
                        @update:modelValue="(val: any) => handleFieldUpdate('delFlags', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in delFlagOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-1>
                <el-form-item label="可用状态">
                    <el-select
                        v-model="searchForm.statuses"
                        @update:modelValue="(val: any) => handleFieldUpdate('statuses', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in enableStatusOption"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-2>
                <el-form-item label="机顶盒厂商">
                    <el-select
                        v-model="searchForm.boxGroups"
                        @update:modelValue="(val: any) => handleFieldUpdate('boxGroups', val)"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in boxGroupOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </template>
            <template #item-3>
                <el-form-item label="关键词">
                    <el-input
                        v-model="searchForm.name"
                        @update:modelValue="(val: any) => handleFieldUpdate('name', val)"
                        placeholder="请输入"
                        style="width: 180px"
                        clearable>
                        <template #prefix>
                            <el-icon class="el-input__icon">
                                <search />
                            </el-icon>
                        </template>
                    </el-input>
                </el-form-item>
            </template>
        </search-container>
    </el-form>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { BoxSearchForm } from '@smartdesk/common/types';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import SearchContainer from '@smartdesk/admin/views/common/search-container.vue';
    import { getKeyFormat, getValueFormat, handleTagClose, isValueNotEmpty } from '@smartdesk/common/utils';

    // 事件
    const emit = defineEmits(['search']);

    // pinia store
    const enumStore = useEnumStore();

    // 用户设置表单字段的顺序
    const activeFields = ref<string[]>([]);

    // 查询表单
    const searchForm = ref<Partial<BoxSearchForm>>({
        name: '',
        delFlags: [0],
        statuses: [],
        boxGroups: [],
    });

    // 状态枚举值
    const enableStatusOption = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('enableStatus') || []);

    // 删除状态枚举
    const delFlagOptions = ref<
        {
            name: any;
            code: number;
        }[]
    >(enumStore.getNameCodeNumberOptionsByKey('delFlag') || []);

    const boxGroupOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('boxGroupEnum') || []);

    // 处理搜索事件
    const handleSearch = () => {
        emit('search', searchForm.value);
    };

    function handleFieldUpdate(field: keyof BoxSearchForm, value: any) {
        searchForm.value[field] = value;

        // 判断值是否有效（非空/非空数组）
        const isEmpty =
            value === '' || value === null || value === undefined || (Array.isArray(value) && value.length === 0);

        if (!isEmpty) {
            if (!activeFields.value.includes(field as string)) {
                activeFields.value.push(field as string);
            }
        } else {
            // 如果用户清空值了，移除 field
            const index = activeFields.value.indexOf(field as string);
            if (index !== -1) {
                activeFields.value.splice(index, 1);
            }
        }
    }

    // 初始化字段
    const initActiveFields = () => {
        Object.entries(searchForm.value).forEach(([key, val]) => {
            if (isValueNotEmpty(val)) {
                if (!activeFields.value.includes(key)) {
                    activeFields.value.push(key);
                }
            }
        });
    };

    // 监听查询表单
    watch(
        () => searchForm.value,
        () => {
            handleSearch();
        },
        { deep: true }
    );

    onMounted(() => {
        initActiveFields();
        handleSearch();
    });
</script>
<style scoped>
    .tree-select-auto {
        min-width: 180px;
        max-width: 600px;
        width: auto;
    }
</style>
