<template>
    <el-dialog
        class="capability-dialog"
        title="机顶盒能力设置"
        v-model="visible"
        width="90%"
        append-to-body
        :z-index="10"
        @open="findBoxList"
        @close="handleClose">
        <div style="margin: 10px">
            <el-form label-suffix=":">
                <el-form-item label="选择能力">
                    <el-select v-model="currentCapability" multiple placeholder="请选择能力">
                        <el-option
                            v-for="item in boxCapabilityOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-form>
            <SelectParamChildren
                :model-value="boxModel || []"
                :enum-option="boxOption"
                @update:model-value="handleUserGroupChange" />
        </div>
        <template #footer>
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, onMounted, computed, watch } from 'vue';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';
    import { Box } from '@smartdesk/common/types';
    import { boxApi } from '@smartdesk/common/api';
    import SelectParamChildren from '@smartdesk/admin/views/personal-rule/components/select-param-children.vue';

    // 参数
    const props = defineProps<{
        visible: boolean;
    }>();

    // 事件
    const emit = defineEmits(['update:visible', 'refresh']);
    // pinia store
    const enumStore = useEnumStore();
    const feedback = useFeedback();

    // 控制弹窗显示
    const visible = ref(props.visible);

    // 机顶盒列表
    const boxList = ref<Box[]>([]);

    // 机顶盒
    const boxModel = ref<[]>([]);

    // 选中的机顶盒
    const currentBoxCodes = ref<string[]>([]);

    // 选中的能力
    const currentCapability = ref<string[]>([]);

    // 机顶盒枚举
    const boxOption = ref<Enumeration[]>([]);

    // 机顶盒厂商
    const boxGroupOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('boxGroupEnum') || []);

    // 机顶盒功能
    const boxCapabilityOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('boxCapabilityEnum') || []);

    // 处理机顶盒功能变化
    const handleCheckedBoxCapabilityChange = (value: string[]) => {
        console.log('handleCheckedBoxCapabilityChange', value);
    };

    // 关闭 Dialog
    const handleClose = () => {
        // 通知父组件关闭对话框
        emit('update:visible', false);
        currentBoxCodes.value = [];
        currentCapability.value = [];
        boxOption.value = [];
        boxModel.value = [];
        emit('refresh');
    };

    // 选中的盒子
    const handleUserGroupChange = (value: string[]) => {
        console.log('handleUserGroupChange', value);
        currentBoxCodes.value = value;
    };

    const handleSubmit = async () => {
        if (currentCapability.value.length == 0) {
            feedback.error('能力不能为空');
            return;
        }
        if (currentBoxCodes.value.length == 0) {
            feedback.error('机顶盒类型不能为空');
            return;
        }
        const response = await boxApi.settingBoxCapability({
            boxCodes: currentBoxCodes.value,
            capability: currentCapability.value,
        });
        if (response.code === 200) {
            feedback.success('保存成功');
            handleClose();
        }
    };

    // 请求后台获取数据
    const findBoxList = async () => {
        boxOption.value = [];
        const response = await boxApi.getBoxList();
        boxList.value = response.result;

        boxOption.value = boxGroupOptions.value.map((item) => {
            debugger;
            const boxes = boxList.value.filter((box) => box.boxGroup === item.code);
            let children = boxes.map((item) => {
                return {
                    code: item.code,
                    name: item.name,
                } as Enumeration;
            });
            return {
                ...item,
                children: children,
            };
        });
    };

    watch(
        () => props.visible,
        (val) => (visible.value = val)
    );
</script>
<style scoped>
    .capability-dialog .dialog-body {
        max-height: 500px; /* 或固定高度，比如 400px */
        overflow-y: auto;
        padding-right: 10px; /* 避免滚动条遮住内容 */
    }
</style>
