<template>
    <el-dialog
        :title="isEdit ? '编辑机顶盒' : '新增机顶盒'"
        v-model="visible"
        width="50%"
        append-to-body
        :z-index="10"
        @close="handleClose">
        <el-form ref="boxFormRef" :model="boxForm" :rules="rules" label-width="180px" label-suffix=":">
            <el-form-item label="机顶盒类型编码" prop="code">
                <el-input v-model="boxForm.code" placeholder="请输入机顶盒类型编码" :disabled="isEdit" />
            </el-form-item>
            <el-form-item label="机顶盒类型名称" prop="name">
                <el-input v-model="boxForm.name" placeholder="请输入机顶盒类型名称" />
            </el-form-item>
            <el-form-item label="机顶盒厂商" prop="boxGroup">
                <el-select v-model="boxForm.boxGroup" placeholder="请选择机顶盒厂商">
                    <el-option v-for="item in boxGroupOptions" :key="item.code" :label="item.name" :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="机顶盒功能" prop="capability">
                <el-checkbox-group
                    v-model="boxForm.capability"
                    @change="handleCheckedBoxCapabilityChange"
                    class="checkbox-group">
                    <el-checkbox
                        v-for="boxCapability in boxCapabilityOptions"
                        :key="boxCapability.code"
                        :label="boxCapability.name"
                        :value="boxCapability.code">
                        {{ boxCapability.name }}
                    </el-checkbox>
                </el-checkbox-group>
            </el-form-item>
        </el-form>
        <template #footer>
            <el-button @click="handleClose">取消</el-button>
            <el-button type="primary" @click="handleConfirm">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import { Box } from '@smartdesk/common/types';
    import type { FormInstance } from 'element-plus';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';

    // 参数
    const props = defineProps<{
        visible: boolean;
        boxData: Box;
    }>();

    // 事件
    const emit = defineEmits(['update:visible', 'save', 'update:boxData']);
    // pinia store
    const enumStore = useEnumStore();
    const feedback = useFeedback();

    // 表单数据
    const boxForm = ref<Box>({} as Box);

    // 表单ref
    const boxFormRef = ref<FormInstance>();

    // 控制弹窗显示
    const visible = ref(props.visible);

    // 判断是新增还是编辑
    const isEdit = ref(false);

    // 机顶盒厂商
    const boxGroupOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('boxGroupEnum') || []);
    const boxCapabilityOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('boxCapabilityEnum') || []);

    // 表单校验规则
    const rules = {
        code: [
            {
                required: true,
                message: '机顶盒类型编码不能为空',
                trigger: 'blur',
            },
        ],
        name: [
            {
                required: true,
                message: '机顶盒类型名称不能为空',
                trigger: 'blur',
            },
        ],
        boxGroup: [
            {
                required: true,
                message: '机顶盒厂商不能为空',
                trigger: 'change',
            },
        ],
    };
    // 关闭 Dialog
    const handleClose = () => {
        // 重置表单数据
        boxForm.value = {} as Box;
        // 重置编辑状态
        isEdit.value = false;
        // 重置表单校验
        boxFormRef.value?.resetFields();
        // 通知父组件关闭对话框
        emit('update:visible', false);
        // 通知父组件清空当前数据
        emit('update:boxData', undefined);
    };

    // 确认操作
    const handleConfirm = async () => {
        if (!boxFormRef.value) return;

        await boxFormRef.value.validate((valid, fields) => {
            if (valid) {
                emit('save', { ...boxForm.value });
                handleClose();
            } else {
                feedback.error('请填写完整信息');
            }
        });
    };

    // 处理机顶盒功能变化
    const handleCheckedBoxCapabilityChange = (value: string[]) => {
        boxForm.value.capability = value;
    };

    watch(
        () => props.visible,
        (val) => (visible.value = val)
    );

    // 监听 linkTypeData 变化，填充表单
    watch(
        () => props.boxData,
        (newBox) => {
            if (newBox?.code) {
                isEdit.value = true;
                boxForm.value = { ...newBox };
            } else {
                isEdit.value = false;
                boxForm.value = {} as Box;
            }
        },
        { immediate: true }
    );
</script>
<style scoped>
    /* 在 style 中添加 */
    .checkbox-group {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
</style>
