<template>
    <PageContainer>
        <template #search>
            <box-search @search="searchBoxList" />
        </template>

        <template #toolbar>
            <box-toolbars
                :box-selection="selectedBox"
                @add="addBox"
                @refresh="refreshBoxList"
                @capability="capability" />
        </template>

        <template #list>
            <box-list
                ref="boxListRef"
                :box-form="searchForm"
                @editBox="editBox"
                @update:selection="handleSelectionBox" />
        </template>
    </PageContainer>
    <box-add
        v-model:visible="addBoxVisible"
        v-model:box-data="currentBox"
        @update:visible="addBoxVisible = $event"
        @save="saveBox" />
    <box-capability
        v-model:visible="capabilityBoxVisible"
        @update:visible="capabilityBoxVisible = $event"
        @refresh="refreshBoxList" />
</template>

<script setup lang="ts">
    import { ref } from 'vue';
    import { Box, BoxSearchForm } from '@smartdesk/common/types';
    import { boxApi } from '@smartdesk/common/api';
    import { useFeedback } from '@smartdesk/common/composables';
    import PageContainer from '../common/page-container.vue';
    import BoxSearch from './components/box-search.vue';
    import BoxList from './components/box-list.vue';
    import BoxToolbars from './components/box-toolbars.vue';
    import BoxAdd from './components/box-add.vue';
    import BoxCapability from './components/box-capability.vue';

    defineOptions({
        name: 'Box',
    });

    // 查询表单
    const searchForm = ref<Partial<BoxSearchForm>>({});

    // pinia store
    const feedback = useFeedback();

    // 已选中的机顶盒列表
    const selectedBox = ref<Box[]>([]);

    // 当前机顶盒
    const currentBox = ref<Box>({} as Box);
    // 新增机顶盒弹窗
    const addBoxVisible = ref(false);
    // 机顶盒功能设置弹窗
    const capabilityBoxVisible = ref(false);

    // 机顶盒列表引用
    const boxListRef = ref<InstanceType<typeof BoxList> | null>(null);

    // 搜索机顶盒
    const searchBoxList = (form: BoxSearchForm) => {
        searchForm.value = { ...form };
    };

    // 刷新机顶盒列表
    const refreshBoxList = () => {
        boxListRef.value?.findBoxtList();
    };

    // 新增机顶盒
    const addBox = () => {
        currentBox.value = {} as Box;
        addBoxVisible.value = true;
    };
    // 修改机顶盒
    const editBox = (box: Box) => {
        currentBox.value = box;
        addBoxVisible.value = true;
    };

    // 中转已选中机顶盒类型
    const handleSelectionBox = (selection: Box[]) => {
        selectedBox.value = selection;
    };

    // 机顶盒功能设置事件
    const capability = () => {
        capabilityBoxVisible.value = true;
    };

    // 保存机顶盒
    const saveBox = (boxForm: Box) => {
        if (boxForm.id) {
            updateBox(boxForm);
        } else {
            createBox(boxForm);
        }
    };

    // 更新机顶盒类型
    const updateBox = async (form: Box) => {
        const res = await boxApi.updateBox(form.id, form);
        if (res.code === 200) {
            refreshBoxList();
            addBoxVisible.value = false;
            feedback.success('更新机顶盒成功');
        } else {
            feedback.error('更新机顶盒失败：' + res.msg);
        }
    };

    // 新增机顶盒类型
    const createBox = async (form: Box) => {
        const res = await boxApi.createBox(form);
        if (res.code === 200) {
            refreshBoxList();
            addBoxVisible.value = false;
            feedback.success('新增机顶盒成功');
        } else {
            feedback.error('新增机顶盒失败：' + res.msg);
        }
    };
</script>
