<template>
    <PageContainer>
        <template #search>
            <page-search @search-page="searchPage" />
        </template>

        <template #toolbar>
            <page-toolbars
                :data-list="selectedPages"
                @batch-action="handleBatchAction"
                @create-page="handleCreatePage" />
        </template>

        <template #list>
            <page-list
                ref="pageListRef"
                :searchFrom="searchFrom"
                @selection-change="handleSelectionChange"
                @row-action="handleRowAction" />
        </template>
    </PageContainer>

    <page-dialog
        v-if="showPageEditVisible"
        v-model:visible="showPageEditVisible"
        :model-value="currentPage"
        :mode="pageEditMode"
        @submit="handlePageSubmit" />

    <page-section-list
        v-model="showPageSectionList"
        :pageSectionList="pageSectionList"
        @save="updatePageSectionOrder" />
</template>

<script setup lang="ts">
    import { ref } from 'vue';
    import { Page, PageSearchForm } from '@smartdesk/common/types';
    import { pageApi, publishApi } from '@smartdesk/common/api';
    import PageDialog from './components/page-dialog.vue';
    import PageList from './components/page-list.vue';
    import PageSearch from './components/page-search.vue';
    import PageToolbars from './components/page-toolbars.vue';
    import PageSectionList from '@smartdesk/admin/views/page-section/components/page-section-list.vue';
    import { useFeedback, useJumpToDesigner } from '@smartdesk/common/composables';
    import { extractDimensions } from '@smartdesk/common/utils';
    import PageContainer from '../common/page-container.vue';

    // 组件名称：Page
    defineOptions({
        name: 'Page',
    });

    // 组合函数
    const feedback = useFeedback();
    const { jumpToDesigner } = useJumpToDesigner();

    // 查询条件
    const searchFrom = ref<PageSearchForm>({} as PageSearchForm);
    const selectedPages = ref<Page[]>([] as Page[]);
    const showPageEditVisible = ref<boolean>(false);
    const showPageSectionList = ref<boolean>(false);
    const pageSectionList = ref<any>([] as any);
    const currentPage = ref<Page>({} as Page);
    const pageEditMode = ref<'create' | 'update' | 'copy'>('create');

    // 绑定子组件
    const pageListRef = ref<InstanceType<typeof PageList> | null>(null);

    // 调用子组件的方法
    const refreshPageList = () => {
        pageListRef.value?.findPageList();
    };

    // 点击查询按钮
    const searchPage = (from: any) => {
        // 查询页面列表
        searchFrom.value = { ...from };
    };

    // 选中行处理
    const handleSelectionChange = (selection: Page[]) => {
        selectedPages.value = selection;
    };

    // 点击批量等按钮
    const handleBatchAction = async (action: string) => {
        if (selectedPages.value.length === 0) {
            feedback.error('请选择数据');
            return;
        }

        // 页面编码列表
        const pageCodes = selectedPages.value.map((page) => {
            return page.code;
        });

        switch (action) {
            case 'audit':
                if (await feedback.confirm('确定要批量送审吗？', '警告', 'warning')) {
                    const res = await publishApi.batchPublishSelf('Page', pageCodes, 'CREATE');
                    if (res.code === 200) {
                        feedback.success('批量送审成功');
                        refreshPageList();
                    } else {
                        feedback.error('批量送审失败：' + res.msg);
                    }
                }
                break;
            case 'online':
                if (await feedback.confirm('确定要批量上线吗？', '警告', 'warning')) {
                    const res = await publishApi.batchPublishSelf('Page', pageCodes, 'ONLINE');
                    if (res.code === 200) {
                        feedback.success('批量上线成功');
                        refreshPageList();
                    } else {
                        feedback.error('批量上线失败：' + res.msg);
                    }
                }
                break;
            case 'offline':
                if (await feedback.confirm('确定要批量下线吗？', '警告', 'warning')) {
                    const res = await publishApi.batchPublishSelf('Page', pageCodes, 'OFFLINE');
                    if (res.code === 200) {
                        feedback.success('批量下线成功');
                        refreshPageList();
                    } else {
                        feedback.error('批量下线失败：' + res.msg);
                    }
                }
                break;
            case 'delete':
                if (await feedback.confirm('确定要批量删除吗？', '警告', 'warning')) {
                    const res = await publishApi.batchPublishComplete('Page', pageCodes, 'DELETE');
                    if (res.code === 200) {
                        feedback.success('批量删除成功');
                        refreshPageList();
                    } else {
                        feedback.error('批量删除失败：' + res.msg);
                    }
                }
                break;
            case 'enable':
                if (await feedback.confirm('确定要批量可用吗？', '警告', 'warning')) {
                    const res = await pageApi.batchEnablePage(pageCodes);
                    if (res.code === 200) {
                        feedback.success('批量可用成功');
                        refreshPageList();
                    } else {
                        feedback.error('批量可用失败：' + res.msg);
                    }
                }
                break;
            case 'disable':
                if (await feedback.confirm('确定要批量不可用吗？', '警告', 'warning')) {
                    const res = await pageApi.batchDisablePage(pageCodes);
                    if (res.code === 200) {
                        feedback.success('批量不可用成功');
                        refreshPageList();
                    } else {
                        feedback.error('批量不可用失败：' + res.msg);
                    }
                }
                break;
        }
    };

    // 点击行内按钮
    const handleRowAction = async (action: string, row: Page) => {
        switch (action) {
            case 'config':
                // 配置，跳转页面设计器
                jumpToDesigner('page', row.code);
                break;
            case 'audit':
                // 送审
                if (await feedback.confirm('确定要送审吗？', '警告', 'warning')) {
                    const res = await publishApi.publishSelf('Page', row.code, 'CREATE');
                    if (res.code === 200) {
                        feedback.success('送审成功');
                        refreshPageList();
                    } else {
                        feedback.error('送审失败：' + res.msg);
                    }
                }
                break;
            case 'online':
                // 上线
                if (await feedback.confirm('确定要上线吗？', '警告', 'warning')) {
                    const res = await publishApi.publishSelf('Page', row.code, 'ONLINE');
                    if (res.code === 200) {
                        feedback.success('上线成功');
                        refreshPageList();
                    } else {
                        feedback.error('上线失败：' + res.msg);
                    }
                }
                break;
            case 'offline':
                // 下线
                if (await feedback.confirm('确定要下线吗？', '警告', 'warning')) {
                    const res = await publishApi.publishSelf('Page', row.code, 'OFFLINE');
                    if (res.code === 200) {
                        feedback.success('下线成功');
                        refreshPageList();
                    } else {
                        feedback.error('下线失败：' + res.msg);
                    }
                }
                break;
            case 'delete':
                // 删除
                if (await feedback.confirm('确定要删除吗？', '警告', 'warning')) {
                    const res = await publishApi.publishComplete('Page', row.code, 'DELETE');
                    if (res.code === 200) {
                        feedback.success('删除成功');
                        refreshPageList();
                    } else {
                        feedback.error('删除失败：' + res.msg);
                    }
                }
                break;
            case 'showPageSection':
                // 查看节
                await findPageSection(row.code);
                break;
            case 'edit':
                // 编辑
                pageEditMode.value = 'update';
                currentPage.value = row;
                showPageEditVisible.value = true;
                break;
            case 'enable':
                // 启用
                const response = await pageApi.batchEnablePage([row.code]);
                if (response.code === 200) {
                    feedback.success('可用成功');
                    refreshPageList();
                } else {
                    feedback.error('可用失败：' + response.msg);
                }
                break;
            case 'disable':
                // 禁用
                const res = await pageApi.batchDisablePage([row.code]);
                if (res.code === 200) {
                    feedback.success('不可用成功');
                    refreshPageList();
                } else {
                    feedback.error('不可用失败：' + res.msg);
                }
                break;
            case 'copy':
                // 复制
                pageEditMode.value = 'copy';
                currentPage.value = row;
                showPageEditVisible.value = true;
                break;
        }
    };

    // 根据页面code 查看节
    const findPageSection = async (code: string) => {
        if (code === '') {
            return;
        }
        const response = await pageApi.findPageSection(code);
        if (response.code === 200) {
            pageSectionList.value = response.result;
            showPageSectionList.value = true;
        }
    };

    // 点击新增按钮
    const handleCreatePage = () => {
        pageEditMode.value = 'create';
        showPageEditVisible.value = true;
        currentPage.value = {} as Page;
    };

    // 初始化页面 layout
    const initPageLayout = (submitData: Page) => {
        const formData: Page = { ...submitData };
        const rect = extractDimensions(formData.resolution ?? '');

        if (!formData.layout) {
            // 如果之前没有布局，则初始化一个
            formData.layout = {
                code: submitData.code,
                name: submitData.name,
                component: 'page',
                componentStyleCode: '',
                layout: {
                    rect: {
                        top: 0,
                        left: 0,
                        width: rect ? rect.width : 0,
                        height: rect ? rect.height : 0,
                    },
                    props: {},
                },
                layoutVersion: 1,
                sections: [],
            };
        } else {
            // 如果之前有布局
            formData.layout.layout.rect.width = rect ? rect.width : 0;
            formData.layout.layout.rect.height = rect ? rect.height : 0;
        }

        return formData;
    };

    // 新增页面提交
    const handlePageSubmit = async (submitData: any) => {
        let response;
        switch (pageEditMode.value) {
            case 'create':
                // 新增页面
                response = await pageApi.addPage(initPageLayout({ ...submitData }));
                if (response.code === 200) {
                    refreshPageList();
                    feedback.success('新增页面成功');
                } else {
                    feedback.error('新增页面失败：' + response.msg);
                }
                break;
            case 'update':
                // 修改页面
                response = await pageApi.updatePage(submitData.code, initPageLayout({ ...submitData }));
                if (response.code === 200) {
                    refreshPageList();
                    feedback.success('更新页面成功');
                } else {
                    feedback.error('更新页面失败：' + response.msg);
                }
                break;
            case 'copy':
                // 复制页面
                response = await pageApi.copyPage(initPageLayout({ ...submitData }));
                if (response.code === 200) {
                    refreshPageList();
                    feedback.success('复制页面成功');
                } else {
                    feedback.error('复制页面失败：' + response.msg);
                }
                break;
        }
    };

    // 更新页面楼层
    const updatePageSectionOrder = async (data: any) => {
        const response = await pageApi.updatePageSectionOrder(data);
        if (response.code === 200) {
            feedback.success('更新页面楼层排序成功');
        } else {
            feedback.error('更新页面楼层排序失败：' + response.msg);
        }
    };
</script>
