<template>
    <el-drawer v-model="visible" direction="rtl" size="60%">
        <template #header>
            <span class="drawer-title">页面楼层</span>
        </template>
        <div class="sortable-tip">
            <span class="tip-icon">≡</span>
            拖动条目可调整顺序
        </div>
        <div class="draggable-wrapper">
            <vue-draggable
                v-model="editableData"
                ghost-class="ghost"
                :animation="200"
                handle=".handle"
                @end="updateOrderNo">
                <div
                    v-for="(section, index) in editableData"
                    :key="section.code"
                    :title="section.name || `楼层${index + 1}`"
                    :class="['section-item', { moved: section.orderNo !== section.originalOrderNo }]">
                    <div class="handle section-handle">
                        <span
                            class="order-circle"
                            :class="{
                                moved: section.orderNo !== section.originalOrderNo,
                            }"
                            >{{ section.orderNo }}</span
                        >
                        <span class="section-name">{{ section.name || `楼层${index + 1}` }}</span>
                        <span v-if="section.orderNo !== section.originalOrderNo" class="moved-tip">已调整</span>
                    </div>
                </div>
            </vue-draggable>
        </div>
        <template #footer>
            <div class="footer-buttons">
                <el-button @click="visible = false">取消</el-button>
                <el-button type="primary" @click="handleSave">保存 </el-button>
            </div>
        </template>
    </el-drawer>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';
    import { VueDraggable } from 'vue-draggable-plus';

    // 参数
    const props = defineProps({
        modelValue: Boolean,
        pageSectionList: Array,
    });

    // 事件
    const emit = defineEmits(['update:modelValue', 'save']);

    // pinia store
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 控制抽屉的显示
    const visible = ref(false);

    // 复制数据，避免直接修改 props
    const editableData = ref<any>([]);

    const updateOrderNo = () => {
        editableData.value.forEach((item: any, index: any) => {
            item.orderNo = index + 1;
        });
    };
    const handleSave = () => {
        // 3. 触发保存事件
        emit('save', editableData.value);

        // 4. 关闭抽屉
        visible.value = false;
    };

    watch(
        () => props.modelValue,
        (val) => (visible.value = val)
    );

    watch(
        () => visible.value,
        (val) => emit('update:modelValue', val)
    );

    watch(
        () => props.pageSectionList,
        (newPageSectionList: any) => {
            editableData.value = newPageSectionList.map((item: any) => ({
                ...item,
                isEditing: false,
                editingName: item.name,
                editingOrderNo: item.orderNo,
                originalOrderNo: item.orderNo,
            }));
        },
        { immediate: true }
    );
</script>
<style scoped>
    .draggable-wrapper {
        padding: 16px;
        background-color: #f9fafb;
        border-radius: 8px;
        max-height: 60vh;
        overflow-y: auto;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    }

    .section-item {
        padding: 10px 18px;
        margin-bottom: 10px;
        border: 1px solid #e4e7ed;
        border-radius: 8px;
        background-color: #fff;
        cursor: move;
        transition:
            box-shadow 0.2s,
            background 0.2s;
        display: flex;
        align-items: center;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.03);
    }

    .section-item.moved {
        background: #ffeaea;
        border-color: #ffb3b3;
    }

    .section-item:hover {
        box-shadow: 0 4px 16px rgba(255, 99, 71, 0.08);
        border-color: #409eff;
    }

    .section-handle {
        display: flex;
        align-items: center;
        gap: 12px;
        font-size: 15px;
        color: #444;
        width: 100%;
    }

    .order-circle {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: #f2f3f5;
        color: #888;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 15px;
        margin-right: 8px;
        border: 1.5px solid #e4e7ed;
        transition:
            background 0.2s,
            border 0.2s;
    }

    .order-circle.moved {
        background: #ffb3b3;
        color: #fff;
        border-color: #ff7875;
    }

    .section-name {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 500;
        color: #222;
    }

    .moved-tip {
        color: #ff4d4f;
        font-size: 13px;
        margin-left: 8px;
        font-weight: 500;
        letter-spacing: 1px;
    }

    .ghost {
        opacity: 0.4;
        background: #e0f3ff !important;
    }

    .footer-buttons {
        display: flex;
        justify-content: flex-end;
        padding: 16px 24px 0;
        gap: 16px;
        border-top: 1px solid #ebeef5;
        background: #fff;
    }

    .drawer-title {
        font-size: 18px;
        font-weight: bold;
        color: #222;
        letter-spacing: 1px;
    }

    .sortable-tip {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #409eff;
        font-size: 13px;
        margin: 0 0 10px 2px;
        user-select: none;
        font-weight: 500;
    }

    .tip-icon {
        font-size: 18px;
        font-weight: bold;
        line-height: 1;
        display: inline-block;
        margin-right: 2px;
    }
</style>
