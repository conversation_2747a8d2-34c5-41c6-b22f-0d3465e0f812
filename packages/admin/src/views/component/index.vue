<template>
    <PageContainer>
        <template #search>
            <component-search @search="handleSearchComponent" />
        </template>

        <template #toolbar>
            <component-toolbars
                :comPonentSelection="componentSelection"
                @handleUploadPackage="handleUploadPackage"
                @refreshComponentList="refreshComponentList" />
        </template>

        <template #list>
            <component-list
                ref="componentListRef"
                :component-search-form="componentSearchForm"
                @update:selection="handleSelection"
                @edit="handleEditeComponent"
                @show-style="showStyle" />
        </template>
    </PageContainer>
    <upload-component-package
        :uploadDialogVisible="uploadDialogVisible"
        @update:uploadDialogVisible="uploadDialogVisible = $event"
        @uploadSuccess="handleUploadSuccess" />

    <component-add
        v-model:visible="editDialogVisible"
        :initial-data="currentComponent"
        @submit="handleComponentSubmit" />
    <component-style-list v-model="showStyleVisible" :componentStyleList="componentStyleList" />
</template>

<script setup lang="ts">
    import { ref } from 'vue';
    import ComponentSearch from './compoents/component-search.vue';
    import ComponentList from './compoents/component-list.vue';
    import ComponentToolbars from './compoents/compoent-toolbars.vue';
    import { Component, ComponentSearchForm, ComponentStyle, ComponentStyleSearchForm } from '@smartdesk/common/types';
    import PageContainer from '../common/page-container.vue';
    import ComponentAdd from './compoents/component-add.vue';
    import { componentApi, componentStyleApi } from '@smartdesk/common/api';
    import { useFeedback } from '@smartdesk/common/composables';
    import ComponentStyleList from './compoents/component-style-list.vue';

    defineOptions({
        name: 'Component',
    });

    const componentSearchForm = ref<Partial<ComponentSearchForm>>({
        // 网站编码
        siteCode: '',
        delFlags: [],
        name: '',
        orgIds: [] as number[],
    });

    const feedback = useFeedback();

    // 绑定子组件
    const componentListRef = ref<InstanceType<typeof ComponentList> | null>(null);

    const componentSelection = ref<Component[]>([]);

    const uploadDialogVisible = ref(false);

    // 调用子组件的方法
    const refreshComponentList = () => {
        componentListRef.value?.findComponentList();
    };

    // 中转组件查询表单
    const handleSearchComponent = (data: Partial<ComponentSearchForm>) => {
        componentSearchForm.value = data;
    };

    // 选中的组件
    const handleSelection = (val: Component[]) => {
        componentSelection.value = val;
    };

    // 修改dialog
    const editDialogVisible = ref<boolean>(false);

    // 样式抽屉
    const showStyleVisible = ref<boolean>(false);

    // 组件样式
    const componentStyleList = ref<ComponentStyle[]>([] as ComponentStyle[]);

    // 组件样式查询表单
    const componentStyleForm = ref<ComponentStyleSearchForm>({} as ComponentStyleSearchForm);

    // 选择的组件
    const currentComponent = ref<Component>({} as Component);

    // 修改事件
    const handleEditeComponent = (componentForm: Component) => {
        editDialogVisible.value = true;
        currentComponent.value = componentForm;
    };

    // 修改组件
    const handleComponentSubmit = async (componentForm: Component) => {
        const response = await componentApi.updateComponent(componentForm.code, componentForm);
        if (response.code === 200) {
            refreshComponentList();
            feedback.success('更新页面成功');
        } else {
            feedback.error('更新页面失败：' + response.msg);
        }
    };

    // 查看组件样式
    const showStyle = async (component: Component) => {
        showStyleVisible.value = true;
        componentStyleForm.value.siteCode = component.siteCode;
        componentStyleForm.value.componentCode = component.code;
        const response = await componentStyleApi.findComponentStylePage(componentStyleForm.value, {
            paged: false,
        });
        componentStyleList.value = response.result;
    };

    // 上传组件包
    const handleUploadPackage = () => {
        uploadDialogVisible.value = true;
    };
    const handleUploadSuccess = () => {
        refreshComponentList();
    };
</script>
