<template>
    <icon-text-button
        :disabled="!permissionStore.hasPermission(ADMIN_BIZ_PERMISSION.COMPONENT.IMPORT)"
        :icon="Upload"
        text="导入组件"
        color="#66cccc"
        @click="handleUploadPackage" />
    <icon-text-button
        :disabled="
            !canBatchExport(comPonentSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.COMPONENT.EXPORT, {
                type: 'org',
                value: comPonentSelection.filter((component) => component.orgId).map((component) => component.orgId),
            })
        "
        :icon="Download"
        text="导出组件"
        color="#66cccc"
        @click="handleExportPackage" />
    <icon-text-button
        :disabled="
            !canBatchEnable(comPonentSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.COMPONENT.BATCH_ENABLE, {
                type: 'org',
                value: comPonentSelection.filter((component) => component.orgId).map((component) => component.orgId),
            })
        "
        :icon="CircleCheck"
        text="批量启用"
        color="#23bcca"
        @click="handleBatchEnable" />
    <icon-text-button
        :disabled="
            !canBatchDisable(comPonentSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.COMPONENT.BATCH_DISABLE, {
                type: 'org',
                value: comPonentSelection.filter((component) => component.orgId).map((component) => component.orgId),
            })
        "
        :icon="CircleClose"
        text="批量禁用"
        color="#ff9f25"
        @click="handleBatchDisEnable" />
    <icon-text-button
        :disabled="
            !canBatchDeleteByAdmin(comPonentSelection) ||
            !permissionStore.hasBatchPermission(ADMIN_BIZ_PERMISSION.COMPONENT.BATCH_DELETE, {
                type: 'org',
                value: comPonentSelection.filter((component) => component.orgId).map((component) => component.orgId),
            })
        "
        :icon="Delete"
        text="批量删除"
        color="#ed5665"
        @click="handleBatchDelete" />
</template>

<script setup lang="ts">
    import { CircleCheck, CircleClose, Delete, Download, Upload } from '@element-plus/icons-vue';
    import { Component } from '@smartdesk/common/types';
    import { componentApi } from '@smartdesk/common/api';
    import {
        ADMIN_BIZ_PERMISSION,
        canBatchDeleteByAdmin,
        canBatchDisable,
        canBatchEnable,
        canBatchExport,
    } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { useFeedback } from '@smartdesk/common/composables';
    import { computed, ref } from 'vue';

    // 参数
    const props = defineProps<{
        comPonentSelection: Component[];
    }>();

    // 事件
    const emit = defineEmits(['handleUploadPackage', 'refreshComponentList']);

    // pinia store
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 已选中组件列表的编码列表
    const codes = computed(() => {
        return props.comPonentSelection.map((item) => item.code);
    });

    // 上传组件包
    const handleUploadPackage = () => {
        emit('handleUploadPackage');
    };

    // 批量删除
    const handleBatchDelete = async () => {
        if (await feedback.confirm('确定要批量删除组件吗？', '确认操作', 'warning')) {
            const response = await componentApi.batchDeleteComponent(codes.value);
            if (response.code === 200) {
                feedback.success('批量删除成功');
                emit('refreshComponentList');
            } else {
                feedback.error('批量删除失败：' + response.msg);
            }
        }
    };

    // 防抖状态变量
    const exportLock = ref(false);
    const lastExportTime = ref(0);

    // 批量导出
    const handleExportPackage = async () => {
        const now = Date.now();

        if (exportLock.value || now - lastExportTime.value < 30000) {
            feedback.error('请勿频繁操作，请稍后再试');
            return;
        }

        exportLock.value = true;
        lastExportTime.value = now;
        if (await feedback.confirm('确定要批量导出组件吗？', '确认操作', 'warning')) {
            try {
                const blob = await componentApi.batchExportComponent(codes.value);

                // 创建浏览器下载行为
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `component.zip`;
                a.click();

                // 清理资源
                URL.revokeObjectURL(url);
                feedback.success(`组件导出成功`);
                emit('refreshComponentList');
            } catch (err) {
                feedback.error(`导出失败`);
            } finally {
                setTimeout(() => {
                    exportLock.value = false;
                }, 2000);
            }
        }
    };

    // 批量启用
    const handleBatchEnable = async () => {
        if (await feedback.confirm('确定要批量启用组件吗？', '确认操作', 'warning')) {
            const response = await componentApi.batchEnableComponent(codes.value);
            if (response.code === 200) {
                feedback.success('批量启用成功');
                emit('refreshComponentList');
            } else {
                feedback.error('批量启用失败：' + response.msg);
            }
        }
    };

    // 批量禁用
    const handleBatchDisEnable = async () => {
        if (await feedback.confirm('确定要批量禁用组件吗？', '确认操作', 'warning')) {
            const response = await componentApi.batchDisableComponent(codes.value);
            if (response.code === 200) {
                feedback.success('批量禁用成功');
                emit('refreshComponentList');
            } else {
                feedback.error('批量禁用失败：' + response.msg);
            }
        }
    };
</script>
