<template>
    <el-drawer v-model="visible" direction="rtl" size="60%">
        <template #header>
            <span class="drawer-title">组件样式</span>
        </template>
        <div>
            <el-table
                ref="multipleTableRef"
                :data="componentStyleLists"
                row-key="id"
                :header-cell-style="{ color: 'black', height: '50px' }">
                <el-table-column property="name" label="样式名称" />
                <el-table-column property="type" label="样式类型" />
                <el-table-column property="layout" label="样式">
                    <template #default="{ row }">
                        <el-popover placement="right" :width="500" trigger="click">
                            <template #reference>
                                <el-button text type="primary">查看</el-button>
                            </template>
                            <json-view v-if="row.layout" :json-data="row.layout" />
                        </el-popover>
                    </template>
                </el-table-column>
                <el-table-column property="category" label="组件分类">
                    <template #default="{ row }">
                        <el-tag v-if="row.category" type="success"
                            >{{ enumStore.getLabelByKeyAndValue('componentCategory', row.category) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="预览图" width="240">
                    <template #default="{ row }">
                        <el-image style="width: 200px; height: 100px" :src="row.icon">
                            <template #error>
                                <image-error-fallback text="图片损坏" />
                            </template>
                        </el-image>
                    </template>
                </el-table-column>
                <el-table-column label="修改时间">
                    <template #default="{ row }">
                        {{ row.modifiedTime ? format(row.modifiedTime, 'yyyy-MM-dd HH:mm:ss') : '' }}
                    </template>
                </el-table-column>
                <el-table-column min-width="160" property="modifiedBy" label="修改人" />
                <el-table-column label="操作" width="150">
                    <template #default="{ row }">
                        <el-button type="primary" size="small" @click="editLayout(row)">编辑 </el-button>
                    </template>
                </el-table-column>
            </el-table>
            <el-dialog v-model="jsonEditorVisible" title="组件样式编辑" width="50%">
                <el-form>
                    <el-form-item label="名称">
                        <el-input v-model="editingName" />
                    </el-form-item>
                    <el-form-item label="样式">
                        <el-input
                            type="textarea"
                            v-model="layoutJson"
                            :autosize="{ minRows: 10, maxRows: 20 }"
                            placeholder="请输入 JSON 格式" />
                    </el-form-item>
                </el-form>
                <template #footer>
                    <el-button @click="jsonEditorVisible = false">取消 </el-button>
                    <el-button type="primary" @click="saveLayoutJson">保存 </el-button>
                </template>
            </el-dialog>
        </div>
    </el-drawer>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import { ComponentStyle } from '@smartdesk/common/types';
    import { format } from 'date-fns';
    import { useEnumStore } from '@chances/portal_common_core';
    import { ElMessage } from 'element-plus';
    import { componentStyleApi } from '@smartdesk/common/api';
    import JsonView from '@smartdesk/common/components/json-view.vue';

    // 参数
    const props = defineProps<{
        modelValue: boolean;
        componentStyleList: ComponentStyle[];
    }>();

    const emit = defineEmits(['update:modelValue', 'save']);

    // pinia store
    const enumStore = useEnumStore();

    const jsonEditorVisible = ref(false); // 控制 JSON 编辑弹窗
    const layoutJson = ref(''); // 编辑区的 JSON 字符串
    const editingRow = ref<ComponentStyle>({} as ComponentStyle); // 当前正在编辑的行
    const editingName = ref(''); // 编辑区的名称

    // 打开编辑器
    const editLayout = (row: ComponentStyle) => {
        editingRow.value = row;
        editingName.value = row.name;
        try {
            layoutJson.value = JSON.stringify(row.layout, null, 2); // 美化格式
            jsonEditorVisible.value = true;
        } catch (e) {
            console.error('无法解析 layout 字段', e);
        }
    };

    // 保存 JSON 到原始数据
    const saveLayoutJson = () => {
        try {
            const parsed = JSON.parse(layoutJson.value); // 解析 JSON
            if (editingRow.value) {
                editingRow.value.layout = parsed; // 更新 layout
                editingRow.value.name = editingName.value; // 更新名称
            }
            jsonEditorVisible.value = false;
            componentStyleApi.updateComponentStyle(editingRow.value?.code || '', editingRow.value);
        } catch (e) {
            ElMessage.error('JSON 格式错误，请检查后再试');
        }
    };

    // 组件样式列表
    const componentStyleLists = ref<ComponentStyle[]>(props.componentStyleList);

    // 控制抽屉的显示
    const visible = ref(false);

    watch(
        () => props.modelValue,
        (val) => (visible.value = val)
    );

    watch(
        () => visible.value,
        (val) => emit('update:modelValue', val)
    );

    watch(
        () => props.componentStyleList,
        (newComponentStyleList) => (componentStyleLists.value = newComponentStyleList)
    );
</script>
