import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';

const routes: Array<RouteRecordRaw> = [
    {
        path: '/preview-page',
        name: 'PREVIEW',
        meta: {
            componentName: 'PREVIEW',
            layout: 'designer',
        },
        component: () => import('@smartdesk/preview/views/preview.vue'),
    },
    {
        path: '/preview-demo',
        name: 'PREVIEW-DEMO',
        meta: {
            componentName: 'PREVIEW-DEMO',
            layout: 'designer',
        },
        component: () => import('@smartdesk/preview/views/demo.vue'),
    },
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

export { routes };
export default router;
