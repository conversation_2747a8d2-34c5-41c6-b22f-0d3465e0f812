<template>
    <div class="contnet-body">
        <el-row style="align-items: center; padding: 10px" justify="space-between">
            <div style="flex: 1; min-width: 0">
                <el-form :model="jsvData" inline label-width="auto" :label-suffix="':'" :size="'default'">
                    <el-form-item label="业务账号UserID">{{ jsvData.aidlData.UserID }} </el-form-item>
                    <el-form-item label="机顶盒型号">
                        {{ getDeviceTypeName(jsvData.aidlData.STBType) }}
                    </el-form-item>
                    <el-form-item>
                        <el-popover :width="1200" placement="bottom" append-to-body>
                            <template #reference>
                                <el-button text style="color: rgb(22, 93, 255)">更多 </el-button>
                            </template>
                            <template #default>
                                <el-descriptions title="机顶盒信息" :column="2" :label-width="120" border>
                                    <el-descriptions-item label="业务账号(UserID)"
                                        >{{ jsvData.aidlData.UserID }}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="机顶盒型号"
                                        >{{ getDeviceTypeName(jsvData.aidlData.STBType) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="业务账号(userId)"
                                        >{{ jsvData.loginData.userId }}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="用户分组"
                                        >{{ getUserGroupName(jsvData.loginData.userGroup) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="用户区域"
                                        >{{ enumStore.getLabelByKeyAndValue('regionEnum', jsvData.loginData.areaCode) }}
                                    </el-descriptions-item>
                                    <el-descriptions-item label="子区域"
                                        >{{ getStbTypeNames(jsvData.loginData.subAreaCode) }}
                                    </el-descriptions-item>
                                </el-descriptions>
                                <el-descriptions
                                    style="padding-top: 5px"
                                    title="机顶盒信息"
                                    :column="2"
                                    :label-width="120"
                                    border>
                                    <el-descriptions-item
                                        v-for="(value, key) in jsvData.loginData.servers"
                                        :key="key"
                                        :label="key">
                                        {{ value }}
                                    </el-descriptions-item>
                                </el-descriptions>
                            </template>
                        </el-popover>
                    </el-form-item>
                </el-form>
            </div>
            <div style="display: flex; align-items: center; gap: 16px">
                <el-form inline label-width="auto" :label-suffix="':'" :size="'default'">
                    <el-form-item label="日期">
                        <el-date-picker
                            v-model="jsvData.aidlData.previewTime"
                            type="datetime"
                            format="YYYY/MM/DD hh:mm:ss"
                            value-format="x"
                            placeholder="请选择"
                            @change="refreshIframe" />
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" @click="onDeviceSetting">详细设置 </el-button>
                    </el-form-item>
                </el-form>
            </div>
        </el-row>
        <iframe
            ref="iframeRef"
            :src="iframeSrc"
            width="100%"
            height="720"
            frameborder="0"
            @load="handleIframeLoad"
            @error="handleIframeError"></iframe>
        <device-setting v-model="deviceSettingVisible" :jsvDate="jsvData" @submit="handleSubmitDeviceSetting" />
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref } from 'vue';
    import { DeviceSettingForm, JsvDate, Servers } from '@smartdesk/common/types';
    import DeviceSetting from './setting/device-setting.vue';
    import { previewApi } from '@smartdesk/common/api';
    import { userPreviewStore } from '@smartdesk/preview/stores';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import { useRoute } from 'vue-router';

    const jsvData = ref<JsvDate>({
        aidlData: {
            STBID: '009903000018059000013C0CDB2B48A4',
            STBType: 'UNT401H-1U5_GD',
            SoftwareVersion: '6.8.028.22v1',
            IP: 'dataNameNotExist',
            MAC: '3c:0c:db:2b:48:a4',
            DeviceID: '000018',
            DeviceCode: 'dataNameNotExist',
            UserID: 'test041409',
            jsessionid: '03UTMMBDT15O3N461519GS26IM4OQWCE',
            UserToken: 'FmLzvFmLzv763UIEVjvx4o900cwEQH2k',
            EPGServerURL: 'http://**************:33200',
            LastchannelNum: '1',
            Reserved: '020',
            Vendor: '',
            SupportMultiPlayer: '',
            platform: 'HW',
            epgUrl: 'null',
            previewTime: '1750126913552',
        },
        loginData: {
            userId: '7660233589',
            userGroup:
                '0|user_group_496,user_group_553,wheyd,user_group_514,whdjs,user_group_541,user_group_575,whdysu|GD_YD_001',
            areaCode: '760',
            subAreaCode: '222',
            servers: {
                code: '',
                name: '',
                biServer2: '',
                epgServerBackup: 'http://*************:8082/epg',
                aaaServer: 'http://*************:8082/epg',
                appStoreIndex: '',
                youkuProxyServer: '',
                updateServer: 'http://**************:58085/upgrade_api/',
                upgradeServer: 'http://**************:58085/upgrade_api/',
                aiqiyiProxyServer: '',
                orderServerBackup: '',
                searchServer: 'http://*************:8082/epg',
                appStoreLoginServer: '',
                tencentProxyServe: '',
                epgIndex: 'http://*************:8082/epg/api/page/biz_73351705.json',
                aaaServerBackup: 'http://*************:8082/epg',
                biServer: 'http://**************:8082',
                imageServer: 'http://**************:8082/epg/resource',
                epgIndex2: 'http://*************:8082/epg/api/page/biz_58411101.json',
                epgIndex3: 'http://*************:8082/epg/api/page/biz_57777408.json',
                webIndex: '',
                epgIndex4: 'http://*************:8082/epg/api/page/biz_58411101.json',
                orderServer: 'https://**************:9003/',
                logServer: 'http://**************:9001',
                updateServerBackup: 'http://**************:58085/upgrade_api/',
                epgServer: 'http://192.168.220.143/epg',
                userServerBackup: 'http://183.235.11.38:9002/tvportal',
                userServer: 'http://192.168.220.127:58023/tvportal',
                webServer: 'http://**************:8082/epg',
            },
        },
    });

    // iframe 的 src 用变量控制，方便刷新
    const iframeSrc = ref<string>('');
    const iframeSrcDB = ref<string>('');

    const previewStore = userPreviewStore();
    const enumStore = useEnumStore();
    const route = useRoute();
    const pageCode = route.query.pageCode ? String(route.query.pageCode) : '';

    const deviceSettingVisible = ref<boolean>(false);

    const iframeRef = ref<HTMLIFrameElement | null>(null);

    // 机顶盒型号
    const deviceTypeOption = ref<Enumeration[]>(enumStore.getEnumsByKey('deviceTypeEnum') || []);

    // 区域
    const areaCodesOption = ref<Enumeration[]>(enumStore.getEnumsByKey('regionEnum') || []);

    // 用户分组
    const userGroupOption = ref<Enumeration[]>(enumStore.getEnumsByKey('userGroupEnum') || []);

    // 发送jsvData到iframe
    const sendJsvDataToIframe = () => {
        if (iframeRef.value && iframeRef.value.contentWindow && iframeSrc.value) {
            const data = JSON.parse(JSON.stringify(jsvData.value)); // 保证可克隆
            const origin = new URL(iframeSrc.value).origin;
            iframeRef.value.contentWindow.postMessage(data, origin);
        }
    };

    // 点击机顶盒设置按钮
    const onDeviceSetting = () => {
        // 打开机顶盒设置 dialog
        deviceSettingVisible.value = true;
    };

    // 机顶盒设置确认
    const handleSubmitDeviceSetting = (deviceSettingForm: DeviceSettingForm, serversForm: Servers) => {
        jsvData.value.loginData.userId = deviceSettingForm.userId;
        jsvData.value.loginData.userGroup = deviceSettingForm.userGroup;
        jsvData.value.loginData.areaCode = deviceSettingForm.areaCode;
        jsvData.value.loginData.subAreaCode = deviceSettingForm.subAreaCode;

        jsvData.value.aidlData.UserID = deviceSettingForm.UserID;
        jsvData.value.aidlData.STBType = deviceSettingForm.STBType;
        jsvData.value.loginData.servers = { ...serversForm };
        deviceSettingVisible.value = false;
        refreshIframe();
    };

    const refreshIframe = () => {
        previewStore.saveJsvData(jsvData.value);

        // 假设 iframeSrcDB 是初始化的 iframe 页面地址
        const timestamp = new Date().getTime(); // 防止缓存
        iframeSrc.value = `${iframeSrcDB.value}?_t=${timestamp}`;

        // 等 iframe 加载完成后发送数据
        setTimeout(() => {
            iframeRef.value?.addEventListener(
                'load',
                () => {
                    sendJsvDataToIframe();
                },
                { once: true } // 避免多次绑定
            );
        }, 0);
    };

    // 获取预览地址
    const getPreviewUrl = async () => {
        const res = await previewApi.getPreviewUrl();
        if (res.code === 200) {
            const base = String(res.result);
            const url = new URL(base, window.location.origin);
            if (pageCode) url.searchParams.set('code', pageCode);
            iframeSrcDB.value = url.toString();
            iframeSrc.value = url.toString();
            refreshIframe();
        }
    };

    // 获取用户分组名称
    const getUserGroupName = (userGroup: string) => {
        if (!userGroup) {
            return '';
        }
        const userGroups = userGroup.split('｜');
        if (!userGroups?.length) return '';
        return userGroups
            .map((code) => {
                // 遍历一级枚举
                for (const group of userGroupOption.value) {
                    // 在二级枚举中查找
                    const found = group.children?.find((child) => child.code === code);
                    if (found) {
                        return found.name;
                    }
                }
                return code;
            })
            .join('|');
    };

    const getStbTypeNames = (code: string) => {
        if (!code) return '';
        // 遍历一级枚举
        for (const group of areaCodesOption.value) {
            // 在二级枚举中查找
            const found = group.children?.find((child) => child.code === code);
            if (found) {
                return found.name;
            }
        }
        return code;
    };

    const getDeviceTypeName = (code: string) => {
        if (!code) return '';
        // 遍历一级枚举
        for (const group of deviceTypeOption.value) {
            // 在二级枚举中查找
            const found = group.children?.find((child) => child.code === code);
            if (found) {
                return found.name;
            }
        }
        return code;
    };

    const handleIframeLoad = () => {
        sendJsvDataToIframe();
    };

    const handleIframeError = (e: any) => {
        console.error('iframe加载失败:', e);
    };

    onMounted(() => {
        getPreviewUrl();
    });
</script>

<style scoped>
    .contnet-body {
        height: calc(100vh - 20px);
    }

    .box-item {
        width: 700px;
        max-height: 400px;
        overflow-y: auto;
    }
</style>
