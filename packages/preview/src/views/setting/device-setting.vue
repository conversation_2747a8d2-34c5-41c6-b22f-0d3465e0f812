<template>
    <el-dialog v-if="visible" v-model="visible" width="50%" @close="onClickCancel">
        <template #header>
            <span class="text-lg">信息设置</span>
        </template>
        <el-tabs v-model="tabsModel">
            <el-tab-pane label="机顶盒设置" name="device">
                <el-form ref="formRef" :model="form" label-width="auto" label-suffix=":">
                    <el-form-item label="业务账号UserID" prop="UserID">
                        <el-input v-model="form.UserID" placeholder="请输入业务账号" />
                    </el-form-item>
                    <el-form-item label="业务账号userId" prop="userId">
                        <el-input v-model="form.userId" placeholder="请输入业务账号" />
                    </el-form-item>
                    <el-form-item label="机顶盒型号" prop="STBType">
                        <el-cascader
                            v-model="form.STBType"
                            placeholder="请选择机顶盒型号"
                            :show-all-levels="false"
                            :options="deviceTypeOption"
                            :props="{
                                label: 'name',
                                value: 'code',
                                children: 'children',
                                emitPath: false,
                            }"
                            filterable
                            clearable
                            style="width: 100%">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="用户分组" prop="userGroup">
                        <el-cascader
                            v-model="userGroupModel"
                            placeholder="请选择用户分组"
                            :show-all-levels="false"
                            :options="userGroupOption"
                            :props="{
                                label: 'name',
                                value: 'code',
                                children: 'children',
                                emitPath: false,
                                multiple: true,
                            }"
                            filterable
                            clearable
                            collapse-tags
                            style="width: 100%">
                        </el-cascader>
                    </el-form-item>
                    <el-form-item label="子区域" prop="userGroup">
                        <el-cascader
                            v-model="subAreaPath"
                            placeholder="请选择子区域"
                            :show-all-levels="false"
                            :options="areaCodesOption"
                            :props="{
                                label: 'name',
                                value: 'code',
                                children: 'children',
                                emitPath: true,
                            }"
                            filterable
                            clearable
                            style="width: 100%">
                        </el-cascader>
                    </el-form-item>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="服务器设置" name="service">
                <el-radio-group v-model="curreServerConfig" @change="handleServerChange" style="margin: 10px">
                    <el-radio
                        v-for="server in serverConfigs"
                        :key="server.code"
                        :label="server.code"
                        :value="server"
                        size="large"
                        border>
                        {{ server.name }}
                    </el-radio>
                </el-radio-group>
                <el-form :model="serversForm" label-width="auto" label-suffix=":">
                    <el-form-item label="biServer2" prop="biServer2">
                        <el-input v-model="serversForm.biServer2" placeholder="请输入biServer2" />
                    </el-form-item>
                    <el-form-item label="epgServerBackup" prop="epgServerBackup">
                        <el-input v-model="serversForm.epgServerBackup" placeholder="请输入epgServerBackup" />
                    </el-form-item>
                    <el-form-item label="aaaServer" prop="aaaServer">
                        <el-input v-model="serversForm.aaaServer" placeholder="请输入aaaServer" />
                    </el-form-item>
                    <el-form-item label="appStoreIndex" prop="appStoreIndex">
                        <el-input v-model="serversForm.appStoreIndex" placeholder="请输入appStoreIndex" />
                    </el-form-item>
                    <el-form-item label="youkuProxyServer" prop="youkuProxyServer">
                        <el-input v-model="serversForm.youkuProxyServer" placeholder="请输入youkuProxyServer" />
                    </el-form-item>
                    <el-form-item label="updateServer" prop="updateServer">
                        <el-input v-model="serversForm.updateServer" placeholder="请输入updateServer" />
                    </el-form-item>
                    <el-form-item label="upgradeServer" prop="upgradeServer">
                        <el-input v-model="serversForm.upgradeServer" placeholder="请输入upgradeServer" />
                    </el-form-item>
                    <el-form-item label="aiqiyiProxyServer" prop="aiqiyiProxyServer">
                        <el-input v-model="serversForm.aiqiyiProxyServer" placeholder="请输入aiqiyiProxyServer" />
                    </el-form-item>
                    <el-form-item label="orderServerBackup" prop="orderServerBackup">
                        <el-input v-model="serversForm.orderServerBackup" placeholder="请输入orderServerBackup" />
                    </el-form-item>
                    <el-form-item label="searchServer" prop="searchServer">
                        <el-input v-model="serversForm.searchServer" placeholder="请输入searchServer" />
                    </el-form-item>
                    <el-form-item label="appStoreLoginServer" prop="appStoreLoginServer">
                        <el-input v-model="serversForm.appStoreLoginServer" placeholder="请输入appStoreLoginServer" />
                    </el-form-item>
                    <el-form-item label="tencentProxyServe" prop="tencentProxyServe">
                        <el-input v-model="serversForm.tencentProxyServe" placeholder="请输入tencentProxyServe" />
                    </el-form-item>
                    <el-form-item label="epgIndex" prop="epgIndex">
                        <el-input v-model="serversForm.epgIndex" placeholder="请输入epgIndex" />
                    </el-form-item>
                    <el-form-item label="aaaServerBackup" prop="aaaServerBackup">
                        <el-input v-model="serversForm.aaaServerBackup" placeholder="请输入aaaServerBackup" />
                    </el-form-item>
                    <el-form-item label="biServer" prop="biServer">
                        <el-input v-model="serversForm.biServer" placeholder="请输入biServer" />
                    </el-form-item>
                    <el-form-item label="imageServer" prop="imageServer">
                        <el-input v-model="serversForm.imageServer" placeholder="请输入imageServer" />
                    </el-form-item>
                    <el-form-item label="epgIndex2" prop="epgIndex2">
                        <el-input v-model="serversForm.epgIndex2" placeholder="请输入epgIndex2" />
                    </el-form-item>
                    <el-form-item label="epgIndex3" prop="epgIndex3">
                        <el-input v-model="serversForm.epgIndex3" placeholder="请输入epgIndex3" />
                    </el-form-item>
                    <el-form-item label="webIndex" prop="webIndex">
                        <el-input v-model="serversForm.webIndex" placeholder="请输入webIndex" />
                    </el-form-item>
                    <el-form-item label="epgIndex4" prop="epgIndex4">
                        <el-input v-model="serversForm.epgIndex4" placeholder="请输入epgIndex4" />
                    </el-form-item>
                    <el-form-item label="orderServer" prop="orderServer">
                        <el-input v-model="serversForm.orderServer" placeholder="请输入orderServer" />
                    </el-form-item>
                    <el-form-item label="logServer" prop="logServer">
                        <el-input v-model="serversForm.logServer" placeholder="请输入logServer" />
                    </el-form-item>
                    <el-form-item label="updateServerBackup" prop="updateServerBackup">
                        <el-input v-model="serversForm.updateServerBackup" placeholder="请输入updateServerBackup" />
                    </el-form-item>
                    <el-form-item label="epgServer" prop="epgServer">
                        <el-input v-model="serversForm.epgServer" placeholder="请输入epgServer" />
                    </el-form-item>
                    <el-form-item label="userServerBackup" prop="userServerBackup">
                        <el-input v-model="serversForm.userServerBackup" placeholder="请输入userServerBackup" />
                    </el-form-item>
                    <el-form-item label="userServer" prop="userServer">
                        <el-input v-model="serversForm.userServer" placeholder="请输入userServer" />
                    </el-form-item>
                    <el-form-item label="webServer" prop="webServer">
                        <el-input v-model="serversForm.webServer" placeholder="请输入webServer" />
                    </el-form-item>
                </el-form>
            </el-tab-pane>
        </el-tabs>
        <template #footer>
            <el-button @click="onClickCancel">取消</el-button>
            <el-button type="primary" @click="onClickConfirm">确认 </el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref, watch } from 'vue';
    import { FormInstance } from 'element-plus';
    import { DeviceSettingForm, JsvDate, Servers } from '@smartdesk/common/types';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import { previewApi } from '@smartdesk/common/api';

    // 参数
    const props = defineProps<{
        modelValue: boolean;
        jsvDate: Partial<JsvDate>;
    }>();

    const enumStore = useEnumStore();

    const curreServerConfig = ref<Servers>({} as Servers);

    // 服务器配置
    const serverConfigs = ref<Servers[]>([] as Servers[]);
    // 机顶盒型号
    const deviceTypeOption = ref<Enumeration[]>(enumStore.getEnumsByKey('deviceTypeEnum') || []);

    // 用户分组
    const userGroupOption = ref<Enumeration[]>(enumStore.getEnumsByKey('userGroupEnum') || []);

    // 区域
    const areaCodesOption = ref<Enumeration[]>(enumStore.getEnumsByKey('regionEnum') || []);

    // 事件
    const emit = defineEmits(['update:modelValue', 'submit']);

    // 模态框显隐
    const visible = ref(false);
    // 表单引用
    const formRef = ref<FormInstance | null>(null);
    // 网站表单
    const form = ref<Partial<DeviceSettingForm>>({} as DeviceSettingForm);

    // 用户分组
    const userGroupModel = computed({
        get() {
            return form.value.userGroup?.split('|');
        },
        set(val: string[]) {
            // 将选择的数组再拼接回字符串格式
            form.value.userGroup = val.join('|');
        },
    });

    // 子区域路径
    const subAreaPath = ref<string[]>([]);

    // tabsModel
    const tabsModel = ref<string>('device');

    // 新增servers表单数据
    const serversForm = ref<Servers>({} as Servers);

    // 监听subAreaPath变化，自动设置form.areaCode和form.subAreaCode
    watch(subAreaPath, (val) => {
        if (val && val.length > 1) {
            form.value.areaCode = val[0];
            form.value.subAreaCode = val[1];
        } else if (val && val.length === 1) {
            form.value.areaCode = val[0];
            form.value.subAreaCode = '';
        } else {
            form.value.areaCode = '';
            form.value.subAreaCode = '';
        }
    });

    // 关闭对话框
    const onClickCancel = () => {
        emit('update:modelValue', false);
    };

    // 提交表单
    const onClickConfirm = () => {
        formRef.value?.validate((valid) => {
            if (valid) {
                emit('submit', form.value, serversForm.value);
            }
        });
    };

    // 选中事件
    const handleServerChange = (newServer: Servers) => {
        curreServerConfig.value = newServer;
        serversForm.value = { ...newServer };
    };

    // 获取服务器列表
    const getServerConfig = async () => {
        const res = await previewApi.getServerConfig();
        if (res.code === 200) {
            serverConfigs.value = res.result;
        }
    };
    // 监控 modelValue
    watch(
        () => props.modelValue,
        (val) => {
            visible.value = val;
            if (val) {
                if (props.jsvDate.loginData) {
                    serversForm.value = { ...props.jsvDate.loginData.servers };
                    form.value.userId = props.jsvDate.loginData.userId;
                }
                if (props.jsvDate.aidlData) {
                    form.value.UserID = props.jsvDate.aidlData.UserID;
                    form.value.STBType = props.jsvDate.aidlData.STBType;
                }
                // 初始化subAreaPath
                if (form.value.areaCode && form.value.subAreaCode) {
                    subAreaPath.value = [form.value.areaCode, form.value.subAreaCode];
                } else {
                    subAreaPath.value = [];
                }
            }
        }
    );

    onMounted(() => {
        getServerConfig();
    });
</script>
<style scoped>
    .el-cascader__dropdown :deep(.el-cascader-panel) {
        min-width: 100% !important;
    }
</style>
