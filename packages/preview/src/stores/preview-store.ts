import { defineStore } from 'pinia';
import { JsvDate } from '@smartdesk/common/types';
import { STORAGE_DRIVER } from '@chances/portal_common_core';

export const userPreviewStore = defineStore(
    'cs_jsv_mock',
    () => {
        const saveJsvData = (data: JsvDate) => {
            localStorage.setItem('jsvData', JSON.stringify(data));
        };

        const loadJsvData = (): JsvDate | null => {
            const raw = localStorage.getItem('jsvData');
            return raw ? JSON.parse(raw) : null;
        };
        return {
            saveJsvData,
            loadJsvData,
        };
    },
    {
        persist: {
            enabled: true,
            storeName: 'smart-desk-portal-store',
            driver: STORAGE_DRIVER.LOCAL_STORAGE,
            storeKey: 'cs_jsv_mock',
        },
    }
);
