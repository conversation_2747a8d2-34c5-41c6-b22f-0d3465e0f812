import { Pinia } from 'pinia';

// 预览相关
import { userPreviewStore } from './preview-store';

// 预览相关
export { userPreviewStore } from './preview-store';

// 注册所有 store
export default (piniaInstance?: Pinia) => {
    if (!piniaInstance) {
        console.warn('Pinia 实例不存在，stores 无法正常注册');
        return;
    }

    // 预览相关
    const previewStore = userPreviewStore(piniaInstance);

    return {
        // 预览相关
        previewStore,
    };
};
