import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import 'element-plus/dist/index.css';

import { createPersistPlugin, emitter, installAllStores, STORAGE_DRIVER } from '@chances/portal_common_core';
import '@chances/portal_common_core/dist/style.css';
import { createPinia } from 'pinia';
import installCommonStores from '@smartdesk/common/stores';

// 创建 APP 实例
const app: any = createApp(App);

// 注册 ElementPlus 相关组件、图标
app.use(ElementPlus);
// 注册所有 Element Plus 图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
}

// 创建 pinia 持久化插件
const persistPlugin = createPersistPlugin({
    defaultDatabase: 'smartdesk-preview',
    defaultDriver: STORAGE_DRIVER.LOCAL_STORAGE,
    timeout: 10000,
});

// 注册 pinia
const pinia = createPinia();
app.use(pinia);

// 注册持久化插件
app.use(persistPlugin);

// 注册公共依赖 store
installAllStores();

// 注册可视化公共 store
installCommonStores(pinia);

// 将 Mitt 实例注入到全局属性中
app.config.globalProperties.$emitter = emitter;

// 注册路由
app.use(router);

// 挂载实例
app.mount('#app');
