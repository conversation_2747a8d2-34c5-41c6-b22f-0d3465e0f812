import { defineConfig } from 'vite';
// @ts-ignore
import vue from '@vitejs/plugin-vue';
import dts from 'vite-plugin-dts';
import { resolve } from 'path';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import Components from 'unplugin-vue-components/vite';

export default defineConfig(({}) => {
    return {
        plugins: [
            vue(),
            dts({
                insertTypesEntry: true,
                copyDtsFiles: true,
            }),
            Components({
                resolvers: [
                    IconsResolver({
                        prefix: 'i',
                        enabledCollections: ['mdi'],
                    }),
                ],
                dts: false,
            }),
            Icons({
                compiler: 'vue3',
                autoInstall: true,
                scale: 1,
                defaultClass: 'inline-block',
            }),
        ],
        build: {
            lib: {
                entry: resolve(__dirname, 'src/index.ts'),
                name: '@smartdesk/preview',
                fileName: 'index',
                formats: ['es'],
            },
            rollupOptions: {
                external: ['vue'],
                output: {
                    globals: {
                        vue: 'Vue',
                    },
                },
            },
        },
        resolve: {
            alias: {
                '@smartdesk/preview': resolve(__dirname, 'src'),
                '@smartdesk/common': resolve(__dirname, '../common/src'),
            },
        },
    };
});
