{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noFallthroughCasesInSwitch": true, "declaration": true, "declarationMap": true, "baseUrl": ".", "paths": {"@smartdesk/common/*": ["./src/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}