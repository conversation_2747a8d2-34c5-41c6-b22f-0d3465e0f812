import { defineConfig } from 'vite';
// @ts-ignore
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import Components from 'unplugin-vue-components/vite';

export default defineConfig(({}) => {
    return {
        plugins: [
            vue(),
            Components({
                resolvers: [
                    IconsResolver({
                        prefix: 'i',
                        enabledCollections: ['mdi'],
                    }),
                ],
                dts: false,
            }),
            Icons({
                compiler: 'vue3',
                autoInstall: true,
                scale: 1,
                defaultClass: 'inline-block',
            }),
        ],
        build: {
            outDir: 'dist',
            rollupOptions: {
                input: resolve(__dirname, 'index.html'),
            },
        },
        resolve: {
            alias: {
                '@smartdesk/common': resolve(__dirname, 'src'),
            },
        },
    };
});
