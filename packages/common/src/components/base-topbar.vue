<template>
    <el-header class="rounded-full p-4 w-full">
        <div class="flex justify-between items-center px-2">
            <div class="flex items-center gap-2">
                <el-button text @click="onClickBack">
                    <el-icon>
                        <ArrowLeft />
                    </el-icon>
                    返回
                </el-button>
                <div>{{ props.title }}设计器</div>
            </div>

            <slot name="center" />

            <div class="flex items-center gap-2">
                <el-button v-if="showSave" :disabled="!canSave" type="primary" @click="onClickSave">
                    <i-mdi-content-save class="mr-2 w-4 h-4" />
                    保存
                </el-button>
                <el-button v-if="showPreview" :disabled="!canPreview" type="success" @click="onClickPreview">
                    <i-mdi-eye class="mr-2 w-4 h-4" />
                    预览
                </el-button>
                <el-button v-if="showPublish" :disabled="!canPublish" type="warning" @click="onClickPublish">
                    <i-mdi-share class="mr-2 w-4 h-4" />
                    送审{{ props.title }}
                </el-button>
                <el-button v-if="showPublishAll" :disabled="!canPublishAll" type="warning" @click="onClickPublishAll">
                    <i-mdi-share-all class="mr-2 w-4 h-4" />
                    全部送审
                </el-button>
            </div>
        </div>
    </el-header>
</template>

<script setup lang="ts">
    import { ArrowLeft } from '@element-plus/icons-vue';
    import { useFeedback } from '@smartdesk/common/composables';
    import { useRouter } from 'vue-router';

    // 基础工具条
    defineOptions({
        name: 'BaseTopBar',
    });

    // 参数
    const props = defineProps({
        title: {
            type: String,
            required: true,
        },
        showSave: {
            type: Boolean,
            default: true,
        },
        canSave: {
            type: Boolean,
            default: true,
        },
        showPreview: {
            type: Boolean,
            default: true,
        },
        canPreview: {
            type: Boolean,
            default: true,
        },
        showPublish: {
            type: Boolean,
            default: true,
        },
        canPublish: {
            type: Boolean,
            default: true,
        },
        showPublishAll: {
            type: Boolean,
            default: true,
        },
        canPublishAll: {
            type: Boolean,
            default: true,
        },
    });

    // 事件
    const emit = defineEmits<{
        (e: 'save'): void;
        (e: 'preview'): void;
        (e: 'publish'): void;
        (e: 'publish-all'): void;
    }>();

    // composable
    const feedback = useFeedback();
    const router = useRouter();

    // 点击返回
    const onClickBack = () => {
        router.back();
    };

    // 点击保存
    const onClickSave = async () => {
        const confirm = await feedback.confirm('是否确定保存当前更改？');
        if (confirm) {
            emit('save');
        }
    };

    // 点击预览
    const onClickPreview = () => {
        emit('preview');
    };

    // 送审
    const onClickPublish = async () => {
        const confirm = await feedback.confirm('是否确定送审 ' + props.title + '？');
        if (confirm) {
            emit('publish');
        }
    };

    // 全部送审
    const onClickPublishAll = async () => {
        const confirm = await feedback.confirm('是否确定全部送审？');
        if (confirm) {
            emit('publish-all');
        }
    };
</script>
