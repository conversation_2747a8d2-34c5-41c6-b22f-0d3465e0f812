<template>
    <div class="spacing-editor">
        <div class="input-group">
            <label for="left">左:</label>
            <el-input-number
                @change="changeHandler"
                v-model="localStyle.left"
                :placeholder="'左'"
                :aria-label="'Left Position'" />
            <!-- 输入框区域 -->
            <div class="input-group">
                <label for="top">上:</label>
                <el-input-number
                    v-model="localStyle.top"
                    @change="changeHandler"
                    :placeholder="'上'"
                    :aria-label="'Top Position'" />
            </div>
            <div class="input-group">
                <label for="right">右:</label>
                <el-input-number
                    @change="changeHandler"
                    v-model="localStyle.right"
                    :placeholder="'右'"
                    :aria-label="'Right'" />
            </div>
        </div>
        <div class="input-group">
            <label for="bottom">:</label>
            <el-input-number
                @change="changeHandler"
                v-model="localStyle.bottom"
                :placeholder="'下'"
                :aria-label="'Bottom'" />
        </div>
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';

    // 使用 ref 创建响应式数据

    const emit = defineEmits<{
        (e: 'update:modelValue', value: Record<string, any>): void;
        (e: 'change', value: Record<string, any>): void;
    }>();

    const props = defineProps<{
        modelValue: Record<string, any>;
        prefix?: string;
    }>();

    const localStyle = ref<any>({ top: 0, left: 0, right: 0, bottom: 0 });

    onMounted(() => {
        removePrefix();
    });

    watch(props.modelValue, () => {
        removePrefix();
    });

    const changeHandler = () => {
        const value = addPrefix();
        emit('update:modelValue', value);
        emit('change', value);
    };

    const addPrefix = () => {
        const value: any = localStyle.value;
        if (props.prefix) {
            for (const key in value) {
                props.modelValue[props.prefix + key.charAt(0).toUpperCase() + key.slice(1)] = value[key];
            }
            return props.modelValue;
        } else {
            for (const key in value) {
                props.modelValue[key] = value[key];
            }
            return props.modelValue;
        }
    };

    const removePrefix = () => {
        if (props.prefix) {
            for (const key in localStyle.value) {
                const newKey = key.replace(props.prefix, '').toLowerCase();
                const value = localStyle.value[key];
                localStyle.value[newKey] = value;
            }
        } else {
            for (const key in localStyle.value) {
                const value = localStyle.value[key];
                if (key === 'top') {
                    localStyle.value.top = value;
                } else if (key === 'left') {
                    localStyle.value.left = value;
                } else if (key === 'right') {
                    localStyle.value.right = value;
                } else if (key === 'bottom') {
                    localStyle.value.bottom = value;
                }
            }
        }
    };
</script>

<style scoped>
    .spacing-editor {
        display: grid;
        grid-template-columns: 1fr 1fr;
        grid-gap: 10px;
        margin-top: 10px;
        width: 300px;
        margin-bottom: 20px;
    }

    .input-group {
        display: flex;
        align-items: center;
        margin-top: 5px;
    }

    label {
        margin-right: 5px;
        font-weight: bold;
    }

    input {
        width: 70px;
        padding: 5px;
        text-align: center;
    }

    .target-element {
        position: absolute; /* 使用绝对定位来移动元素 */
        border: 2px solid #000;
        background-color: lightgray;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .target-element p {
        margin: 0;
    }
</style>
