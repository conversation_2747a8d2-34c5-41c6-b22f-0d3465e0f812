<template>
    <el-input v-model="searchKey" placeholder="输入关键字搜索" clearable @clear="reloadTree">
        <template #prefix>
            <el-icon>
                <Search />
            </el-icon>
        </template>
    </el-input>

    <el-tree
        ref="treeRef"
        :data="treeData"
        :props="{
            label: 'name',
            isLeaf: (data: TreeNodeData, node: Node) => false,
        }"
        :load="loadNode"
        lazy
        node-key="code"
        :highlight-current="true"
        :default-expanded-keys="expandedKeys"
        @check="handleCheck"
        show-checkbox
        check-strictly>
        <template #default="{ node, data }">
            <span v-html="highlightText(node.label, searchKey)"></span>
        </template>
    </el-tree>
</template>

<script setup lang="ts">
    import { nextTick, ref, watch, reactive } from 'vue';
    import { ElIcon, ElInput, ElTree } from 'element-plus';
    import { Search } from '@element-plus/icons-vue';
    import { RestResultResponse } from '@chances/portal_common_core';
    import type Node from 'element-plus/es/components/tree/src/model/node';
    import { TreeNodeData } from 'element-plus/es/components/tree/src/tree.type';

    interface TreeNode {
        code: string;
        name: string;
        children?: TreeNode[];
        isLeaf?: boolean;
        loaded?: boolean;
    }

    interface FetchParams {
        isRoot?: boolean;
        parentCode?: string;
        keyword?: string;
    }

    interface NodePath {
        node: TreeNode;
        path: TreeNode[];
    }

    const props = defineProps<{
        fetchApi: (params: FetchParams) => Promise<RestResultResponse<TreeNode[]>>;
    }>();

    const emit = defineEmits(['select']);

    // 树引用
    const treeRef = ref<InstanceType<typeof ElTree>>();
    // 关键字
    const searchKey = ref('');
    // 树数据
    const treeData = ref<TreeNode[]>([]);
    // 展开键
    const expandedKeys = ref<string[]>([]);
    // 查询模式，规避关键字查询的额外查询
    const isSearchMode = ref(false);
    // 当前选择的结点
    const currentSelectedNode = ref<string | null>(null);
    // 节点父级关系映射 (code -> parentCode)
    const nodeParentMap = reactive<Record<string, string>>({});
    // 节点缓存 (code -> node)
    const nodeCache = reactive<Record<string, TreeNode>>({});

    // 防抖处理
    let searchTimer: ReturnType<typeof setTimeout>;

    // 单选处理函数
    const handleCheck = (node: TreeNode, state: any) => {
        if (state.checkedKeys.includes(node.code)) {
            // 先清除之前选中的节点
            if (currentSelectedNode.value && currentSelectedNode.value !== node.code) {
                treeRef.value?.setChecked(currentSelectedNode.value, false, false);
            }
            // 更新当前选中的节点
            currentSelectedNode.value = node.code;

            // 获取节点路径并发出事件
            const nodePath = getNodePath(node);
            emit('select', nodePath);
        } else {
            // 如果当前节点被取消选中
            if (currentSelectedNode.value === node.code) {
                currentSelectedNode.value = null;
                emit('select', null);
            }
        }
    };

    // 获取节点及其祖先节点
    const getNodePath = (node: TreeNode): NodePath => {
        const path: TreeNode[] = [];
        let currentCode = node.code;

        // 添加当前节点
        path.unshift(node);

        // 向上查找父节点
        while (nodeParentMap[currentCode]) {
            const parentCode = nodeParentMap[currentCode];
            if (nodeCache[parentCode]) {
                path.unshift(nodeCache[parentCode]);
                currentCode = parentCode;
            } else {
                break;
            }
        }

        return {
            node,
            path,
        };
    };

    // 缓存节点并建立父子关系
    const cacheNode = (node: TreeNode, parentCode?: string) => {
        // 缓存节点
        nodeCache[node.code] = node;

        // 建立父子关系
        if (parentCode) {
            nodeParentMap[node.code] = parentCode;
        }
    };

    // 搜索功能
    watch(searchKey, (newVal) => {
        clearTimeout(searchTimer);
        searchTimer = setTimeout(async () => {
            if (newVal) {
                isSearchMode.value = true;
                const res = await props.fetchApi({ keyword: newVal });
                // 对搜索结果进行处理，使其不再触发懒加载
                treeData.value = processSearchNodes(res.result);
                await nextTick();
                expandAllNodes();

                // 恢复选中状态
                if (currentSelectedNode.value) {
                    await nextTick();
                    treeRef.value?.setChecked(currentSelectedNode.value, true, false);
                }
            } else {
                isSearchMode.value = false;
                await reloadTree();
            }
        }, 500);
    });

    // 懒加载实现
    const loadNode = async (node: any, resolve: (data: TreeNode[]) => void) => {
        // 如果是搜索模式，并且节点已有children，则不需要再请求
        if (isSearchMode.value && node.data && node.data.children) {
            resolve(node.data.children);
            return;
        }

        try {
            const params: FetchParams = node.level === 0 ? { isRoot: true } : { parentCode: node.data.code };

            const res = await props.fetchApi(params);
            const processedNodes = processNodes(res.result);

            // 记录父子关系
            if (node.level > 0) {
                processedNodes.forEach((childNode) => {
                    cacheNode(childNode, node.data.code);
                });
            } else {
                // 根节点直接缓存
                processedNodes.forEach((rootNode) => {
                    cacheNode(rootNode);
                });
            }

            resolve(processedNodes);
        } catch (error) {
            resolve([]);
        }
    };

    // 通用节点处理函数
    const processNodes = (nodes: TreeNode[]): TreeNode[] => {
        return nodes.map((node) => ({
            ...node,
            isLeaf: node.isLeaf ?? !node.children?.length,
        }));
    };

    // 处理搜索结果的节点，确保它们已加载完成
    const processSearchNodes = (nodes: TreeNode[]): TreeNode[] => {
        const processNode = (node: TreeNode, parentCode?: string): TreeNode => {
            // 缓存节点和父子关系
            cacheNode(node, parentCode);

            const processedNode = {
                ...node,
                isLeaf: node.isLeaf ?? !node.children?.length,
                // 标记为已加载，避免触发懒加载
                loaded: true,
            };

            if (node.children && node.children.length > 0) {
                processedNode.children = node.children.map((child) => processNode(child, node.code));
            }

            return processedNode;
        };

        return nodes.map((node) => processNode(node));
    };

    // 高亮文本
    const highlightText = (text: string, search: string) => {
        if (!search) return text;
        const regex = new RegExp(`(${search})`, 'gi');
        return text.replace(regex, '<span class="text-red-500 font-bold">$1</span>');
    };

    // 重新加载树
    const reloadTree = async () => {
        expandedKeys.value = [];
        // 不清空节点映射，保留已加载的节点关系
        const res = await props.fetchApi({ isRoot: true });
        const processedNodes = processNodes(res.result);

        // 更新根节点缓存
        processedNodes.forEach((rootNode) => {
            cacheNode(rootNode);
        });

        treeData.value = processedNodes;

        // 恢复选中状态
        if (currentSelectedNode.value) {
            await nextTick();
            treeRef.value?.setChecked(currentSelectedNode.value, true, false);
        }
    };

    // 搜索时展开所有节点
    const expandAllNodes = () => {
        if (!treeRef.value) return;

        // 收集所有节点的 key
        const collectAllKeys = (nodes: TreeNode[]): string[] => {
            let keys: string[] = [];
            nodes.forEach((node) => {
                keys.push(node.code);
                if (node.children && node.children.length) {
                    keys = keys.concat(collectAllKeys(node.children));
                }
            });
            return keys;
        };

        expandedKeys.value = collectAllKeys(treeData.value);
    };

    defineExpose({
        // 获取当前选中节点及其路径
        getSelectedNode: () => {
            if (!currentSelectedNode.value) return null;

            // 获取当前选中的节点对象
            const node = nodeCache[currentSelectedNode.value];
            if (!node) return null;

            // 获取节点路径
            return getNodePath(node);
        },
        // 设置获取当前选中节点
        setSelectedNode: (nodeCode: string) => {
            if (treeRef.value) {
                treeRef.value.setChecked(nodeCode, true, false);
                currentSelectedNode.value = nodeCode;
            }
        },
        // 清除当前选中节点
        clearSelection: () => {
            if (currentSelectedNode.value && treeRef.value) {
                treeRef.value.setChecked(currentSelectedNode.value, false, false);
                currentSelectedNode.value = null;
            }
        },
    });
</script>

<style scoped>
    .el-tree {
        margin-top: 10px;
        max-height: 400px;
        overflow-y: auto;
    }
</style>
