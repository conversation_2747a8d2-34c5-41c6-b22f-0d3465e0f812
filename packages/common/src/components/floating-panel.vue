<template>
    <div
        v-if="visible"
        class="floating-panel-content fixed bg-white border border-gray-200 rounded-lg shadow-xl z-50"
        :style="panelStyle"
        @mouseenter="onPanelMouseEnter"
        @mouseleave="onPanelMouseLeave"
        @click.stop>
        <slot />
    </div>
</template>

<script setup lang="ts">
    import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue';

    interface Props {
        visible: boolean;
        triggerElement?: HTMLElement | null;
        width?: number;
        maxHeight?: number;
        placement?: 'right' | 'left' | 'top' | 'bottom' | 'auto';
        offset?: number;
        hideDelay?: number;
    }

    interface Emits {
        (e: 'update:visible', value: boolean): void;

        (e: 'panel-enter'): void;

        (e: 'panel-leave'): void;
    }

    const props = withDefaults(defineProps<Props>(), {
        width: 320,
        maxHeight: 400,
        placement: 'auto',
        offset: 10,
        hideDelay: 100,
    });

    const emit = defineEmits<Emits>();

    // 面板样式
    const panelStyle = ref<Record<string, string>>({});

    // 悬浮状态
    const isHoveringPanel = ref(false);
    const hideTimer = ref<number | null>(null);

    // 计算面板的最佳位置
    const calculatePanelPosition = (targetRect: DOMRect) => {
        const panelWidth = props.width;
        const panelHeight = props.maxHeight;
        const gap = props.offset;

        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let left: number;
        let top: number;

        switch (props.placement) {
            case 'right':
                left = targetRect.right + gap;
                top = targetRect.top;
                break;
            case 'left':
                left = targetRect.left - panelWidth - gap;
                top = targetRect.top;
                break;
            case 'top':
                left = targetRect.left;
                top = targetRect.top - panelHeight - gap;
                break;
            case 'bottom':
                left = targetRect.left;
                top = targetRect.bottom + gap;
                break;
            case 'auto':
            default:
                // 默认尝试右侧
                left = targetRect.right + gap;
                top = targetRect.top;

                // 水平位置调整
                if (left + panelWidth > viewportWidth) {
                    // 右侧空间不足，显示在左侧
                    left = targetRect.left - panelWidth - gap;

                    // 如果左侧也不够，则贴着右边缘显示
                    if (left < 0) {
                        left = viewportWidth - panelWidth - gap;
                    }
                }
                break;
        }

        // 垂直位置调整（所有placement都需要）
        if (top + panelHeight > viewportHeight) {
            // 底部空间不足，向上调整
            top = viewportHeight - panelHeight - gap;

            // 确保不超出顶部
            if (top < gap) {
                top = gap;
            }
        }

        // 确保不超出边界
        if (left < gap) {
            left = gap;
        }
        if (top < gap) {
            top = gap;
        }

        return { left, top };
    };

    // 更新面板位置
    const updatePanelPosition = async () => {
        if (!props.triggerElement || !props.visible) return;

        await nextTick();
        const rect = props.triggerElement.getBoundingClientRect();
        const position = calculatePanelPosition(rect);

        panelStyle.value = {
            left: `${position.left}px`,
            top: `${position.top}px`,
            width: `${props.width}px`,
            maxHeight: `${props.maxHeight}px`,
            overflowY: 'auto',
        };
    };

    // 面板鼠标进入
    const onPanelMouseEnter = () => {
        isHoveringPanel.value = true;
        emit('panel-enter');

        // 清除隐藏定时器
        if (hideTimer.value !== null) {
            window.clearTimeout(hideTimer.value);
            hideTimer.value = null;
        }
    };

    // 面板鼠标离开
    const onPanelMouseLeave = (event: MouseEvent) => {
        // 检查是否移动到了下拉框或颜色选择器等弹出层
        const relatedTarget = event.relatedTarget as HTMLElement;
        if (
            relatedTarget &&
            (relatedTarget.closest('.el-select-dropdown') ||
                relatedTarget.closest('.el-color-picker__panel') ||
                relatedTarget.closest('.el-popper') ||
                relatedTarget.closest('[role="tooltip"]') ||
                relatedTarget.closest('[role="dialog"]'))
        ) {
            // 如果移动到了弹出层，不隐藏面板
            return;
        }

        isHoveringPanel.value = false;
        emit('panel-leave');

        // 延迟隐藏
        hideTimer.value = window.setTimeout(() => {
            emit('update:visible', false);
        }, props.hideDelay);
    };

    // 外部调用的隐藏方法
    const hide = () => {
        if (!isHoveringPanel.value) {
            hideTimer.value = window.setTimeout(() => {
                emit('update:visible', false);
            }, props.hideDelay);
        }
    };

    // 立即隐藏
    const hideImmediately = () => {
        if (hideTimer.value !== null) {
            window.clearTimeout(hideTimer.value);
            hideTimer.value = null;
        }
        emit('update:visible', false);
    };

    // 监听visible变化，更新位置
    watch(
        () => props.visible,
        (newVisible) => {
            if (newVisible) {
                updatePanelPosition();
            } else {
                // 清除定时器
                if (hideTimer.value !== null) {
                    window.clearTimeout(hideTimer.value);
                    hideTimer.value = null;
                }
            }
        }
    );

    // 监听triggerElement变化，更新位置
    watch(
        () => props.triggerElement,
        () => {
            if (props.visible) {
                updatePanelPosition();
            }
        }
    );

    // 处理全局点击事件
    const handleGlobalClick = (event: MouseEvent) => {
        if (!props.visible) return;

        const target = event.target as HTMLElement;
        const panel = document.querySelector('.floating-panel-content');
        const trigger = props.triggerElement;

        // 如果点击在面板内、触发元素内或相关弹出层内，不隐藏
        if (
            panel?.contains(target) ||
            trigger?.contains(target) ||
            target.closest('.el-select-dropdown') ||
            target.closest('.el-color-picker__panel') ||
            target.closest('.el-popper') ||
            target.closest('[role="tooltip"]') ||
            target.closest('[role="dialog"]')
        ) {
            return;
        }

        // 否则隐藏面板
        emit('update:visible', false);
    };

    // 生命周期管理
    onMounted(() => {
        document.addEventListener('click', handleGlobalClick, true);
    });

    onUnmounted(() => {
        document.removeEventListener('click', handleGlobalClick, true);
        if (hideTimer.value !== null) {
            window.clearTimeout(hideTimer.value);
        }
    });

    // 暴露方法给父组件
    defineExpose({
        hide,
        hideImmediately,
        updatePosition: updatePanelPosition,
    });
</script>
