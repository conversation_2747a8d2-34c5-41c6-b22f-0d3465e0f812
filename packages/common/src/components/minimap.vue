<template>
    <div class="bg-white rounded shadow-md select-none">
        <div class="relative w-[200px] h-[150px] border border-gray-200 overflow-hidden" @mousedown="handleMinimapDrag">
            <canvas
                ref="minimapCanvas"
                class="absolute top-0 left-0 w-full h-full"
                :width="MINIMAP_WIDTH"
                :height="MINIMAP_HEIGHT" />
            <div
                class="absolute border-2 border-blue-500/50 bg-blue-500/10"
                :style="viewportIndicatorStyle"
                @mousedown.stop="handleMinimapDrag"></div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, onBeforeUnmount, onMounted, ref, watch } from 'vue';

    interface Props {
        // 内容区域最大宽度
        maxWidth: number;
        // 当前缩放比例
        scale: number;
        // 当前内容位置
        position: {
            x: number;
            y: number;
        };
        // 内容容器选择器
        containerSelector?: string;
    }

    // 参数
    const props = withDefaults(defineProps<Props>(), {
        containerSelector: '#base-canvas-container > div',
    });

    // 事件
    const emit = defineEmits<{
        (e: 'update:position', position: { x: number; y: number }): void;
    }>();

    // Minimap 常量
    const MINIMAP_WIDTH = 200;
    const MINIMAP_HEIGHT = 150;

    // 状态
    const isMinimapDragging = ref(false);
    const minimapCanvas = ref<HTMLCanvasElement | null>(null);

    // 计算 minimap 的缩放比例
    const minimapScale = computed(() => {
        const scaleX = MINIMAP_WIDTH / props.maxWidth;
        const scaleY = MINIMAP_HEIGHT / window.innerHeight;
        return Math.min(scaleX, scaleY);
    });

    // 计算视口指示器的样式
    const viewportIndicatorStyle = computed(() => {
        const scale = minimapScale.value;
        const containerWidth = window.innerWidth - 60; // 减去标尺宽度
        const containerHeight = window.innerHeight - 30; // 减去标尺高度

        const width = Math.min(MINIMAP_WIDTH, containerWidth * scale);
        const height = Math.min(MINIMAP_HEIGHT, containerHeight * scale);

        const left = -props.position.x * scale;
        const top = -props.position.y * scale;

        return {
            width: `${width}px`,
            height: `${height}px`,
            transform: `translate(${left}px, ${top}px)`,
        };
    });

    // 获取内容元素信息
    const getContentElements = () => {
        const container = document.querySelector(props.containerSelector) as HTMLElement;
        if (!container) return [];

        // 获取所有直接子元素
        const elements = Array.from(container.children);

        return elements.map((element) => {
            const rect = (element as HTMLElement).getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();

            // 计算相对于容器的位置
            return {
                x: (rect.left - containerRect.left) / props.scale,
                y: (rect.top - containerRect.top) / props.scale,
                width: rect.width / props.scale,
                height: rect.height / props.scale,
                color: window.getComputedStyle(element).backgroundColor || '#2563eb',
            };
        });
    };

    // 更新 minimap 内容
    const updateMinimap = () => {
        const canvas = minimapCanvas.value;
        const content = document.querySelector(props.containerSelector) as HTMLElement;
        if (!canvas || !content) return;

        const ctx = canvas.getContext('2d');
        if (!ctx) return;

        // 清空画布
        ctx.clearRect(0, 0, MINIMAP_WIDTH, MINIMAP_HEIGHT);

        // 设置背景
        ctx.fillStyle = '#f5f5f5';
        ctx.fillRect(0, 0, MINIMAP_WIDTH, MINIMAP_HEIGHT);

        // 计算缩放比例
        const scale = minimapScale.value;

        // 绘制网格
        ctx.strokeStyle = '#e5e7eb';
        ctx.lineWidth = 0.5;
        const gridSize = 20 * scale;

        for (let x = 0; x <= MINIMAP_WIDTH; x += gridSize) {
            ctx.beginPath();
            ctx.moveTo(x, 0);
            ctx.lineTo(x, MINIMAP_HEIGHT);
            ctx.stroke();
        }

        for (let y = 0; y <= MINIMAP_HEIGHT; y += gridSize) {
            ctx.beginPath();
            ctx.moveTo(0, y);
            ctx.lineTo(MINIMAP_WIDTH, y);
            ctx.stroke();
        }

        // 绘制内容区域边界
        ctx.strokeStyle = '#94a3b8';
        ctx.lineWidth = 1;
        ctx.strokeRect(0, 0, props.maxWidth * scale, window.innerHeight * scale);

        // 获取并绘制内容元素
        const elements = getContentElements();
        elements.forEach((item) => {
            ctx.fillStyle = item.color;
            ctx.fillRect(item.x * scale, item.y * scale, item.width * scale, item.height * scale);
        });
    };

    // Minimap 拖拽处理
    const handleMinimapDrag = (e: MouseEvent) => {
        isMinimapDragging.value = true;
        const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
        const scale = minimapScale.value;

        // 计算点击位置相对于 minimap 的偏移
        const offsetX = (e.clientX - rect.left) / scale;
        const offsetY = (e.clientY - rect.top) / scale;

        // 更新位置，考虑视口中心
        const containerWidth = window.innerWidth - 60;
        const containerHeight = window.innerHeight - 30;

        const newX = -offsetX + containerWidth / 2;
        const newY = -offsetY + containerHeight / 2;

        // 应用限制
        const limitedX = Math.min(0, Math.max(-(props.maxWidth - containerWidth), newX));
        const limitedY = Math.min(0, newY);

        emit('update:position', { x: limitedX, y: limitedY });

        const handleMinimapMove = (e: MouseEvent) => {
            if (!isMinimapDragging.value) return;

            const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
            const scale = minimapScale.value;

            const offsetX = (e.clientX - rect.left) / scale;
            const offsetY = (e.clientY - rect.top) / scale;

            const newX = -offsetX + containerWidth / 2;
            const newY = -offsetY + containerHeight / 2;

            const limitedX = Math.min(0, Math.max(-(props.maxWidth - containerWidth), newX));
            const limitedY = Math.min(0, newY);

            emit('update:position', { x: limitedX, y: limitedY });
        };

        const handleMinimapUp = () => {
            isMinimapDragging.value = false;
            window.removeEventListener('mousemove', handleMinimapMove);
            window.removeEventListener('mouseup', handleMinimapUp);
        };

        window.addEventListener('mousemove', handleMinimapMove);
        window.addEventListener('mouseup', handleMinimapUp);
    };

    // 创建 MutationObserver 来监听内容变化
    const setupContentObserver = (): MutationObserver | null => {
        const container = document.querySelector(props.containerSelector);
        if (!container) return null;

        const observer = new MutationObserver(() => {
            requestAnimationFrame(updateMinimap);
        });

        observer.observe(container, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['style', 'class'],
        });

        return observer;
    };

    // 监听变化并更新 minimap
    watch(
        [() => props.scale, () => props.position],
        () => {
            requestAnimationFrame(updateMinimap);
        },
        { deep: true }
    );

    let contentObserver: MutationObserver | null = null;

    onMounted(() => {
        updateMinimap(); // 初始化 minimap

        // 设置内容观察器
        contentObserver = setupContentObserver();

        // 监听窗口大小变化
        window.addEventListener('resize', updateMinimap);
    });

    onBeforeUnmount(() => {
        window.removeEventListener('resize', updateMinimap);

        // 清理内容观察器
        if (contentObserver) {
            contentObserver.disconnect();
        }
    });
</script>
