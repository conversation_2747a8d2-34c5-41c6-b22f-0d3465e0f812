<template>
    <el-container
        class="flex w-full h-full overflow-hidden relative three-panel-layout"
        :class="{ 'user-select-none': isResizing }">
        <el-header
            v-if="$slots.top"
            class="w-full overflow-hidden p-0 border-b border-gray-200 flex items-center"
            :style="props.headerHeight ? { height: `${props.headerHeight}px` } : {}">
            <slot name="top"></slot>
        </el-header>

        <el-container class="flex-1 w-full overflow-hidden">
            <div v-if="!leftVisibleState" class="absolute top-1/2 -translate-y-1/2 left-0 z-20">
                <el-button
                    @click="toggleLeftPanel"
                    size="small"
                    circle
                    class="flex items-center justify-center shadow-sm !w-8 !h-8">
                    <el-icon>
                        <arrow-right />
                    </el-icon>
                </el-button>
            </div>

            <el-aside
                v-show="leftVisibleState"
                class="h-full overflow-hidden relative border-l border-gray-200"
                :style="{ width: `${leftWidth}px` }"
                ref="leftPanelRef">
                <div class="h-full overflow-hidden">
                    <slot name="left"></slot>
                </div>
            </el-aside>

            <div v-show="leftVisibleState" class="relative h-full">
                <div
                    class="w-1 h-full cursor-ew-resize bg-gray-100 hover:bg-blue-100 active:bg-blue-200 transition-colors duration-200 z-10 relative flex items-center justify-center"
                    @mousedown="startResize('left', $event)">
                    <div class="absolute top-1/2 -translate-y-1/2 -ml-4 left-0 z-20" @mousedown.stop>
                        <el-button
                            v-if="leftArrowVisible"
                            @click="toggleLeftPanel"
                            size="small"
                            circle
                            class="flex items-center justify-center shadow-sm !w-8 !h-8">
                            <el-icon>
                                <arrow-left />
                            </el-icon>
                        </el-button>
                    </div>
                </div>
            </div>

            <el-main
                class="h-full overflow-hidden relative flex-1 p-0"
                :style="{ width: `${centerWidth}px` }"
                ref="centerPanelRef">
                <div class="h-full overflow-hidden">
                    <slot name="center"></slot>
                </div>
            </el-main>

            <div v-show="rightVisibleState" class="relative h-full">
                <div
                    class="w-1 h-full cursor-ew-resize bg-gray-100 hover:bg-blue-100 active:bg-blue-200 transition-colors duration-200 z-10 relative flex items-center justify-center"
                    @mousedown="startResize('right', $event)">
                    <div class="absolute top-1/2 -translate-y-1/2 -mr-4 right-0 z-20" @mousedown.stop>
                        <el-button
                            v-if="rightArrowVisible"
                            @click="toggleRightPanel"
                            size="small"
                            circle
                            class="flex items-center justify-center shadow-sm !w-8 !h-8">
                            <el-icon>
                                <arrow-right />
                            </el-icon>
                        </el-button>
                    </div>
                </div>
            </div>

            <el-aside
                v-show="rightVisibleState"
                class="h-full overflow-hidden relative border-l border-gray-200"
                :style="{ width: `${rightWidth}px` }"
                ref="rightPanelRef">
                <div class="h-full overflow-hidden">
                    <slot name="right"></slot>
                </div>
            </el-aside>

            <div v-if="!rightVisibleState" class="absolute top-1/2 -translate-y-1/2 right-0 z-20">
                <el-button
                    @click="toggleRightPanel"
                    size="small"
                    circle
                    class="flex items-center justify-center shadow-sm !w-8 !h-8">
                    <el-icon>
                        <arrow-left />
                    </el-icon>
                </el-button>
            </div>
        </el-container>
    </el-container>
</template>

<script lang="ts" setup>
    import { computed, onBeforeUnmount, onMounted, PropType, provide, ref, watch } from 'vue';
    import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

    // 参数
    const props = defineProps({
        // 左侧面板默认宽度
        defaultLeftWidth: {
            type: Number as PropType<number>,
            default: 360,
        },
        // 右侧面板默认宽度
        defaultRightWidth: {
            type: Number as PropType<number>,
            default: 360,
        },
        // 左侧面板最小宽度
        minLeftWidth: {
            type: Number as PropType<number>,
            default: 100,
        },
        // 中心面板最小宽度
        minCenterWidth: {
            type: Number as PropType<number>,
            default: 300,
        },
        // 右侧面板最小宽度
        minRightWidth: {
            type: Number as PropType<number>,
            default: 100,
        },
        // 左侧面板是否可见
        leftVisible: {
            type: Boolean as PropType<boolean>,
            default: true,
        },
        // 左侧箭头是否可见
        leftArrowVisible: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
        // 右侧面板是否可见
        rightVisible: {
            type: Boolean as PropType<boolean>,
            default: true,
        },
        // 右侧箭头是否可见
        rightArrowVisible: {
            type: Boolean as PropType<boolean>,
            default: false,
        },
        // 顶部区域高度
        headerHeight: {
            type: Number as PropType<number>,
            default: 0,
        },
    });

    // 事件
    const emit = defineEmits([
        // 更新左侧宽度
        'update:leftWidth',
        // 更新中心宽度
        'update:centerWidth',
        // 更新右侧宽度
        'update:rightWidth',
        // 更新左侧面板是否可见
        'update:leftVisible',
        // 更新右侧面板是否可见
        'update:rightVisible',
        // 重制宽度
        'resize',
    ]);

    // 面板引用
    const leftPanelRef = ref<HTMLElement | null>(null);
    const centerPanelRef = ref<HTMLElement | null>(null);
    const rightPanelRef = ref<HTMLElement | null>(null);

    // 宽度状态
    const leftWidth = ref(props.defaultLeftWidth);
    const rightWidth = ref(props.defaultRightWidth);
    const containerWidth = ref(0);

    // 调整状态
    const isResizing = ref(false);
    const currentHandle = ref<'left' | 'right' | null>(null);
    const startX = ref(0);
    const startLeftWidth = ref(0);
    const startRightWidth = ref(0);

    // 面板可见状态
    const leftVisibleState = ref(props.leftVisible);
    const rightVisibleState = ref(props.rightVisible);

    // 计算中间区域的宽度
    const centerWidth = computed(() => {
        const total = containerWidth.value;
        const left = leftVisibleState.value ? leftWidth.value : 0;
        const right = rightVisibleState.value ? rightWidth.value : 0;

        // 容器总宽度 - （如果左侧可见）左侧面板宽度 - （如果右侧可见）右侧面板宽度 - 标尺的宽度 - 滚动条宽度，取计算结果与最小中心宽度的最大值
        return Math.max(total - left - right - 30 - 15, props.minCenterWidth);
    });

    // provide centerWidth 给子组件
    provide('centerWidth', centerWidth);

    // 切换左侧面板
    const toggleLeftPanel = () => {
        leftVisibleState.value = !leftVisibleState.value;
        emit('update:leftVisible', leftVisibleState.value);

        // 等待 DOM 更新后重新计算容器宽度
        setTimeout(() => {
            updateContainerWidth();
        }, 0);
    };

    // 切换右侧面板
    const toggleRightPanel = () => {
        rightVisibleState.value = !rightVisibleState.value;
        emit('update:rightVisible', rightVisibleState.value);

        // 等待DOM更新后重新计算容器宽度
        setTimeout(() => {
            updateContainerWidth();
        }, 0);
    };

    // 开始调整大小
    const startResize = (handle: 'left' | 'right', event: MouseEvent) => {
        // 如果点击的是按钮，不启动拖拽
        if ((event.target as HTMLElement).closest('button')) {
            return;
        }

        isResizing.value = true;
        currentHandle.value = handle;
        startX.value = event.clientX;
        startLeftWidth.value = leftWidth.value;
        startRightWidth.value = rightWidth.value;

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', stopResize);

        // 确保容器宽度已正确设置
        updateContainerWidth();

        // 阻止默认事件以避免文本选择
        event.preventDefault();
    };

    // 处理鼠标移动
    const handleMouseMove = (event: MouseEvent) => {
        if (!isResizing.value) return;

        const deltaX = event.clientX - startX.value;

        if (currentHandle.value === 'left') {
            // 调整左侧面板宽度
            const newLeftWidth = Math.max(props.minLeftWidth, startLeftWidth.value + deltaX);

            // 确保不会导致中间区域小于最小宽度
            const maxLeftWidth =
                containerWidth.value - (rightVisibleState.value ? rightWidth.value : 0) - props.minCenterWidth;

            leftWidth.value = Math.min(newLeftWidth, maxLeftWidth);
            emit('update:leftWidth', leftWidth.value);
        } else if (currentHandle.value === 'right') {
            // 调整右侧面板宽度
            const newRightWidth = Math.max(props.minRightWidth, startRightWidth.value - deltaX);

            // 确保不会导致中间区域小于最小宽度
            const maxRightWidth =
                containerWidth.value - (leftVisibleState.value ? leftWidth.value : 0) - props.minCenterWidth;

            rightWidth.value = Math.min(newRightWidth, maxRightWidth);
            emit('update:rightWidth', rightWidth.value);
        }

        emit('resize', {
            leftWidth: leftWidth.value,
            centerWidth: centerWidth.value,
            rightWidth: rightWidth.value,
            leftVisible: leftVisibleState.value,
            rightVisible: rightVisibleState.value,
        });
    };

    // 停止调整大小
    const stopResize = () => {
        isResizing.value = false;
        currentHandle.value = null;
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', stopResize);
    };

    // 更新容器宽度
    const updateContainerWidth = () => {
        // 获取最近的 three-panel-layout 元素
        const container = document.querySelector('.three-panel-layout');
        if (container) {
            containerWidth.value = container.clientWidth;
        }
    };

    // 监听props变化
    watch(
        () => props.leftVisible,
        (newVal) => {
            leftVisibleState.value = newVal;
            // 等待DOM更新后重新计算容器宽度
            setTimeout(() => {
                updateContainerWidth();
            }, 0);
        }
    );

    watch(
        () => props.rightVisible,
        (newVal) => {
            rightVisibleState.value = newVal;
            // 等待DOM更新后重新计算容器宽度
            setTimeout(() => {
                updateContainerWidth();
            }, 0);
        }
    );

    // 生命周期钩子
    onMounted(() => {
        updateContainerWidth();
        window.addEventListener('resize', updateContainerWidth);
    });

    onBeforeUnmount(() => {
        window.removeEventListener('resize', updateContainerWidth);
    });
</script>

<style scoped>
    .user-select-none {
        user-select: none;
    }

    :deep(.el-container) {
        display: flex;
        flex-direction: row;
    }

    :deep(.el-aside),
    :deep(.el-main) {
        padding: 0;
    }

    :deep(.el-header) {
        height: auto;
        padding: 0;
    }
</style>
