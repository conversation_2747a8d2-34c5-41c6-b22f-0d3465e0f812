<template>
    <div class="flex flex-wrap gap-2">
        <el-tag
            v-bind="$attrs"
            v-for="tag in allTags"
            :key="tag.value"
            class="cursor-pointer transition-all duration-300 mr-2 hover:opacity-80"
            :type="selectedTags.has(tag.value) ? 'primary' : 'info'"
            :effect="selectedTags.has(tag.value) ? 'dark' : 'plain'"
            @click="toggleTag(tag.value)">
            {{ tag.label }}
        </el-tag>
    </div>
</template>

<script lang="ts" setup>
    import { computed, ref, watch } from 'vue';
    import { LabelValue } from '@chances/portal_common_core';

    // 参数
    const props = defineProps<{
        tags: LabelValue[];
        modelValue: string[];
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: string[]): void;
        (e: 'toggle', value: string[]): void;
    }>();

    // 已选中的标签
    const selectedTags = ref<Set<string>>(
        new Set(props.modelValue && props.modelValue.length > 0 ? props.modelValue : ['-1'])
    );

    // 所有标签，包括全部
    const allTags = computed(() => [{ label: '不限', value: '-1' }, ...props.tags]);

    // 标签全部是否被选中
    const isAllSelected = computed(() => selectedTags.value.has('-1'));

    // 点击标签
    const toggleTag = (value: string) => {
        if (value === '-1') {
            // 点击全部，清空其他已选中标签
            selectedTags.value.clear();
            selectedTags.value.add('-1');
        } else {
            // 点击单个标签
            if (isAllSelected.value) {
                // 若此时全部标签已选中，则去掉全部标签，只加入单个标签
                selectedTags.value.clear();
                selectedTags.value.add(value);
            } else {
                // 若此时全部标签未选中
                if (selectedTags.value.has(value)) {
                    // 若点击的标签已被选中，删除对应标签
                    selectedTags.value.delete(value);

                    // 若此时已选中的标签为空，则需要默认选中全部标签
                    if (selectedTags.value.size === 0) {
                        selectedTags.value.add('-1');
                    }
                } else {
                    // 若点击的标签未被选中，增加对应标签
                    selectedTags.value.add(value);
                    if (selectedTags.value.size === props.tags.length) {
                        // 若所有标签都已选中，则清空选中标签，选中全部标签
                        selectedTags.value.clear();
                        selectedTags.value.add('-1');
                    }
                }
            }
        }

        // 更新 modelValue
        emit('update:modelValue', Array.from(selectedTags.value));
        emit('toggle', Array.from(selectedTags.value));
    };

    // 当 modelValue 发生变化，已选中的标签也同步变化
    watch(
        () => props.modelValue,
        (newValue) => {
            selectedTags.value = new Set(newValue);
        }
    );
</script>
