<template>
    <el-button :disabled="disabled" :style="{ color: color }" class="icon-button" @click="onClick">
        <el-icon>
            <component :is="icon" />
        </el-icon>
    </el-button>
</template>

<script setup lang="ts">
    const props = defineProps({
        icon: {
            type: [String, Object],
            required: true,
        },
        color: {
            type: String,
            default: '#409EFF',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    });

    const emit = defineEmits(['click']);

    const onClick = (event: MouseEvent) => {
        emit('click', event);
    };
</script>

<style scoped>
    .icon-button {
        border: none;
        padding: 4px;
        background: transparent;
    }

    .icon-button:hover {
        background-color: #f5f7fa;
    }

    .icon-button:disabled {
        cursor: not-allowed;
        opacity: 0.6;
    }
</style>
