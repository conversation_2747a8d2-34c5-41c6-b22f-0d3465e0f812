import { App } from 'vue';
import ActionButton from './action-button.vue';
import BackgroundEditor from './background-editor.vue';
import BaseCanvas from './base-canvas.vue';
import BaseCheckbox from './base-checkbox.vue';
import BaseDatePicker from './base-date-picker.vue';
import BaseDesigner from './base-designer.vue';
import BaseModal from './base-modal.vue';
import BasePagination from './base-pagination.vue';
import BaseTableWithPagination from './base-table-with-pagination.vue';
import BaseTopBar from './base-topbar.vue';
import BaseTree from './base-tree.vue';
import CardRectEditor from './card-rect-editor.vue';
import CollapseItemWrapper from './collapse-item-wrapper.vue';
import CollapseItemWrapperHeader from './collapse-item-wrapper-header.vue';
import CollapseWrapper from './collapse-wrapper.vue';
import DimensionEditor from './dimension-editor.vue';
import DimensionSelector from './dimension-selector.vue';
import DynamicImage from './dynamic-image.vue';
import FileUpload from './file-upload.vue';
import FloatingPanel from './floating-panel.vue';
import IconButton from './icon-button.vue';
import IconTextButton from './icon-text-button.vue';
import ImageCard from './image-card.vue';
import ImageEditor from './image-editor.vue';
import ImageErrorFallback from './image-error-fallback.vue';
import ImageUploader from './image-uploader.vue';
import Minimap from './minimap.vue';
import Pagination from './pagination.vue';
import RadioInput from './radio-input.vue';
import SearchScroll from './search-scroll.vue';
import SearchScrollPage from './search-scroll-page.vue';
import SelectBastags from './select-bastags.vue';
import SelectPage from './select-page.vue';
import SelectTag from './select-tag.vue';
import SiteSelector from './site-selector.vue';
import SpaceEditor from './space-editor.vue';
import StatusDot from './status-dot.vue';
import TabGroup from './tab-group.vue';
import TagsGroup from './tags-group.vue';
import TagsSelector from './tags-selector.vue';
import TextMarquee from './text-marquee.vue';
import ThreePanelLayout from './three-panel-layout.vue';
import UploadComponentPackage from './upload-component-package.vue';
import StatusColumns from './status-columns.vue';
import PersonalRuleDialog from './personal-rule-dialog.vue';
import JsonView from './json-view.vue';

export const install = (app: App) => {
    app.component('ActionButton', ActionButton);
    app.component('BackgroundEditor', BackgroundEditor);
    app.component('BaseCanvas', BaseCanvas);
    app.component('BaseCheckbox', BaseCheckbox);
    app.component('BaseDatePicker', BaseDatePicker);
    app.component('BaseDesigner', BaseDesigner);
    app.component('BaseModal', BaseModal);
    app.component('BasePagination', BasePagination);
    app.component('BaseTableWithPagination', BaseTableWithPagination);
    app.component('BaseTopBar', BaseTopBar);
    app.component('BaseTree', BaseTree);
    app.component('CardRectEditor', CardRectEditor);
    app.component('CollapseItemWrapper', CollapseItemWrapper);
    app.component('CollapseItemWrapperHeader', CollapseItemWrapperHeader);
    app.component('CollapseWrapper', CollapseWrapper);
    app.component('DimensionEditor', DimensionEditor);
    app.component('DimensionSelector', DimensionSelector);
    app.component('DynamicImage', DynamicImage);
    app.component('FileUpload', FileUpload);
    app.component('FloatingPanel', FloatingPanel);
    app.component('IconButton', IconButton);
    app.component('IconTextButton', IconTextButton);
    app.component('ImageCard', ImageCard);
    app.component('ImageEditor', ImageEditor);
    app.component('ImageErrorFallback', ImageErrorFallback);
    app.component('ImageUploader', ImageUploader);
    app.component('Minimap', Minimap);
    app.component('Pagination', Pagination);
    app.component('RadioInput', RadioInput);
    app.component('SearchScroll', SearchScroll);
    app.component('SearchScrollPage', SearchScrollPage);
    app.component('SelectBastags', SelectBastags);
    app.component('SelectPage', SelectPage);
    app.component('SelectTag', SelectTag);
    app.component('SiteSelector', SiteSelector);
    app.component('SpaceEditor', SpaceEditor);
    app.component('StatusDot', StatusDot);
    app.component('TabGroup', TabGroup);
    app.component('TagsGroup', TagsGroup);
    app.component('TagsSelector', TagsSelector);
    app.component('TextMarquee', TextMarquee);
    app.component('ThreePanelLayout', ThreePanelLayout);
    app.component('UploadComponentPackage', UploadComponentPackage);
    app.component('StatusColumns', StatusColumns);
    app.component('PersonalRuleDialog', PersonalRuleDialog);
    app.component('JsonView', JsonView);
};

export default {
    install,
};
