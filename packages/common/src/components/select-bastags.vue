<template>
    <el-dialog
        title="选择基础标签"
        append-to-body
        v-model="dialogVisible"
        width="700px"
        @open="initTableDate()"
        @close="onCloseDialog">
        <div style="margin-bottom: 10px">
            <el-input placeholder="请输入名称或编码" clearable @keyup.enter="searchTag" v-model="keyword">
                <template #append>
                    <el-button :icon="Search" @click="searchTag" />
                </template>
            </el-input>
        </div>
        <el-scrollbar style="height: 450px; transition: 0.2s">
            <el-table stripe :data="tableData" style="width: 100%">
                <el-table-column width="130" label="全选">
                    <template #default="scope">
                        <el-checkbox
                            :indeterminate="scope.row.isIndeterminate"
                            v-model="scope.row.checkAll"
                            @change="handleCheckAllChange($event, scope.row)"
                            >{{ scope.row.name }}
                        </el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column label="">
                    <template #default="scope">
                        <el-checkbox-group v-model="checkedTags" @change="handleCheckedChange($event, scope.row)">
                            <el-checkbox
                                :value="item.code"
                                :style="index == 0 ? 'margin-left:30px' : ''"
                                v-for="(item, index) in scope.row.tags"
                                :key="item.code"
                                >{{ item.name }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </template>
                </el-table-column>
            </el-table>
        </el-scrollbar>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="chooseAll(false)">取消</el-button>
                <el-button type="primary" @click="onClickConfirm"> 确定 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    // @ts-ignore
    import pinyin from 'js-pinyin';
    import { Search } from '@element-plus/icons-vue';
    import { MaterialTagVO } from '@smartdesk/common/types';

    // 参数
    const props = defineProps<{
        visible: boolean;
        bastTagsList: MaterialTagVO[];
    }>();
    const emit = defineEmits(['update:visible', 'on-click-confirm']);
    // 是否对话框
    const dialogVisible = ref(props.visible);
    // 关键字 用于标签查询
    const keyword = ref<string>('');

    // 关闭对话框
    const onCloseDialog = () => {
        tableData.value = [];
        tableDataStorage.value = [];
        emit('update:visible', false);
    };
    // 关键字查询标签
    const searchTag = () => {
        const tmpTableData = JSON.parse(JSON.stringify(tableDataStorage.value));
        const resultTable = tmpTableData.filter((item: any) => {
            item.tags = item.tags.filter(
                (tagItem: any) =>
                    tagItem.code.toLowerCase().includes(keyword.value.toLowerCase()) ||
                    tagItem.name.toLowerCase().includes(keyword.value.toLowerCase())
            );
            return item.tags.length;
        });
        resultTable.forEach((item: any) => {
            setItemCheck(item);
        });
        tableData.value = resultTable;
    };
    // 选中的标签
    const checkedTags = ref<string[]>([]);
    // 标签表格数据
    const tableData = ref<{ tags: any[] }[]>([]);
    const tableDataStorage = ref<{ tags: any[] }[]>([]);
    // 设置选中的标签
    const setItemCheck = (data: any) => {
        let tagsCodes = data.tags.map((tag: any) => tag.code);
        let some = tagsCodes.some((tag: any) => checkedTags.value.includes(tag));
        let all = tagsCodes.every((tag: any) => checkedTags.value.includes(tag));
        data.checkAll = false;
        data.isIndeterminate = false;
        if (all) {
            data.checkAll = true;
        } else if (some) {
            data.isIndeterminate = true;
        }
    };
    // 初始化数据
    const initTableDate = () => {
        tableData.value = [];
        tableDataStorage.value = [];
        let letters = [
            'A',
            'B',
            'C',
            'D',
            'E',
            'F',
            'G',
            'H',
            'I',
            'J',
            'K',
            'L',
            'M',
            'N',
            'O',
            'P',
            'Q',
            'R',
            'S',
            'T',
            'U',
            'V',
            'W',
            'X',
            'Y',
            'Z',
            '其他',
        ];
        const map = new Map<string, { tags: any[] }>();
        map.set('其他', { tags: [] });
        props.bastTagsList.forEach((item) => {
            let t = pinyin.getFullChars(item.name);
            t = t.length ? t[0] : '#';

            // 确保该 key 存在于 map，否则先初始化
            if (!map.has(t)) {
                map.set(t, { tags: [] });
            }

            // 安全地 push 进去
            map.get(t)?.tags.push(item);
        });

        for (let value of map.values()) {
            if (value.tags.length) {
                tableData.value.push(value);
            }
        }
        tableDataStorage.value = JSON.parse(JSON.stringify(tableData.value));
    };
    const handleCheckAllChange = (val: any, row: any) => {
        row.isIndeterminate = false;
        row.checkAll = val;
        row.tags.forEach((tag: any) => {
            if (val) {
                if (!checkedTags.value.includes(tag.code)) {
                    checkedTags.value.push(tag.code);
                }
            } else {
                const index = checkedTags.value.indexOf(tag.code);
                if (index != -1) {
                    checkedTags.value.splice(index, 1);
                }
            }
        });
    };
    const handleCheckedChange = (val: any, row: any) => {
        let all = row.tags.every((tag: any) => val.includes(tag.code));
        let some = row.tags.some((tag: any) => val.includes(tag.code));
        row.checkAll = false;
        row.isIndeterminate = false;
        if (all) {
            row.checkAll = true;
        } else if (some) {
            row.isIndeterminate = true;
        }
    };
    // 取消
    const chooseAll = (val: boolean) => {
        tableData.value.forEach((item) => {
            handleCheckAllChange(val, item);
        });
        onCloseDialog();
    };
    // 确定按钮
    const onClickConfirm = () => {
        emit('on-click-confirm', checkedTags.value);
        onCloseDialog();
    };
    watch(
        () => props.visible,
        (val) => {
            dialogVisible.value = val;
        }
    );
</script>

<style scoped></style>
