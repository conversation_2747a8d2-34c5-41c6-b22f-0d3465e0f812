<template>
    <div class="w-full h-full flex flex-col items-center justify-center bg-gray-50 text-gray-400">
        <svg width="40" height="40" viewBox="0 0 24 24" fill="currentColor" class="mb-2">
            <path
                d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z" />
            <path d="M2 2l20 20" stroke="currentColor" stroke-width="2" />
        </svg>
        <span class="text-sm">{{ text }}</span>
    </div>
</template>

<script lang="ts" setup>
    // 参数
    defineProps<{
        text?: string;
    }>();
</script>
