<script setup lang="ts" name="SearchScroll">
    import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
    import { RestPageResultResponse } from '@chances/portal_common_core';

    const props = defineProps<{
        // 获取数据
        fetchData: () => Promise<RestPageResultResponse<any>>;
        // 搜索框提示
        placeholder?: string;
        // 刷新标识
        refreshKey?: string;
    }>();

    // 搜索框
    const searchQuery = ref<string>('');
    // 数据列表
    const dataList = ref<any[]>([]);
    // 加载中
    const loading = ref<boolean>(false);

    // 滚动相关
    const loadingTrigger = ref<HTMLDivElement | null>(null);
    const scrollContainer = ref<HTMLDivElement | null>(null);
    let observer: IntersectionObserver | null = null;

    /**
     * 获取列表
     */
    const fetchList = async () => {
        if (loading.value) return;
        loading.value = true;
        try {
            const res: RestPageResultResponse<any> = await props.fetchData();
            dataList.value = res.result;
        } catch (error) {
            console.error('获取列表失败:', error);
        } finally {
            loading.value = false;
        }
    };

    /**
     * 过滤列表
     */
    const filteredDataList = computed(() => {
        if (!searchQuery.value) return dataList.value;
        return dataList.value.filter((item) => item.name.toLowerCase().includes(searchQuery.value.toLowerCase()));
    });

    /**
     * 设置滚动监听
     */
    const setupIntersectionObserver = () => {
        observer = new IntersectionObserver(() => {}, {
            root: scrollContainer.value,
            threshold: 0.1,
            rootMargin: '100px',
        });
        if (loadingTrigger.value) {
            observer.observe(loadingTrigger.value);
        }
    };

    /**
     * 重新获取列表
     */
    const reFetch = () => {
        // 重新设置滚动监听
        if (observer) {
            observer.disconnect();
        }
        setupIntersectionObserver();
    };

    // 更新搜索框
    watch(searchQuery, () => {
        reFetch();
    });

    // 手动刷新
    watch(
        () => props.refreshKey,
        () => {
            searchQuery.value = '';
            reFetch();
        }
    );

    onMounted(async () => {
        await fetchList();
        setupIntersectionObserver();
    });

    onUnmounted(() => {
        // 清理滚动监听
        if (observer) {
            observer.disconnect();
        }
        searchQuery.value = '';
    });
</script>

<template>
    <div class="h-full flex flex-col min-h-0">
        <div class="flex-shrink-0 mb-4">
            <el-input v-model="searchQuery" :placeholder="placeholder" class="mb-2" clearable>
                <template #prefix>
                    <i-mdi-search class="w-4 h-4" />
                </template>
            </el-input>
        </div>
        <div class="flex-1 min-h-0 overflow-y-auto relative" ref="scrollContainer">
            <slot :list="filteredDataList" />
            <div v-if="loading" class="text-center py-5 text-gray-400">加载中...</div>
            <div ref="loadingTrigger" class="h-5 my-2.5"></div>
        </div>
    </div>
</template>

<style scoped>
    .overflow-y-auto::-webkit-scrollbar {
        display: none;
    }

    .overflow-y-auto {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
</style>
