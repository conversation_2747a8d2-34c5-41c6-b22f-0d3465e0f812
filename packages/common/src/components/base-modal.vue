<template>
    <el-dialog
        v-model="dialogVisible"
        v-bind="$attrs"
        :title="title"
        :width="width"
        :destroy-on-close="destroyOnClose"
        @open="onOpen"
        @opened="onOpened"
        @close="onClose"
        @closed="onClosed">
        <slot></slot>

        <template #footer>
            <slot name="footer">
                <div class="flex justify-end gap-2 pt-4">
                    <el-button @click="handleCancel" class="!px-5">{{ cancelText }}</el-button>
                    <el-button type="primary" @click="handleConfirm" class="!px-5">
                        {{ confirmText }}
                    </el-button>
                </div>
            </slot>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
    import { ref, watch } from 'vue';

    defineOptions({
        name: 'BaseModal',
    });

    // 定义组件接收的props
    const props = defineProps({
        // 对话框标题
        title: {
            type: String,
            default: '提示',
        },
        // 对话框宽度
        width: {
            type: String,
            default: '50%',
        },
        // 关闭时销毁组件内元素
        destroyOnClose: {
            type: Boolean,
            default: false,
        },
        // 取消按钮文本
        cancelText: {
            type: String,
            default: '取消',
        },
        // 确认按钮文本
        confirmText: {
            type: String,
            default: '确定',
        },
        // 对话框显示状态
        modelValue: {
            type: Boolean,
            default: false,
        },
    });

    // 定义组件事件
    const emit = defineEmits(['update:modelValue', 'open', 'opened', 'close', 'closed', 'cancel', 'confirm']);

    // 对话框可见状态
    const dialogVisible = ref(props.modelValue);

    // 监听modelValue的变化
    watch(
        () => props.modelValue,
        (newVal) => {
            dialogVisible.value = newVal;
        }
    );

    // 监听dialogVisible的变化
    watch(
        () => dialogVisible.value,
        (newVal) => {
            emit('update:modelValue', newVal);
        }
    );

    // 事件处理方法
    const onOpen = () => {
        emit('open');
    };

    const onOpened = () => {
        emit('opened');
    };

    const onClose = () => {
        emit('close');
    };

    const onClosed = () => {
        emit('closed');
    };

    const handleCancel = () => {
        dialogVisible.value = false;
        emit('cancel');
    };

    const handleConfirm = () => {
        emit('confirm');
    };
</script>
