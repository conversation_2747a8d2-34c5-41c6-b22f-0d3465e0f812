<template>
    <el-dialog
        :model-value="modelValue"
        :width="dialogWidth"
        :before-close="handleClose"
        append-to-body
        :z-index="zIndex">
        <template #header>
            <span class="upload-title">{{ title }}</span>
            <span v-if="subtitle" class="upload-subtitle">{{ subtitle }}</span>
        </template>

        <el-upload
            class="upload-demo"
            drag
            :auto-upload="autoUpload"
            :http-request="handleUpload"
            :show-file-list="showFileList"
            :accept="accept"
            :multiple="multiple"
            :before-upload="handleBeforeUpload"
            :disabled="uploading"
            :limit="1"
            ref="uploadRef">
            <el-icon class="el-icon--upload">
                <upload-filled />
            </el-icon>
            <div class="el-upload__text">
                {{ uploadText || '拖拽文件到此处或点击上传' }}
            </div>
            <template #tip>
                <div class="el-upload__tip">
                    {{ tipText }}
                </div>
            </template>
        </el-upload>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="onCancel" :disabled="uploading">
                    {{ cancelText }}
                </el-button>
                <el-button type="primary" @click="onConfirm" :loading="uploading" :disabled="uploading">
                    {{ confirmText }}
                </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { computed, ref } from 'vue';
    import type { UploadInstance, UploadProps, UploadRequestOptions } from 'element-plus';
    import { UploadFilled } from '@element-plus/icons-vue';

    // Props 定义
    interface Props {
        modelValue?: boolean;
        title?: string;
        subtitle?: string;
        dialogWidth?: string | number;
        zIndex?: number;
        accept?: string;
        multiple?: boolean;
        autoUpload?: boolean;
        showFileList?: boolean;
        maxSize?: number;
        uploadText?: string;
        tipText?: string;
        cancelText?: string;
        confirmText?: string;
        uploadFunction?: (file: File) => Promise<any>;
        beforeUpload?: (file: File) => boolean | Promise<boolean>;
        validateFile?: (file: File) => { valid: boolean; message?: string };
    }

    const props = withDefaults(defineProps<Props>(), {
        modelValue: false,
        title: '文件上传',
        dialogWidth: '500px',
        zIndex: 2000,
        accept: '*',
        multiple: false,
        autoUpload: false,
        showFileList: true,
        maxSize: 100,
        cancelText: '取消',
        confirmText: '确定',
    });

    // Emits 定义
    const emit = defineEmits([
        'update:modelValue',
        'upload-success',
        'upload-error',
        'before-close',
        'cancel',
        'confirm',
    ]);

    // 响应式数据
    const uploadRef = ref<UploadInstance>();
    const uploading = ref(false);

    // 计算属性
    const tipText = computed(() => {
        if (props.tipText) return props.tipText;

        const sizeText = `文件大小不能超过${props.maxSize}MB`;
        const acceptText = props.accept !== '*' ? `支持格式：${props.accept}` : '';

        return [acceptText, sizeText].filter(Boolean).join('，');
    });

    // 文件上传前验证
    const handleBeforeUpload: UploadProps['beforeUpload'] = async (file) => {
        // 文件大小验证
        const maxSizeBytes = props.maxSize * 1024 * 1024;
        if (file.size > maxSizeBytes) {
            emit('upload-error', {
                message: `文件大小不能超过${props.maxSize}MB`,
            });
            return false;
        }

        // 自定义验证
        if (props.validateFile) {
            const validation = props.validateFile(file);
            if (!validation.valid) {
                emit('upload-error', {
                    message: validation.message || '文件验证失败',
                });
                return false;
            }
        }

        // 自定义 beforeUpload
        if (props.beforeUpload) {
            const result = await props.beforeUpload(file);
            if (!result) return false;
        }

        return true;
    };

    // 自定义上传处理
    const handleUpload = async (options: UploadRequestOptions) => {
        if (!props.uploadFunction) {
            emit('upload-error', { message: '未提供上传函数' });
            return;
        }

        uploading.value = true;

        const response = await props.uploadFunction(options.file);
        if (response.code === 200) {
            emit('upload-success', response);
            handleUploadSuccess();
        } else {
            emit('upload-error', response.msg);
            handleUploadError();
        }

        uploading.value = false;
    };

    // 关闭对话框
    const handleClose = () => {
        if (uploading.value) return false;

        clearFiles();
        emit('before-close');
        emit('update:modelValue', false);
    };

    // 清空文件列表
    const clearFiles = () => {
        if (uploadRef.value) {
            uploadRef.value.clearFiles();
        }
    };

    // 取消按钮
    const onCancel = () => {
        emit('cancel');
        handleClose();
    };

    // 确定按钮
    const onConfirm = () => {
        emit('confirm');
        if (uploadRef.value && !uploading.value) {
            uploadRef.value.submit();
        }
    };

    // 上传成功处理
    const handleUploadSuccess = () => {
        clearFiles();
        handleClose();
    };

    // 上传失败处理
    const handleUploadError = () => {
        clearFiles();
    };

    // 暴露方法
    defineExpose({
        clearFiles,
    });
</script>

<style scoped>
    .upload-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--el-text-color-primary);
    }

    .upload-subtitle {
        margin-left: 8px;
        font-size: 14px;
        color: var(--el-text-color-regular);
    }

    .dialog-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
    }

    .upload-demo {
        width: 100%;
    }
</style>
