<template>
    <div class="relative w-full">
        <el-popover placement="bottom-start" :width="width" trigger="click" v-model:visible="visible">
            <template #reference>
                <el-input v-model="selectedLabel" readonly :placeholder="placeholder" clearable style="width: 100%">
                    <template #suffix>
                        <el-icon>
                            <ArrowDown />
                        </el-icon>
                    </template>
                </el-input>
            </template>

            <el-input v-if="filterable" v-model="keyword" placeholder="搜索..." clearable class="mb-2">
                <template #prefix>
                    <el-icon>
                        <Search />
                    </el-icon>
                </template>
            </el-input>

            <div class="max-h-64 overflow-y-auto">
                <div
                    class="px-3 py-2 border-b border-gray-200 cursor-pointer hover:bg-gray-50 text-red-600 text-sm"
                    @click="clearAndClose">
                    <el-icon class="mr-1">
                        <Close />
                    </el-icon>
                    清空选择
                </div>
                <el-tree
                    ref="treeRef"
                    :data="data"
                    :props="{
                        ...treeProps,
                        disabled: () => !(data as any).hasPermission,
                    }"
                    :filter-node-method="filterNode"
                    @node-click="selectNode"
                    node-key="id"
                    :current-node-key="currentNodeKey"
                    highlight-current
                    v-bind="$attrs">
                    <template #default="{ node, data }">
                        <span
                            :class="{
                                'text-gray-400': !data.hasPermission,
                                'cursor-not-allowed': !data.hasPermission,
                            }">
                            {{ node.label }}
                            <el-icon v-if="!data.hasPermission" class="ml-1"><Lock /></el-icon>
                        </span>
                    </template>
                </el-tree>
            </div>
        </el-popover>
    </div>
</template>

<script lang="ts" setup>
    import { computed, nextTick, onMounted, ref, watch } from 'vue';
    import { useOperatorStore } from '@chances/portal_common_core';
    import { ArrowDown, Close, Lock, Search } from '@element-plus/icons-vue';

    // 基础配置
    const props = defineProps({
        modelValue: { type: [Number, String, null], required: false },
        data: { type: Array, required: true },
        width: { type: [Number, String], default: 300 },
        placeholder: { type: String, default: '请选择' },
        filterable: { type: Boolean, default: true },
        labelKey: { type: String, default: 'label' },
        valueKey: { type: String, default: 'value' },
        childrenKey: { type: String, default: 'children' },
        nodeKey: { type: String, default: 'id' },
    });

    const emit = defineEmits(['update:modelValue', 'change']);

    // pinia store
    const operatorStore = useOperatorStore();

    // 响应式状态
    const visible = ref(false);
    const keyword = ref('');
    const selectedLabel = ref('');
    const treeRef = ref(null);
    const currentNodeKey = ref(null);

    // 树配置
    const treeProps = computed(() => ({
        children: props.childrenKey,
        label: props.labelKey,
    }));

    // 递归查找节点
    const findNodeByValue = (nodes: any, value: any) => {
        if (!nodes || nodes.length === 0 || value === null || value === undefined) return null;

        for (const node of nodes) {
            if (node[props.valueKey] === value) {
                return node;
            }
            if (node[props.childrenKey] && node[props.childrenKey].length > 0) {
                const found: any = findNodeByValue(node[props.childrenKey], value);
                if (found) return found;
            }
        }
        return null;
    };

    // 动态设置权限字段
    const markPermissions = (nodes: any, userPermissions: any) => {
        if (!nodes || nodes.length === 0) return;
        for (const node of nodes) {
            node.hasPermission = userPermissions.includes(Number(node.id));
            if (node[props.childrenKey]) {
                markPermissions(node[props.childrenKey], userPermissions);
            }
        }
    };

    // 初始化选中值
    const initSelectedValue = () => {
        const userPermissions = operatorStore?.loginModel?.dimensionMap?.org?.map(Number) || [];
        markPermissions(props.data, userPermissions);
        if (props.modelValue !== null && props.modelValue !== undefined) {
            const node = findNodeByValue(props.data, props.modelValue);
            if (node) {
                selectedLabel.value = node[props.labelKey];
                currentNodeKey.value = node[props.nodeKey];

                // 在打开下拉框时，确保正确设置当前节点
                nextTick(() => {
                    if (treeRef.value && visible.value) {
                        // 使用 setCurrentKey 设置当前节点
                        (treeRef.value as any).setCurrentKey(node[props.nodeKey]);
                    }
                });
            }
        } else {
            selectedLabel.value = '';
            currentNodeKey.value = null;
        }
    };

    // 选择节点
    const selectNode = (data: any) => {
        if (!data.hasPermission) return false;

        selectedLabel.value = data[props.labelKey];
        currentNodeKey.value = data[props.nodeKey];
        emit('update:modelValue', data[props.valueKey]);
        emit('change', data);
        visible.value = false;
        return true;
    };

    // 过滤节点
    const filterNode = (value: any, data: any) => {
        if (!value) return true;
        return data[props.labelKey].toLowerCase().includes(value.toLowerCase());
    };

    // 清空选择
    const clearSelection = () => {
        selectedLabel.value = '';
        currentNodeKey.value = null;
        if (treeRef.value) {
            (treeRef.value as any).setCurrentKey(null);
        }
        emit('update:modelValue', null);
        emit('change', null);
    };

    // 清空并关闭弹窗
    const clearAndClose = () => {
        clearSelection();
        visible.value = false;
    };

    // 监控关键词的变化
    watch(
        () => keyword.value,
        (val) => {
            if (treeRef.value) {
                (treeRef.value as any).filter(val);
            }
        }
    );

    // 监控modelValue的变化
    watch(
        () => props.modelValue,
        () => {
            initSelectedValue();
        },
        { immediate: true }
    );

    // 监控data的变化，当数据更新时重新初始化选中值
    watch(
        () => props.data,
        () => {
            nextTick(() => {
                const userPermissions = operatorStore?.loginModel?.dimensionMap?.org?.map(Number) || [];
                markPermissions(props.data, userPermissions); // 动态设置权限
                initSelectedValue();
            });
        },
        { deep: true }
    );

    // 监控visible的变化，当打开弹出框时设置当前选中节点
    watch(
        () => visible.value,
        (isVisible) => {
            if (isVisible && treeRef.value && currentNodeKey.value) {
                nextTick(() => {
                    (treeRef.value as any).setCurrentKey(currentNodeKey.value);
                });
            }
        }
    );

    // 组件挂载时初始化
    onMounted(() => {
        initSelectedValue();
    });

    // 暴露方法
    defineExpose({
        clear: clearSelection,
        findNodeByValue,
        setCurrentNode: (nodeKey: any) => {
            if (treeRef.value) {
                (treeRef.value as any).setCurrentKey(nodeKey);
            }
        },
    });
</script>
