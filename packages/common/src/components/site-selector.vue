<template>
    <el-dropdown trigger="click" @visible-change="dropdownVisibleChange">
        <span class="cursor-pointer text-2xl">
            <el-tooltip class="box-item" content="点击切换网站" placement="right">
                {{ siteStore.currentSite?.name ?? '未选择网站' }}
            </el-tooltip>
            <el-icon class="ml-2">
                <ArrowUp v-if="isDropdownVisible" />
                <ArrowDown v-else />
            </el-icon>
        </span>
        <template #dropdown>
            <el-dropdown-menu>
                <el-dropdown-item v-for="site in siteStore.siteList" :key="site.code" @click="onClickSwitchSite(site)">
                    {{ site.name }}
                </el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
</template>

<script setup lang="ts">
    import { useSiteStore } from '@smartdesk/common/stores';
    import { Site } from '@smartdesk/common/types';
    import { ArrowDown, ArrowUp } from '@element-plus/icons-vue';
    import { ref } from 'vue';

    const siteStore = useSiteStore();

    const isDropdownVisible = ref<boolean>(false);

    // 下拉菜单可见性变化
    const dropdownVisibleChange = (visible: boolean) => {
        isDropdownVisible.value = visible;
    };

    // 点击切换网站
    const onClickSwitchSite = (site: Site) => {
        siteStore.switchSite(site.code);
    };
</script>
