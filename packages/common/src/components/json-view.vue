<template>
    <div>
        <vue-json-pretty
            :data="jsonData"
            :virtual="true"
            :showLineNumber="true"
            :showLength="true"
            :showIcon="true"
            :showSelectController="true" />
    </div>
</template>

<script setup lang="ts">
    import VueJsonPretty from 'vue-json-pretty';
    import 'vue-json-pretty/lib/styles.css';

    const props = defineProps<{
        jsonData: Record<string, any>;
    }>();
</script>
