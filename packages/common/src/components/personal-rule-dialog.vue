<template>
    <el-dialog v-model="props.dialogVisible" title="选择推荐策略" width="60%" @close="handleClose">
        <el-form label-width="120px" :model="searchForm" :label-suffix="':'" :size="'default'">
            <el-row :gutter="24">
                <el-col :span="12">
                    <el-form-item label="推荐策略目录">
                        <el-cascader
                            v-model="searchForm.folderCode"
                            :options="personalRuleFolderOptions"
                            :props="{
                                label: 'name',
                                value: 'code',
                                checkStrictly: true,
                                emitPath: false,
                            }"
                            clearable
                            placeholder="请选择目录" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="关键字">
                        <el-input placeholder="请输入关键字" clearable v-model="searchForm.keyword" />
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form>
        <base-table-with-pagination
            :total="totalElements"
            :current-page="currentPage"
            :page-size="pageSize"
            @update:currentPage="handleCurrentPageChange"
            @update:pageSize="handleSizeChange">
            <template #table>
                <el-table
                    v-loading="loading"
                    :data="ruleList"
                    :style="{ width: '100%', height: '500px' }"
                    :row-key="(row: any) => row.code"
                    highlight-current-row
                    border
                    @current-change="handleCurrentChange"
                    :current-row-key="currentRowCode">
                    <el-table-column label="选择" width="55">
                        <template #default="scope">
                            <el-radio
                                class="hidden-label"
                                v-model="currentRowCode"
                                :label="scope.row.code"
                                @change="handleRowSelect(scope.row)">
                            </el-radio>
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="策略名称" show-overflow-tooltip />
                    <el-table-column prop="status" label="状态" width="100">
                        <template #default="{ row }">
                            <el-tag :type="row.status === 1 ? 'success' : 'danger'">
                                {{ enumStore.getLabelByKeyAndValue('enableStatus', row.status) }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="citationCount" label="引用次数" width="100" align="center">
                        <template #default="{ row }">
                            <el-tag :type="row.citationCount > 0 ? 'success' : 'danger'">
                                {{ row.citationCount }}
                            </el-tag>
                        </template>
                    </el-table-column>
                    <el-table-column prop="createdBy" label="创建人" width="100" />
                </el-table>
            </template>
        </base-table-with-pagination>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="onConfirm"> 确定 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { personalRuleApi, personalRuleFolderApi } from '@smartdesk/common/api';
    import { PersonalRule, PersonalRuleFolderModel } from '@smartdesk/common/types';
    import { useEnumStore } from '@chances/portal_common_core';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';
    import { useFeedback } from '@smartdesk/common/composables';

    // 推荐策略弹框
    defineOptions({
        name: 'PersonalRuleDialog',
    });

    // 参数
    const props = defineProps<{
        // 当前选中内容
        modelValue: string;
        dialogVisible: boolean;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: Object): void;
        (e: 'update:dialogVisible', value: boolean): void;
        (e: 'save', vale: Object): void;
    }>();

    // pinia store
    const enumStore = useEnumStore();
    const feedback = useFeedback();

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);

    // 当前选中行
    const currentRow = ref<PersonalRule>({} as PersonalRule);
    const currentRowCode = ref<string>(props.modelValue || '');

    // 加载
    const loading = ref(false);
    const searchForm = ref<any>({});

    // 推荐策略列表
    const ruleList = ref<PersonalRule[]>([] as PersonalRule[]);

    // 推荐策略目录选项
    const personalRuleFolderOptions = ref<PersonalRuleFolderModel[]>([] as PersonalRuleFolderModel[]);

    // 处理页码变化
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1;
        getPersonalRuleList();
    };

    // 处理页码变化
    const handleCurrentPageChange = (val: number) => {
        currentPage.value = val;
        getPersonalRuleList();
    };

    // 处理当前页变化
    const handleCurrentChange = (row: PersonalRule) => {
        currentRowCode.value = row ? row.code : '';
        currentRow.value = row;
        emit('update:modelValue', row);
    };

    // 行选中事件
    const handleRowSelect = (row: PersonalRule) => {
        currentRow.value = row;
        currentRowCode.value = row.code;
    };

    // 获取推荐策略列表
    const getPersonalRuleList = async () => {
        loading.value = true;
        const res = await personalRuleApi.getPersonalRules(searchForm.value, {
            page: currentPage.value - 1,
            size: pageSize.value,
            sort: 'id,desc',
        });
        if (res.code === 200) {
            ruleList.value = res.result;
            totalElements.value = Number(res.page.totalElements);
        }
        loading.value = false;
    };

    // 获取目录树
    const getPersonalRuleFolderTree = async () => {
        const res = await personalRuleFolderApi.getPersonalRuleFolderTree(false, '', '');
        personalRuleFolderOptions.value = res.result || [];
    };

    // 关闭对话框
    const handleClose = () => {
        currentRow.value = {} as PersonalRule;
        currentRowCode.value = '';
        emit('update:dialogVisible', false);
    };

    // 确定按钮
    const onConfirm = () => {
        if (!currentRow.value?.code) {
            feedback.warning('请选择一条记录');
            return;
        }
        emit('update:modelValue', currentRowCode.value);
        emit('save', currentRowCode.value);
        emit('update:dialogVisible', false);
    };

    // 监听查询表单
    watch(
        () => searchForm.value,
        () => {
            getPersonalRuleList();
        },
        { deep: true }
    );

    onMounted(() => {
        getPersonalRuleFolderTree();
        getPersonalRuleList();
    });
</script>

<style scoped>
    :deep(.hidden-label) .el-radio__label {
        display: none !important;
    }
</style>
