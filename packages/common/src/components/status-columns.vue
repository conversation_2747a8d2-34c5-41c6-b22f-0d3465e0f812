<template>
    <div>
        <template v-for="(item, index) in statusItems" :key="item.key">
            {{ getLabel(item.key, item.value) }}
            <span v-if="index < statusItems.length - 1"> | </span>
        </template>
    </div>
</template>

<script setup lang="ts">
    import { computed } from 'vue';
    import { useEnumStore } from '@chances/portal_common_core';
    import { PublishStatus } from '@smartdesk/common/types';

    const enumStore = useEnumStore();

    // Props
    const props = withDefaults(
        defineProps<{
            publishStatus: PublishStatus;
            showStatus?: boolean;
            showDelFlag?: boolean;
            showVisibleStatus?: boolean;
            showOnlineStatus?: boolean;
            showAuditStatus?: boolean;
        }>(),
        {
            showStatus: false,
            showDelFlag: true,
            showVisibleStatus: true,
            showOnlineStatus: true,
            showAuditStatus: true,
        }
    );

    // 映射状态项 key 类型（更严谨）
    type StatusKey = 'enableStatus' | 'delFlag' | 'visibleStatus' | 'onlineStatus' | 'auditStatus';

    // 状态项生成逻辑
    const statusItems = computed(() => {
        const list: { key: StatusKey; value: any }[] = [];

        if (props.showStatus) list.push({ key: 'enableStatus', value: props.publishStatus.status });
        if (props.showDelFlag) list.push({ key: 'delFlag', value: props.publishStatus.delFlag });
        if (props.showVisibleStatus) list.push({ key: 'visibleStatus', value: props.publishStatus.visibleStatus });
        if (props.showOnlineStatus) list.push({ key: 'onlineStatus', value: props.publishStatus.onlineStatus });
        if (props.showAuditStatus) list.push({ key: 'auditStatus', value: props.publishStatus.auditStatus });

        return list;
    });

    // 获取 label
    const getLabel = (key: string, value: any): string => enumStore.getLabelByKeyAndValue(key, value);
</script>
