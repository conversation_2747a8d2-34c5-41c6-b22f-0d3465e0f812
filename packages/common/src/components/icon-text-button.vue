<template>
    <el-button
        :type="type"
        :disabled="disabled"
        class="icon-text-button"
        :style="buttonStyle"
        @click="onClick"
        v-bind="$attrs">
        <el-icon class="icon">
            <component :is="icon" />
        </el-icon>
        <span class="text">{{ text }}</span>
    </el-button>
</template>

<script setup lang="ts">
    import { computed } from 'vue';

    const props = defineProps({
        icon: {
            type: [String, Object],
            required: true,
        },
        text: {
            type: String,
            required: true,
        },
        color: {
            type: String,
            default: '#409EFF',
        },
        type: {
            type: String,
            default: 'default',
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    });

    const emit = defineEmits(['click']);

    const buttonStyle = computed(() => {
        if (props.type === 'default') {
            return {
                backgroundColor: props.color,
                borderColor: props.color,
            };
        }
        return {};
    });

    const onClick = (event: MouseEvent) => {
        emit('click', event);
    };
</script>

<style scoped>
    .icon-text-button {
        display: inline-flex;
        align-items: center;
        padding: 8px 16px;
        border-radius: 4px;
        transition: all 0.3s;
        color: white !important; /* 确保文字始终为白色 */
    }

    .icon-text-button:hover {
        opacity: 0.8;
    }

    .icon-text-button:disabled {
        cursor: not-allowed;
        opacity: 0.6;
    }

    .icon {
        margin-right: 4px;
        font-size: 16px;
    }

    .text {
        font-size: 14px;
    }

    /* 预设类型样式 */
    .icon-text-button[type='primary'] {
        background-color: var(--el-color-primary);
        border-color: var(--el-color-primary);
    }

    .icon-text-button[type='success'] {
        background-color: var(--el-color-success);
        border-color: var(--el-color-success);
    }

    .icon-text-button[type='warning'] {
        background-color: var(--el-color-warning);
        border-color: var(--el-color-warning);
    }

    .icon-text-button[type='danger'] {
        background-color: var(--el-color-danger);
        border-color: var(--el-color-danger);
    }
</style>
