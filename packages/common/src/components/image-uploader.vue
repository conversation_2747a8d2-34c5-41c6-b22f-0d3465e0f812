<template>
    <el-upload
        v-if="!imageUrl"
        list-type="picture-card"
        :show-file-list="false"
        :http-request="handleUpload"
        :on-success="handleUploadSuccess">
        <el-icon>
            <Plus />
        </el-icon>
    </el-upload>

    <div v-if="imageUrl" class="relative group">
        <el-image class="w-[200px]" :src="imageUrl">
            <template #error>
                <image-error-fallback text="图片损坏" />
            </template>
        </el-image>

        <div
            class="absolute inset-0 bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-8">
            <el-icon @click.stop="handlePreview" color="blue" size="20">
                <ZoomIn />
            </el-icon>
            <el-icon @click.stop="handleRemove" color="red" size="20">
                <Delete />
            </el-icon>
        </div>
    </div>

    <el-dialog v-model="dialogVisible" width="50%" title="图片预览">
        <el-image class="w-full" :src="dialogImageUrl">
            <template #error>
                <image-error-fallback text="图片损坏" />
            </template>
        </el-image>
    </el-dialog>
</template>

<script lang="ts" setup>
    import { ref, watch } from 'vue';
    import { Delete, Plus, ZoomIn } from '@element-plus/icons-vue';
    import type { UploadRequestOptions } from 'element-plus';
    import { storageApi } from '@smartdesk/common/api';
    import { RestResultResponse } from '@chances/portal_common_core';
    import ImageErrorFallback from './image-error-fallback.vue';

    // 参数
    const props = defineProps<{
        modelValue: string | undefined;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: string): void;
        (e: 'change', value: string): void;
    }>();

    // 预览图地址
    const dialogImageUrl = ref<string>('');

    // 预览图显隐
    const dialogVisible = ref<boolean>(false);

    // 图片地址
    const imageUrl = ref<string>(props.modelValue ?? '');

    // 移除图片
    const handleRemove = () => {
        imageUrl.value = '';
        emit('update:modelValue', '');
        emit('change', '');
    };

    // 预览图片
    const handlePreview = () => {
        dialogImageUrl.value = imageUrl.value!;
        dialogVisible.value = true;
    };

    // 处理上传图片
    const handleUpload = (options: UploadRequestOptions) => {
        return storageApi.uploadFile(options.file);
    };

    // 上传成功
    const handleUploadSuccess = (response: RestResultResponse<string>) => {
        imageUrl.value = response.result;
        emit('update:modelValue', imageUrl.value!);
        emit('change', imageUrl.value!);
    };

    // 监听参数
    watch(
        () => props.modelValue,
        (newValue) => {
            if (newValue) {
                imageUrl.value = newValue;
            } else {
                imageUrl.value = '';
            }
        }
    );
</script>
