<template>
    <image-editor :disabled="disabled" v-model="imageUrl" @change="handleChange" />
</template>

<script lang="ts" setup>
    import { PropType, ref, watch } from 'vue';
    import ImageEditor from './image-editor.vue';

    type Background = {
        image?: string;
        color?: string;
    };

    // 参数
    const props = defineProps({
        modelValue: {
            type: Object as PropType<Background>,
            required: true,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    });

    const emit = defineEmits<{
        (e: 'update:modelValue', value: Background): void;
    }>();

    const imageUrl = ref(props.modelValue?.image);

    const handleChange = () => {
        emit('update:modelValue', {
            image: imageUrl.value,
        });
    };

    watch(
        () => props.modelValue,
        (newValue, oldValue) => {
            if (newValue) {
                imageUrl.value = newValue?.image;
            }
        }
    );
</script>
