<template>
    <div class="border-b border-gray-200">
        <div
            ref="scrollRef"
            class="flex space-x-2 overflow-x-auto scroll-smooth no-scrollbar h-[48px]"
            @wheel.prevent="handleWheel"
            :style="{
                msOverflowStyle: 'none',
                scrollbarWidth: 'none',
                WebkitOverflowScrolling: 'touch',
            }">
            <el-button
                v-for="tab in tabs"
                :key="tab.value"
                :data-tab="tab.value"
                :type="modelValue.value === tab.value ? 'primary' : 'default'"
                :class="[modelValue.value === tab.value ? activeClass : '']"
                @click="handleTabClick(tab)"
                :size="buttonSize"
                :text="modelValue.value !== tab.value"
                :bg="modelValue.value !== tab.value"
                link>
                <span :class="['mx-4', fontClass]">{{ tab.label }}</span>
            </el-button>
        </div>

        <div v-if="showLeftArrow" class="absolute left-0 inset-y-0 flex items-center">
            <div class="h-full w-8 bg-gradient-mask-l" @click="handleScroll('left')">
                <div class="h-full flex items-center justify-center cursor-pointer">
                    <el-icon>
                        <ArrowLeft />
                    </el-icon>
                </div>
            </div>
        </div>

        <div v-if="showRightArrow" class="absolute right-0 inset-y-0 flex items-center">
            <div class="h-full w-8 bg-gradient-mask-r" @click="handleScroll('right')">
                <div class="h-full flex items-center justify-center cursor-pointer">
                    <el-icon>
                        <ArrowRight />
                    </el-icon>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';
    import { LabelValue } from '@chances/portal_common_core';

    export default defineComponent({
        name: 'TabGroup',
        components: {
            ArrowLeft,
            ArrowRight,
        },
        props: {
            tabs: {
                type: Array as () => LabelValue[],
                required: true,
            },
            modelValue: {
                type: LabelValue,
                required: true,
            },
            activeClass: {
                type: String,
                default: '',
            },
            buttonSize: {
                type: String,
                default: 'default',
            },
            fontClass: {
                type: String,
                default: 'text-2xl',
            },
        },
        emits: ['update:modelValue', 'tab-click'],
        data() {
            return {
                showLeftArrow: false,
                showRightArrow: false,
            };
        },
        methods: {
            // 检查滚动位置
            checkScrollPosition(): void {
                const el = this.$refs.scrollRef as HTMLElement;
                if (!el) return;

                const hasScroll = el.scrollWidth > el.clientWidth;
                if (!hasScroll) {
                    this.showLeftArrow = false;
                    this.showRightArrow = false;
                    return;
                }

                this.showLeftArrow = el.scrollLeft > 0;
                this.showRightArrow = Math.ceil(el.scrollLeft + el.clientWidth) < el.scrollWidth;
            },
            // 处理滚动
            handleScroll(direction: 'left' | 'right'): void {
                const el = this.$refs.scrollRef as HTMLElement;
                if (!el) return;

                const scrollAmount = el.clientWidth * 0.8;
                el.scrollBy({
                    left: direction === 'left' ? -scrollAmount : scrollAmount,
                    behavior: 'smooth',
                });
            },
            // 处理点击标签
            handleTabClick(tab: LabelValue): void {
                if (this.modelValue.value === tab.value) {
                    return;
                }

                this.$emit('update:modelValue', tab);
                this.$emit('tab-click', tab);

                this.$nextTick(() => {
                    const el = this.$refs.scrollRef as HTMLElement;
                    if (!el) return;

                    const tabElement: any = el.querySelector(`[data-tab="${tab.value}"]`);
                    if (!tabElement) return;

                    const tabLeft = tabElement.offsetLeft;
                    const tabWidth = tabElement.offsetWidth;
                    const containerWidth = el.clientWidth;

                    const scrollTo = tabLeft - (containerWidth - tabWidth) / 2;

                    requestAnimationFrame(() => {
                        el.scrollTo({
                            left: Math.max(0, scrollTo),
                            behavior: 'smooth',
                        });
                    });
                });
            },
            // 处理鼠标滚动
            handleWheel(e: WheelEvent): void {
                const el = this.$refs.scrollRef as HTMLElement;
                if (!el || !e.deltaY) return;

                el.scrollBy({
                    left: e.deltaY > 0 ? 150 : -150,
                    behavior: 'smooth',
                });
            },
        },
        mounted() {
            this.checkScrollPosition();
            this.$refs.scrollRef &&
                (this.$refs.scrollRef as HTMLElement).addEventListener('scroll', this.checkScrollPosition);
            window.addEventListener('resize', this.checkScrollPosition);
        },
        beforeUnmount() {
            if (this.$refs.scrollRef) {
                (this.$refs.scrollRef as HTMLElement).removeEventListener('scroll', this.checkScrollPosition);
            }
            window.removeEventListener('resize', this.checkScrollPosition);
        },
    });
</script>

<style scoped>
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }

    :deep(.el-button) {
        margin: 0;
    }

    :deep(.el-button),
    :deep(.el-button:active) {
        transform: none;
        transition:
            background-color 0.3s,
            border-color 0.3s,
            color 0.3s;
    }

    :deep(.el-button) {
        --el-button-hover-bg-color: var(--el-color-primary-light-9);
        --el-button-hover-border-color: var(--el-color-primary);
        --el-button-hover-text-color: var(--el-color-primary);
    }

    :deep(.el-button .el-button--primary:hover) {
        --el-button-hover-bg-color: var(--el-color-primary);
        --el-button-hover-border-color: var(--el-color-primary);
        --el-button-hover-text-color: var(--el-color-white);
    }

    .bg-gradient-mask-l {
        background: linear-gradient(to right, rgb(255, 255, 255) 0%, rgba(255, 255, 255, 0.9) 100%);
    }

    .bg-gradient-mask-r {
        background: linear-gradient(to left, rgb(255, 255, 255) 0%, rgba(255, 255, 255, 0.9) 100%);
    }
</style>
