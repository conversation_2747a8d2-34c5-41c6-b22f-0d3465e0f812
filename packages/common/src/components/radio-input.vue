<template>
    <div class="radio-input-container">
        <el-radio-group v-model:model-value="value" class="flex flex-wrap" @change="handleChange">
            <el-radio v-for="item in options" :key="item.value" :value="item.value">{{ item.label }}</el-radio>
        </el-radio-group>

        <div class="mt-4" v-if="input">
            <el-input
                v-model:model-value="inputValue"
                @change="handleChange"
                @blur="handleChange"
                :placeholder="placeholder"
                maxlength="200"
                show-word-limit
                clearable />
        </div>
    </div>
</template>

<script lang="ts">
    import { defineComponent } from 'vue';
    import { LabelValue } from '@chances/portal_common_core';

    export default defineComponent({
        name: 'RadioInput',
        props: {
            modelValue: {
                type: String,
            },
            options: {
                type: Array<LabelValue>,
                required: true,
            },
            placeholder: {
                type: String,
                default: '',
            },
            input: {
                type: Boolean,
                default: true,
            },
        },
        emits: ['update:modelValue', 'change'],
        computed: {
            value: {
                get() {
                    return this.options.some((opt) => opt.value === this.modelValue) ? this.modelValue : '';
                },
                set(val: string) {
                    this.$emit('update:modelValue', val);
                },
            },
            inputValue: {
                get() {
                    return !this.options.some((opt) => opt.value === this.modelValue) ? this.modelValue : '';
                },
                set(val: string) {
                    this.$emit('update:modelValue', val.trim());
                },
            },
        },
        methods: {
            handleChange() {
                // 触发 el-form-item 的验证
                this.$emit('change', this.modelValue);
            },
        },
    });
</script>

<style scoped>
    .radio-input-container {
        width: 100%;
    }

    .radio-input-container :deep(.el-radio__label) {
        white-space: normal;
        word-break: break-word;
    }
</style>
