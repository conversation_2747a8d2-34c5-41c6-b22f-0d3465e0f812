<template>
    <div class="table-container">
        <slot name="table"></slot>
        <div class="pagination-wrapper">
            <slot name="pagination">
                <pagination
                    :total="total"
                    :current-page-value="currentPage"
                    @update:currentPageValue="onCurrentChange"
                    @update:pageSizeValue="onSizeChange" />
            </slot>
        </div>
    </div>
</template>

<script setup lang="ts">
    const props = defineProps({
        total: { type: Number, required: true },
        currentPage: { type: Number, required: true },
        pageSize: { type: Number, required: true },
    });
    const emits = defineEmits(['update:currentPage', 'update:pageSize']);

    function onCurrentChange(val: number) {
        emits('update:currentPage', val);
    }

    function onSizeChange(val: number) {
        emits('update:pageSize', val);
    }
</script>

<style scoped>
    .table-container {
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 400px;
    }

    .table-container ::v-deep(.el-table) {
        flex: 1 1 auto;
        height: calc(100% - 56px);
        overflow: auto;
    }

    .pagination-wrapper {
        flex-shrink: 0;
        background: #fff;
        box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
        padding: 12px 0 0 0;
        z-index: 2;
    }
</style>
