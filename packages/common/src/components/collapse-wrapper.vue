<template>
    <div class="collapse-container">
        <slot></slot>
    </div>
</template>

<script setup lang="ts">
    import { provide, reactive, watch } from 'vue';
    import { CollapseContext } from '@smartdesk/common/types';

    defineOptions({
        name: 'CollapseWrapper',
    });

    const props = defineProps({
        modelValue: {
            type: Array as () => string[],
            default: () => [],
        },
        according: {
            type: Boolean,
            default: false,
        },
    });

    const emit = defineEmits<{
        (e: 'update:modelValue', value: string[]): void;
        (e: 'change', value: string[]): void;
    }>();

    // 当前展示的面板
    const activeNames = reactive<string[]>([...props.modelValue]);

    // 设置展开的面板
    const setActiveNames = (names: string[]): void => {
        activeNames.length = 0;
        activeNames.push(...names);
        emit('update:modelValue', [...activeNames]);
        emit('change', [...activeNames]);
    };

    // 处理子项的变化
    const handleItemChange = (name: string, expanded: boolean): void => {
        // 手风琴模式下只能展示一个
        if (props.according) {
            setActiveNames(expanded ? [name] : []);
        } else {
            const index = activeNames.indexOf(name);
            if (expanded && index === -1) {
                activeNames.push(name);
            } else if (!expanded && index !== -1) {
                activeNames.splice(index, 1);
            }
            emit('update:modelValue', [...activeNames]);
            emit('change', [...activeNames]);
        }
    };

    // 将方法提供给子组件
    provide<CollapseContext>('collapseContext', {
        activeNames,
        handleItemChange,
    });

    // 监听外部传入的值变化
    watch(
        () => props.modelValue,
        (val) => {
            if (val.toString() !== activeNames.toString()) {
                activeNames.length = 0;
                activeNames.push(...val);
            }
        },
        { deep: true }
    );
</script>

<style scoped>
    .collapse-container {
        width: 100%;
    }
</style>
