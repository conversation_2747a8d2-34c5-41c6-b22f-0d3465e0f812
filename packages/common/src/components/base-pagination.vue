<template>
    <div class="flex justify-end py-3 md:justify-end sm:justify-center">
        <el-config-provider :locale="zhCn">
            <el-pagination
                v-model:current-page="pageInfo.page"
                v-model:page-size="pageInfo.size"
                :page-sizes="pageInfo.sizeArray"
                :size="'default'"
                :background="true"
                :layout="layout"
                :total="pageInfo.totalElements"
                @size-change="onPageSizeChange"
                @current-change="onPageChange" />
        </el-config-provider>
    </div>
</template>

<script setup lang="ts">
    import { PageInfo } from '@smartdesk/common/types';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';

    const props = defineProps({
        pageInfo: {
            type: Object as () => PageInfo,
            default: () => ({
                page: 1,
                size: 10,
                sizeArray: [10, 20, 50, 100],
                totalElements: 0,
            }),
        },
        // 布局选项
        layout: {
            type: String,
            default: 'total, sizes, prev, pager, next, jumper',
        },
    });

    // 定义事件
    const emit = defineEmits(['change']);

    // 页码变化
    const onPageChange = (page: number) => {
        const updatedPageInfo = { ...props.pageInfo, page };
        emit('change', updatedPageInfo);
    };

    // 每页大小变化
    const onPageSizeChange = (size: number) => {
        const updatedPageInfo = { ...props.pageInfo, size, page: 1 };
        emit('change', updatedPageInfo);
    };
</script>
