<template>
    <div class="dimension-editor">
        <!-- 宽度输入框 -->
        <div class="input-group">
            <div class="input-field">
                <label for="width">宽:</label>
                <el-input-number
                    @change="onBlur"
                    v-model="localDimensions.width"
                    :placeholder="'宽'"
                    :aria-label="'Width'" />
            </div>
            <!-- 高度输入框 -->
            <div class="input-field">
                <label for="height">高:</label>
                <el-input-number
                    @change="onBlur"
                    v-model="localDimensions.height"
                    :placeholder="'高'"
                    :aria-label="'height'" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { ref } from 'vue';

    const emit = defineEmits<{
        (e: 'update:modelValue', value: Record<string, any>): void;
        (e: 'change', value: Record<string, any>): void;
    }>();
    // 接收传入的 v-model 数据 (Object with width and height)
    const props = defineProps({
        modelValue: {
            type: Object,
            required: true,
            default: () => ({
                width: 100,
                height: 100,
            }),
        },
    });

    // 将传入的对象属性解构

    // 使用本地数据存储并处理宽度和高度
    const localDimensions = ref({
        width: props.modelValue.width,
        height: props.modelValue.height,
    });

    const onBlur = () => {
        emit('change', localDimensions.value);
        emit('update:modelValue', localDimensions.value);
    };

    // 监听 localDimensions 的变化，更新父组件的数据
</script>

<style scoped>
    .dimension-editor {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 200px;
    }

    .input-group {
        display: flex;
        gap: 10px; /* 控制输入框之间的间距 */
        align-items: center;
    }

    .input-field {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
    }

    label {
        margin-bottom: 3px;
        font-weight: bold;
    }

    input {
        width: 80px;
        padding: 2px;
        text-align: center;
        font-size: 14px;
    }
</style>
