<template>
    <div
        class="marquee-container transform overflow-hidden relative h-full cursor-pointer"
        ref="container"
        @mouseenter="isPlaying = false"
        @mouseleave="isPlaying = true">
        <div class="marquee-content inline-block whitespace-nowrap" :style="contentStyle">
            <span class="inline-block pt-1.5" ref="contentSpan">{{ title }}</span>
            <span v-if="shouldDuplicate" class="inline-block">{{ title }}</span>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, nextTick, onMounted, ref, watch } from 'vue';

    // 参数
    const props = defineProps({
        title: String,
    });

    // 容器
    const container = ref<HTMLElement | null>(null);

    // 内容 span 元素
    const contentSpan = ref<HTMLElement | null>(null);

    // 内容宽度
    const contentWidth = ref<number>(0);

    // 容器宽度
    const containerWidth = ref<number>(0);

    // 是否在播放
    const isPlaying = ref<boolean>(true);

    // 是否重复
    const shouldDuplicate = computed(() => contentWidth.value > containerWidth.value);

    // 内容样式，计算滚动
    const contentStyle = computed(() => ({
        animation: shouldDuplicate.value ? `marquee ${contentWidth.value / 35}s linear infinite` : 'none',
        animationPlayState: isPlaying.value ? 'running' : 'paused',
        transform: shouldDuplicate.value ? 'translateX(0)' : 'translateX(-50%)',
        left: shouldDuplicate.value ? '100%' : '50%',
    }));

    // 设置容器宽度、内容宽度
    const updateSizes = () => {
        if (!container.value || !contentSpan.value) return;

        containerWidth.value = container.value.offsetWidth;
        contentWidth.value = contentSpan.value.offsetWidth + 16;
    };

    // 当 title 变化时重新计算
    watch(
        () => props.title,
        () => {
            // 使用 nextTick 确保 DOM 更新后再测量
            nextTick(() => {
                updateSizes();
            });
        }
    );

    onMounted(() => {
        updateSizes();
        const observer = new ResizeObserver(updateSizes);
        if (container.value) observer.observe(container.value);

        // 也监听内容区域的变化
        if (contentSpan.value) observer.observe(contentSpan.value);
    });
</script>

<style>
    @keyframes marquee {
        0% {
            transform: translateX(0);
        }
        100% {
            transform: translateX(calc(-100% - 16px));
        }
    }

    .marquee-content {
        position: absolute;
        transform: translateY(-50%);
    }
</style>
