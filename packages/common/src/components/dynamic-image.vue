<template>
    <div class="w-full">
        <div v-for="(item, index) in localImageItems" :key="index" class="mb-4">
            <div>
                <el-form-item :label="getTypeLabel(item.type) + '：'" :prop="`${formFieldName}[${index}].type`">
                    <div class="flex-container">
                        <div class="flex-select">
                            <el-select
                                v-model="item.type"
                                placeholder="请选择图片类型"
                                class="select-full-width"
                                clearable>
                                <el-option
                                    v-for="type in availableTypes(index)"
                                    :key="type.value"
                                    :label="type.label"
                                    :value="type.value" />
                            </el-select>
                        </div>

                        <div class="flex-select ml-3">
                            <el-select
                                v-model="item.source"
                                placeholder="请选择图片来源"
                                class="select-full-width"
                                :disabled="!item.type"
                                clearable>
                                <el-option
                                    v-for="source in sources"
                                    :key="source.value"
                                    :label="source.label"
                                    :value="source.value" />
                            </el-select>
                        </div>

                        <el-button
                            type="danger"
                            icon="Delete"
                            circle
                            plain
                            size="small"
                            @click="removeImageItem(index)"
                            class="ml-2 flex-shrink-0"
                            :disabled="localImageItems.length <= 1"></el-button>
                    </div>
                </el-form-item>
            </div>

            <el-form-item
                v-if="item.source"
                label=" "
                :prop="`${formFieldName}[${index}].${item.source === 'upload' ? 'fileList' : 'imageId'}`">
                <template v-if="item.source && item.source !== 'upload'">
                    <div class="image-select-container">
                        <el-select
                            v-model="item.imageId"
                            placeholder="请选择图片"
                            @change="handleImageSelected(index)"
                            class="select-full-width">
                            <el-option
                                v-for="image in getImagesBySource(item.source)"
                                :key="image.id"
                                :label="image.name"
                                :value="image.id" />
                        </el-select>
                    </div>
                </template>

                <template v-else-if="item.source === 'upload'">
                    <el-upload
                        action="#"
                        list-type="picture-card"
                        :auto-upload="false"
                        :file-list="item.fileList || []"
                        :on-preview="handlePictureCardPreview"
                        :on-change="(file: UploadFile) => handleFileChange(file, index)"
                        :on-remove="() => handleFileRemove(index)"
                        :class="{
                            'hide-upload': item.fileList && item.fileList.length > 0,
                        }">
                        <el-icon>
                            <Plus />
                        </el-icon>
                    </el-upload>

                    <el-dialog v-model="dialogVisible">
                        <img w-full :src="dialogImageUrl" alt="Preview Image" />
                    </el-dialog>
                </template>
            </el-form-item>
        </div>
    </div>

    <div v-if="canAddNewItem" class="w-full flex items-center justify-end cursor-pointer">
        <div
            @click="addImageItem"
            class="flex items-center justify-center border border-gray-400 border-dashed rounded-sm w-6 h-6 mb-6">
            <el-icon>
                <Plus />
            </el-icon>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, ref, watch } from 'vue';
    import { Plus } from '@element-plus/icons-vue';
    import type { UploadFile, UploadProps } from 'element-plus';

    // 定义图片项类型
    interface ImageItem {
        type: string; // 图片类型
        source: string; // 图片来源
        imageUrl?: string; // 图片URL
        imageName?: string; // 图片名称
        imageId?: string | number; // 选中的图片ID
        fileList?: UploadFile[]; // 上传文件列表
    }

    defineOptions({
        name: 'DynamicImage',
    });

    // 定义组件Props
    const props = defineProps({
        // 表单字段名称 (用于嵌套表单验证)
        formFieldName: {
            type: String,
            default: 'imageItems',
        },

        // 可选图片类型列表
        types: {
            type: Array as () => { label: string; value: string }[],
            default: () => [],
        },

        // 图片来源配置
        sources: {
            type: Array as () => { label: string; value: string }[],
            default: () => [],
        },

        // 初始图片数据
        modelValue: {
            type: Array as () => ImageItem[],
            default: () => [],
        },

        // 图片库数据
        imageLibrary: {
            type: Object as () => Record<string, any[]>,
            default: () => ({
                basicHorizontal: [],
                basicVertical: [],
                operationHorizontal: [],
                operationVertical: [],
            }),
        },
    });

    // 定义事件
    const emit = defineEmits<{
        (e: 'update:modelValue', items: ImageItem[]): void;
        (e: 'change', items: ImageItem[]): void;
    }>();

    const dialogImageUrl = ref('');
    const dialogVisible = ref(false);

    // 本地状态
    const localImageItems = ref<ImageItem[]>([...props.modelValue]);

    // 获取已使用的图片类型
    const usedTypes = computed(() => {
        return localImageItems.value.filter((item) => item.type).map((item) => item.type);
    });

    // 判断是否所有项都已选择图片类型
    const allItemsHaveType = computed(() => {
        return localImageItems.value.every((item) => item.type);
    });

    // 判断是否可以添加新项
    const canAddNewItem = computed(() => {
        // 所有现有项都必须选择了图片类型
        // 还有可用的图片类型未被使用
        return allItemsHaveType.value && usedTypes.value.length < props.types.length;
    });

    // 获取当前项可用的图片类型
    const availableTypes = (currentIndex: number) => {
        return props.types.filter((type) => {
            // 当前项已选择的类型始终可用
            if (localImageItems.value[currentIndex].type === type.value) {
                return true;
            }
            // 其他未被使用的类型
            return !usedTypes.value.includes(type.value);
        });
    };

    // 根据来源获取图片列表
    const getImagesBySource = (source: string) => {
        return props.imageLibrary[source] || [];
    };

    // 获取类型标签
    const getTypeLabel = (type: string): string => {
        if (!type) return '图片类型';

        const typeObj = props.types.find((t) => t.value === type);
        return typeObj ? typeObj.label : '图片类型';
    };

    // 添加新图片项
    const addImageItem = () => {
        // 如果不能添加新项，直接返回
        if (!canAddNewItem.value) return;

        localImageItems.value.push({
            type: '',
            source: '',
            fileList: [],
        });
        updateValue();
    };

    // 移除图片项
    const removeImageItem = (index: number) => {
        if (localImageItems.value.length <= 1) return;

        localImageItems.value.splice(index, 1);
        updateValue();
    };

    // 处理图片选择
    const handleImageSelected = (index: number) => {
        const item = localImageItems.value[index];
        const source = item.source;
        const imageId = item.imageId;

        const images = getImagesBySource(source);
        const selectedImage = images.find((img) => img.id === imageId);

        if (selectedImage) {
            item.imageUrl = selectedImage.url;
            item.imageName = selectedImage.name;
        }

        updateValue();
    };

    // 处理文件上传
    const handleFileChange = (file: UploadFile, index: number) => {
        const item = localImageItems.value[index];
        item.fileList = [file];

        if (file.raw) {
            const reader = new FileReader();
            reader.onload = (e) => {
                item.imageUrl = e.target?.result as string;
                item.imageName = file.name;
                updateValue();
            };
            reader.readAsDataURL(file.raw);
        }
    };

    // 处理文件移除
    const handleFileRemove = (index: number) => {
        const item = localImageItems.value[index];
        item.fileList = [];
        item.imageUrl = '';
        item.imageName = '';
        updateValue();
    };

    // 处理预览
    const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
        dialogImageUrl.value = uploadFile.url!;
        dialogVisible.value = true;
    };

    // 更新值
    const updateValue = () => {
        emit('update:modelValue', [...localImageItems.value]);
        emit('change', [...localImageItems.value]);
    };

    // 监听外部值变化
    watch(
        () => props.modelValue,
        (newVal) => {
            if (JSON.stringify(newVal) !== JSON.stringify(localImageItems.value)) {
                localImageItems.value = [...newVal];
            }
        },
        { deep: true }
    );

    // 确保有初始项
    if (localImageItems.value.length === 0) {
        addImageItem();
    }
</script>

<style scoped>
    :deep(.el-upload--picture-card) {
        width: 80px;
        height: 80px;
        line-height: 80px;
    }

    :deep(.el-upload-list--picture-card .el-upload-list__item) {
        width: 80px;
        height: 80px;
    }

    .hide-upload :deep(.el-upload--picture-card) {
        display: none;
    }

    .flex-container {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .flex-select {
        flex: 1;
        min-width: 0;
        max-width: 200px;
    }

    .image-select-container {
        width: 100%;
        max-width: 300px;
    }

    .select-full-width {
        width: 100%;
    }

    :deep(.el-select) {
        width: 100%;
        display: block;
    }

    :deep(.el-select .el-input) {
        width: 100%;
    }

    :deep(.el-select .el-input__wrapper) {
        width: 100%;
    }
</style>
