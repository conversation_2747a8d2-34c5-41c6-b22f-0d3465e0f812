<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { RestPageResultResponse } from '@chances/portal_common_core';
    import { Layout } from '@smartdesk/common/types';

    const props = defineProps<{
        // 获取数据
        fetchData: (page: number) => Promise<RestPageResultResponse<any>>;
        // 下拉框提示
        placeholder?: string;
        // 刷新标识
        refreshKey?: string;
    }>();

    // 下拉框值
    const selectValue = ref<string | number>('');
    // 选项列表
    const options = ref<any[]>([]);
    // 加载中
    const loading = ref<boolean>(false);
    // 当前页码
    const page = ref<number>(0);
    // 是否还有更多
    const hasMore = ref<boolean>(true);

    /**
     * 获取列表
     */
    const fetchList = async () => {
        if (!hasMore.value || loading.value) return;
        loading.value = true;
        try {
            const res: RestPageResultResponse<Layout> = await props.fetchData(page.value);

            const newData = res.result || [];
            options.value = [...options.value, ...newData];
            // 判断是否还有更多数据
            hasMore.value = newData.length === res.page?.size;
            page.value++;
            loading.value = false;
        } catch (error) {
            console.error('获取列表失败:', error);
        } finally {
            loading.value = false;
        }
    };

    /**
     * 滚动到底部时触发加载
     *
     * @param event 事件对象
     */
    const handleScroll = (event: any) => {
        const { scrollTop, offsetHeight, scrollHeight } = event.target;
        if (scrollTop + offsetHeight >= scrollHeight - 20) {
            fetchList();
        }
    };

    /**
     * 重新获取列表
     */
    const reFetch = async () => {
        page.value = 0;
        await fetchList();
    };

    // 手动刷新
    watch(
        () => props.refreshKey,
        async () => {
            await reFetch();
        }
    );

    onMounted(async () => {
        await fetchList();
    });
</script>

<template>
    <el-select v-model="selectValue" :placeholder="placeholder" :loading="loading" @scroll="handleScroll">
        <el-option v-for="item in options" :key="item.id" :label="item.name" :value="item.code">
            <slot :item="item" />
        </el-option>
        <template #empty>
            <div class="text-center py-2.5 text-gray-400">
                {{ hasMore ? '加载中...' : '没有更多数据' }}
            </div>
        </template>
    </el-select>
</template>

<style scoped></style>
