<template>
    <el-config-provider :locale="zhCn">
        <el-date-picker v-model="dateRange" v-bind="$attrs" @change="handleChange" format="YYYY-MM-DD" />
    </el-config-provider>
</template>

<script lang="ts" setup>
    import { ref, watch } from 'vue';
    import dayjs from 'dayjs';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';

    // 参数
    const props = withDefaults(
        defineProps<{
            startDate: Date | string | number | null;
            endDate: Date | string | number | null;
            format?: string;
            valueFormat?: string;
        }>(),
        {
            startDate: null,
            endDate: null,
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
        }
    );

    // 定义事件
    const emit = defineEmits<{
        (e: 'update:startDate', value: any): void;
        (e: 'update:endDate', value: any): void;
        (e: 'change', value: [any, any] | null): void;
    }>();

    // 内部日期范围状态
    const dateRange = ref<[any, any] | null>(null);

    // 格式化日期
    const formatDate = (date: any): any => {
        if (!date) return null;

        return dayjs(date).format(props.valueFormat);
    };

    // 解析日期
    const parseDate = (date: any): any => {
        if (!date) return null;

        // 如果输入是字符串，转换为日期对象
        if (typeof date === 'string') {
            return dayjs(date).toDate();
        }

        return date;
    };

    // 根据 startDate 和 endDate 更新日期范围
    const updateDateRange = () => {
        if (props.startDate && props.endDate) {
            dateRange.value = [parseDate(props.startDate), parseDate(props.endDate)];
        } else {
            dateRange.value = null;
        }
    };

    // 监听外部传入的日期变化
    watch(() => props.startDate, updateDateRange);
    watch(() => props.endDate, updateDateRange);

    // 初始化
    updateDateRange();

    // 处理日期变化
    const handleChange = (val: [any, any] | null) => {
        if (val && val.length === 2) {
            const formattedStartDate = formatDate(val[0]);
            const formattedEndDate = formatDate(val[1]);

            emit('update:startDate', formattedStartDate);
            emit('update:endDate', formattedEndDate);
            emit('change', [formattedStartDate, formattedEndDate]);
        } else {
            emit('update:startDate', null);
            emit('update:endDate', null);
            emit('change', null);
        }
    };
</script>
