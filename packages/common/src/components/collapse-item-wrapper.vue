<template>
    <div class="collapse-item">
        <slot name="header" :is-active="isActive" :toggle="toggleCollapse">
            <div class="collapse-header" @click.stop="toggleCollapse">
                <el-icon>
                    <CaretBottom v-if="isActive" />
                    <CaretRight v-else />
                </el-icon>
                <span>{{ title }}</span>
            </div>
        </slot>

        <div v-show="isActive" class="collapse-content">
            <slot></slot>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { CaretBottom, CaretRight } from '@element-plus/icons-vue';
    import { computed, inject } from 'vue';
    import { CollapseContext } from '@smartdesk/common/types';

    defineOptions({
        name: 'CollapseItemWrapper',
    });

    const props = defineProps({
        name: {
            type: [String, Number],
            required: true,
        },
        title: {
            type: String,
            default: '',
        },
    });

    // 从父组件注入上下文
    const collapseContext = inject<CollapseContext | null>('collapseContext', null);
    const isActive = computed((): boolean => {
        if (collapseContext && props.name) {
            return collapseContext.activeNames.includes(props.name.toString());
        }
        return false;
    });

    // 点击折叠面板
    const toggleCollapse = (): void => {
        if (collapseContext && props.name) {
            collapseContext.handleItemChange(props.name.toString(), !isActive.value);
        }
    };
</script>

<style scoped>
    .collapse-header {
        cursor: pointer;
        padding: 12px 0;
        display: flex;
        align-items: center;
        gap: 8px;
    }
</style>
