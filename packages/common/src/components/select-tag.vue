<template>
    <el-dialog append-to-body v-model="dialogVisible" width="700px" @open="openDialog" @close="onCloseDialog">
        <template #header>
            {{ showTitle() }}
        </template>
        <div style="margin-bottom: 10px">
            <el-input placeholder="请输入名称或编码" clearable @keyup.enter="searchTag" v-model="keyword">
                <template #append>
                    <el-button :icon="Search" @click="searchTag" />
                </template>
            </el-input>
        </div>
        <!-- 添加已选中标签展示区域 -->
        <div v-if="checkedTableTags.length > 0" style="margin-bottom: 10px">
            <div style="margin-bottom: 5px; color: #606266">已选标签：</div>
            <el-tag
                v-for="code in checkedTableTags"
                :key="code"
                closable
                @close="handleClose(code)"
                style="margin-right: 10px; margin-bottom: 5px">
                {{ getTagName(code) }}
            </el-tag>
        </div>
        <el-scrollbar style="height: 450px; transition: 0.2s">
            <el-table stripe :data="tableData" style="width: 100%">
                <el-table-column width="130" label="全选">
                    <template #default="scope">
                        <el-checkbox
                            :indeterminate="scope.row.isIndeterminate"
                            v-model="scope.row.checkAll"
                            @change="handleCheckAllChange($event, scope.row)"
                            >{{ scope.row.name }}
                        </el-checkbox>
                    </template>
                </el-table-column>
                <el-table-column label="">
                    <template #default="scope">
                        <el-checkbox-group v-model="checkedTableTags" @change="handleCheckedChange($event, scope.row)">
                            <el-checkbox
                                :value="item.code"
                                :style="index == 0 ? 'margin-left:30px' : ''"
                                v-for="(item, index) in scope.row.tags"
                                :key="item.code"
                                >{{ item.name }}
                            </el-checkbox>
                        </el-checkbox-group>
                    </template>
                </el-table-column>
            </el-table>
        </el-scrollbar>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="chooseAll(false)">清空</el-button>
                <el-button type="primary" @click="onClickConfirm"> 确定 </el-button>
            </div>
        </template>
    </el-dialog>
</template>
<script setup lang="ts">
    import { ref, watch } from 'vue';
    // @ts-ignore
    import pinyin from 'js-pinyin';
    import { Search } from '@element-plus/icons-vue';
    import { MaterialTagVO } from '@smartdesk/common/types';

    // 参数
    const props = defineProps<{
        visible: boolean;
        // 标签类型
        tagType: string;
        // 标签列表
        tagOption: MaterialTagVO[];
        // 选中的标签
        checkedTags: string[];
    }>();

    // 选中的标签
    const checkedTableTags = ref<string[]>([]);

    const emit = defineEmits(['update:visible', 'on-click-confirm']);

    // 是否显示对话框
    const dialogVisible = ref(props.visible);
    // 关键字
    const keyword = ref<string>('');
    // 标签列表数据
    const tableData = ref<
        Array<{
            name: string;
            tags: MaterialTagVO[];
            checkAll: boolean;
            isIndeterminate: boolean;
        }>
    >([]);
    // 标识搜索事件
    const searchTag = () => {
        // 如果关键字为空,重新加载所有标签但不重置选中状态
        if (!keyword.value) {
            // 保存当前选中的标签
            const currentCheckedTags = [...checkedTableTags.value];

            // 生成拼音分组
            const letters = [...'ABCDEFGHIJKLMNOPQRSTUVWXYZ', '其他'];
            const map = new Map<
                string,
                {
                    name: string;
                    tags: MaterialTagVO[];
                    checkAll: boolean;
                    isIndeterminate: boolean;
                }
            >();

            // 初始化分组容器
            letters.forEach((char) => {
                map.set(char, {
                    name: char,
                    tags: [],
                    checkAll: false,
                    isIndeterminate: false,
                });
            });

            // 填充所有数据
            props.tagOption.forEach((tag) => {
                const firstChar = pinyin.getFullChars(tag.name)[0]?.toUpperCase() || '#';
                const group = map.get(firstChar) || map.get('其他')!;
                group.tags.push(tag);
            });

            // 更新表格数据,保持选中状态
            tableData.value = Array.from(map.values())
                .filter((group) => group.tags.length > 0)
                .map((group) => {
                    const selectedCount = group.tags.filter((t) => currentCheckedTags.includes(t.code)).length;
                    return {
                        ...group,
                        checkAll: selectedCount === group.tags.length,
                        isIndeterminate: selectedCount > 0 && selectedCount < group.tags.length,
                    };
                });

            return;
        }

        // 生成拼音分组
        const letters = [...'ABCDEFGHIJKLMNOPQRSTUVWXYZ', '其他'];
        const map = new Map<
            string,
            {
                name: string;
                tags: MaterialTagVO[];
                checkAll: boolean;
                isIndeterminate: boolean;
            }
        >();

        // 初始化分组容器
        letters.forEach((char) => {
            map.set(char, {
                name: char,
                tags: [],
                checkAll: false,
                isIndeterminate: false,
            });
        });

        // 根据关键字过滤标签
        const filteredTags = props.tagOption.filter((tag) => {
            const searchText = keyword.value.toLowerCase();
            return tag.name.toLowerCase().includes(searchText) || tag.code.toLowerCase().includes(searchText);
        });

        // 填充过滤后的数据
        filteredTags.forEach((tag) => {
            const firstChar = pinyin.getFullChars(tag.name)[0]?.toUpperCase() || '#';
            const group = map.get(firstChar) || map.get('其他')!;
            group.tags.push(tag);
        });

        // 更新表格数据,保持选中状态
        tableData.value = Array.from(map.values())
            .filter((group) => group.tags.length > 0)
            .map((group) => {
                const selectedCount = group.tags.filter((t) => checkedTableTags.value.includes(t.code)).length;
                return {
                    ...group,
                    checkAll: selectedCount === group.tags.length,
                    isIndeterminate: selectedCount > 0 && selectedCount < group.tags.length,
                };
            });
    };

    // 关闭对话框
    const onCloseDialog = () => {
        tableData.value = [];
        emit('update:visible', false);
    };

    // 打开对话框
    const openDialog = () => {
        // 清空搜索框
        keyword.value = '';

        // 清空旧数据
        tableData.value = [];
        checkedTableTags.value = [];
        // 生成拼音分组
        const letters = [...'ABCDEFGHIJKLMNOPQRSTUVWXYZ', '其他'];
        const map = new Map<
            string,
            {
                name: string;
                tags: MaterialTagVO[];
                checkAll: boolean;
                isIndeterminate: boolean;
            }
        >();
        // 初始化分组容器
        letters.forEach((char) => {
            map.set(char, {
                name: char,
                tags: [],
                checkAll: false,
                isIndeterminate: false,
            });
        });
        // 填充数据
        props.tagOption.forEach((tag) => {
            const firstChar = pinyin.getFullChars(tag.name)[0]?.toUpperCase() || '#';
            const group = map.get(firstChar) || map.get('其他')!;
            group.tags.push(tag);
        });

        // 处理回显数据
        if (props.checkedTags?.length > 0) {
            checkedTableTags.value = [...props.checkedTags];
        }

        tableData.value = Array.from(map.values())
            .filter((group) => group.tags.length > 0)
            .map((group) => {
                const selectedCount = group.tags.filter((t) => checkedTableTags.value.includes(t.code)).length;
                return {
                    ...group,
                    checkAll: selectedCount === group.tags.length,
                    isIndeterminate: selectedCount > 0 && selectedCount < group.tags.length,
                };
            });
    };

    // 点击全选按钮事件
    const handleCheckAllChange = (checked: boolean, row: (typeof tableData.value)[number]) => {
        row.checkAll = checked;
        row.isIndeterminate = false;
        row.tags.forEach((tag) => {
            if (checked) {
                if (!checkedTableTags.value.includes(tag.code)) {
                    checkedTableTags.value.push(tag.code);
                }
            } else {
                const index = checkedTableTags.value.indexOf(tag.code);
                if (index != -1) {
                    checkedTableTags.value.splice(index, 1);
                }
            }
        });
    };

    // 选中事件
    const handleCheckedChange = (codes: string[], row: (typeof tableData.value)[number]) => {
        const currentCodes = row.tags.map((t) => t.code);
        const allSelected = currentCodes.every((c) => codes.includes(c));
        const someSelected = currentCodes.some((c) => codes.includes(c));
        row.checkAll = allSelected;
        row.isIndeterminate = someSelected && !allSelected;
    };

    // 取消事件
    const chooseAll = (flag: boolean) => {
        tableData.value.forEach((item) => {
            handleCheckAllChange(flag, item);
        });
    };
    // 显示标题
    const showTitle = () => {
        const titles: Record<string, string> = {
            base: '基础标签',
            management: '运营标签',
        };
        return titles[props.tagType || '选择标签'];
    };
    // 确定按钮
    const onClickConfirm = () => {
        emit('on-click-confirm', checkedTableTags.value, props.tagType);
        onCloseDialog();
    };
    // 获取标签名称
    const getTagName = (code: string) => {
        for (const group of tableData.value) {
            const tag = group.tags.find((t) => t.code === code);
            if (tag) return tag.name;
        }
        return '';
    };

    // 关闭标签
    const handleClose = (code: string) => {
        const index = checkedTableTags.value.indexOf(code);
        if (index > -1) {
            checkedTableTags.value.splice(index, 1);
        }
        // 更新表格中的选中状态
        tableData.value.forEach((group) => {
            const selectedCount = group.tags.filter((t) => checkedTableTags.value.includes(t.code)).length;
            group.checkAll = selectedCount === group.tags.length;
            group.isIndeterminate = selectedCount > 0 && selectedCount < group.tags.length;
        });
    };
    watch(
        () => props.visible,
        (val) => {
            dialogVisible.value = val;
        }
    );
</script>
<script lang="ts">
    export default {
        name: 'SelectTag',
    };
</script>
<style scoped></style>
