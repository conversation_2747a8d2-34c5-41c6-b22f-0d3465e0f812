<template>
    <div class="flex items-center">
        <div class="mr-1 w-2 h-2 rounded-full" :class="dotClass"></div>
        <div>
            <slot>{{ text }}</slot>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed } from 'vue';

    defineOptions({
        name: 'StatusDot',
    });

    const props = defineProps({
        type: {
            type: String,
            default: 'default',
            validator: (val: string) => ['success', 'error', 'info', 'warning', 'default'].includes(val),
        },
        text: {
            type: String,
            default: '',
        },
    });

    const dotClass = computed(() => {
        const classes: any = {
            success: 'bg-green-600',
            error: 'bg-red-600',
            warning: 'bg-yellow-600',
            info: 'bg-blue-600',
            default: 'bg-gray-600',
        };
        return classes[props.type] || classes.default;
    });
</script>
