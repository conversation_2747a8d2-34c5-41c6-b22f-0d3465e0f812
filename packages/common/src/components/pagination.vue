<template>
    <div class="pagination">
        <el-config-provider :locale="zhCn">
            <el-pagination
                :current-page="currentPage"
                :page-size="pageSize"
                :page-sizes="pageSizes"
                :size="size"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange" />
        </el-config-provider>
    </div>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';
    import { PAGE_SIZES } from '@smartdesk/common/constant';

    // 定义 props
    const props = defineProps({
        total: { type: Number, required: true }, // 总条数
        currentPageValue: { type: Number, required: true }, // 当前页
        pageSizeValue: { type: Number, default: PAGE_SIZES[0] || 20 }, // 默认每页条数
        pageSizes: { type: Array as () => number[], default: () => PAGE_SIZES }, // 使用常量
        size: { type: String, default: 'default' }, // 组件尺寸
    });

    // 定义 emits
    const emit = defineEmits(['update:currentPageValue', 'update:pageSizeValue']);

    // 控制当前页和每页条数
    const currentPage = ref(props.currentPageValue);
    const pageSize = ref(props.pageSizeValue);

    // 监听外部值变化
    watch(
        () => props.currentPageValue,
        (newVal) => (currentPage.value = newVal)
    );
    watch(
        () => props.pageSizeValue,
        (newVal) => (pageSize.value = newVal)
    );

    // 处理分页变化
    const handleSizeChange = (size: number) => {
        pageSize.value = size;
        emit('update:pageSizeValue', size);
    };
    const handleCurrentChange = (page: number) => {
        currentPage.value = page;
        emit('update:currentPageValue', page);
    };
</script>

<style scoped>
    .pagination {
        margin: 20px 0;
    }
</style>
