<template>
    <el-form :model="rect" label-width="auto" label-suffix=":">
        <div class="flex flex-col items-center">
            <el-form-item label="宽">
                <el-input-number :disabled="disabled" v-model="rect.width" @change="handleChange" />
            </el-form-item>
            <el-form-item label="高">
                <el-input-number :disabled="disabled" v-model="rect.height" @change="handleChange" />
            </el-form-item>
        </div>
    </el-form>
</template>

<script setup lang="ts">
    import { PropType, ref, watch } from 'vue';

    // 参数
    const props = defineProps({
        modelValue: {
            type: [Object, null] as PropType<{ width: number; height: number } | null>,
            default: null,
            required: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    });

    const emit = defineEmits<{
        (e: 'update:modelValue', value: { width: number; height: number }): void;
        (e: 'change', value: { width: number; height: number }): void;
    }>();

    const rect = ref<{ width: number; height: number }>({
        width: props.modelValue?.width || 0,
        height: props.modelValue?.height || 0,
    });

    const handleChange = () => {
        emit('update:modelValue', rect.value);
        emit('change', rect.value);
    };

    watch(
        () => props.modelValue,
        (newVal) => {
            if (newVal) {
                rect.value = newVal;
            }
        }
    );
</script>
