<template>
    <el-checkbox-group v-model="localValue" class="checkbox-row-group">
        <el-checkbox v-for="item in options" :key="item.value" :value="item.value" class="checkbox-row-item">
            {{ item.label }}
        </el-checkbox>
    </el-checkbox-group>
</template>

<script lang="ts" setup>
    import { computed, PropType } from 'vue';
    import { LabelValue } from '@chances/portal_common_core';

    // 参数
    const props = defineProps({
        modelValue: {
            type: Object as PropType<LabelValue[]>,
            default: () => [],
        },
        options: {
            type: Object as PropType<LabelValue[]>,
            default: () => [],
        },
    });

    // 事件
    const emits = defineEmits(['update:modelValue']);

    // 本地值
    const localValue = computed({
        get: () => {
            return [...props.modelValue];
        },
        set: (value) => {
            emits('update:modelValue', value);
        },
    });
</script>

<style scoped>
    .checkbox-row-group {
        display: flex;
        flex-wrap: wrap;
        gap: 0 6px;
        align-items: center;
    }

    .checkbox-row-item {
        margin: 0;
        padding: 0 4px;
        min-width: 64px;
        text-align: center;
        white-space: nowrap;
    }
</style>
