<template>
    <div class="relative">
        <el-image :src="formatSrc" :preview-src-list="formatSrcList" preview-teleported v-bind="$attrs" class="z-0">
            <template #error>
                <image-error-fallback text="图片加载失败" />
            </template>
        </el-image>

        <div class="absolute top-0 right-0 h-8 w-full text-white text-right text-base rounded-t-lg cursor-pointer">
            <div class="whitespace-nowrap overflow-auto scrollbar-hide h-full mr-1">
                {{ resolution }}
            </div>
        </div>

        <div class="w-full rounded-b-lg bg-gray-200/80 transform h-8 -mt-8 relative z-10">
            <text-marquee :title="title" :key="title" />
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed } from 'vue';
    import ImageErrorFallback from './image-error-fallback.vue';

    // 参数
    const props = defineProps({
        withPrefix: {
            type: Boolean,
            default: false,
        },
        src: {
            type: String,
            default: '',
        },
        title: {
            type: String,
            default: '',
        },
        resolution: {
            type: String,
            default: '',
        },
    });

    // 格式化后的图片地址
    const formatSrc = computed(() => {
        return props.src;
    });

    // 格式化后的图片地址列表
    const formatSrcList = computed(() => {
        return [props.src];
    });
</script>

<style scoped>
    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
</style>
