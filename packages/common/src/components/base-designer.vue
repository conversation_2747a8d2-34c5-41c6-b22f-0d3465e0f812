<template>
    <div class="flex flex-col h-screen">
        <div class="h-20">
            <slot name="top"></slot>
        </div>

        <el-container class="flex-1 overflow-hidden relative">
            <el-aside :style="leftStyle" class="bg-white transition-all duration-300 flex flex-row">
                <slot name="left" v-if="leftVisible"></slot>
                <el-divider
                    direction="vertical"
                    class="h-full flex items-center justify-center"
                    style="height: 100%; margin: 0; padding: 0 4px; cursor: pointer"
                    @click="handleLayoutListClick">
                    <div class="writing-vertical">
                        {{ leftVisible ? '收起' : '展开' }}
                    </div>
                </el-divider>
            </el-aside>

            <el-main class="transition-all duration-300 relative">
                <slot name="content"></slot>
            </el-main>

            <el-aside v-if="rightVisible" :style="rightStyle" class="bg-white transition-all duration-300">
                <slot name="right"></slot>
            </el-aside>

            <div v-if="!rightVisible" class="absolute right-0 top-0 h-full w-4 z-10 cursor-pointer"></div>
        </el-container>
    </div>
</template>

<script setup lang="ts">
    import { computed, onBeforeUnmount, ref, watch } from 'vue';

    defineOptions({
        name: 'BaseDesigner',
    });

    const props = withDefaults(
        defineProps<{
            leftWidth?: string | number;
            rightWidth?: string | number;
            hoverDelay?: number;
            collapsedLeftWidth?: string | number;
            collapsedRightWidth?: string | number;
        }>(),
        {
            leftWidth: '20%',
            rightWidth: '20%',
            hoverDelay: 300,
            collapsedLeftWidth: '20px',
            collapsedRightWidth: '20px',
        }
    );

    const leftVisible = ref<boolean>(true);
    const rightVisible = ref<boolean>(true);

    // 添加计时器变量，用于处理延迟
    let leftHoverTimer: number | null = null;
    let rightHoverTimer: number | null = null;

    const leftStyle = computed(() => ({
        width: leftVisible.value
            ? typeof props.leftWidth === 'number'
                ? `${props.leftWidth}px`
                : props.leftWidth
            : typeof props.collapsedLeftWidth === 'number'
              ? `${props.collapsedLeftWidth}px`
              : props.collapsedLeftWidth,
    }));

    const rightStyle = computed(() => ({
        width: rightVisible.value
            ? typeof props.rightWidth === 'number'
                ? `${props.rightWidth}px`
                : props.rightWidth
            : typeof props.collapsedRightWidth === 'number'
              ? `${props.collapsedRightWidth}px`
              : props.collapsedRightWidth,
    }));

    // 展开左侧面板
    const expandLeft = () => {
        // 清除右侧计时器
        if (rightHoverTimer) {
            clearTimeout(rightHoverTimer);
            rightHoverTimer = null;
        }

        // 设置左侧计时器
        if (!leftVisible.value && !leftHoverTimer) {
            leftHoverTimer = window.setTimeout(() => {
                leftVisible.value = true;
                rightVisible.value = false;
                leftHoverTimer = null;
            }, props.hoverDelay);
        }
    };

    // 展开右侧面板
    const expandRight = () => {
        // 清除左侧计时器
        if (leftHoverTimer) {
            clearTimeout(leftHoverTimer);
            leftHoverTimer = null;
        }

        // 设置右侧计时器
        if (!rightVisible.value && !rightHoverTimer) {
            rightHoverTimer = window.setTimeout(() => {
                leftVisible.value = false;
                rightVisible.value = true;
                rightHoverTimer = null;
            }, props.hoverDelay);
        }
    };

    // 手动切换面板可见性的方法
    const toggleLeft = () => {
        leftVisible.value = !leftVisible.value;
        if (leftVisible.value) {
            rightVisible.value = false;
        }
    };

    const toggleRight = () => {
        rightVisible.value = !rightVisible.value;
        if (rightVisible.value) {
            leftVisible.value = false;
        }
    };

    // 在可见性变化时触发窗口resize事件
    watch([leftVisible, rightVisible], () => {
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, 300);
    });

    // 组件卸载时清除计时器
    onBeforeUnmount(() => {
        if (leftHoverTimer) {
            clearTimeout(leftHoverTimer);
        }
        if (rightHoverTimer) {
            clearTimeout(rightHoverTimer);
        }
    });
    const handleLayoutListClick = () => {
        leftVisible.value = !leftVisible.value;
        if (leftVisible.value) {
            expandLeft();
        } else {
            expandRight();
        }
    };
    defineExpose({
        leftVisible,
        rightVisible,
        expandLeft,
        expandRight,
        toggleLeft,
        toggleRight,
    });
</script>

<style scoped>
    .writing-vertical {
        writing-mode: vertical-lr;
        text-orientation: upright;
        white-space: nowrap;
        font-size: 12px;
    }
</style>
