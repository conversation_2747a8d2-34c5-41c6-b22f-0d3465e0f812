<template>
    <el-button :link="link" :type="type" :size="size" @click="$emit('click')" v-bind="$attrs">
        <slot name="icon">
            <el-icon v-if="icon" :color="iconColor">
                <component :is="icon" />
            </el-icon>
        </slot>

        <slot>
            <span :class="textClass">{{ text }}</span>
        </slot>
    </el-button>
</template>

<script setup lang="ts">
    defineOptions({
        name: 'ActionButton',
    });

    defineProps({
        icon: {
            type: String,
            default: '',
        },
        iconColor: {
            type: String,
            default: '',
        },
        link: {
            type: Boolean,
            default: false,
        },
        type: {
            type: String,
            default: 'default',
        },
        size: {
            type: String,
            default: 'default',
        },
        text: {
            type: String,
            default: '',
        },
        textClass: {
            type: String,
            default: '',
        },
    });

    defineEmits<{
        (e: 'click'): void;
    }>();
</script>
