<template>
    <el-divider v-if="isActive" content-position="left">
        <slot name="activeHeader"></slot>
    </el-divider>

    <div v-else class="w-full">
        <el-divider content-position="left">
            <slot name="deActiveHeader"></slot>
        </el-divider>
        <div @click.stop class="w-full mb-2">
            <slot name="deActiveContent"></slot>
        </div>
    </div>
</template>

<script setup lang="ts">
    defineOptions({
        name: 'CollapseItemWrapperHeader',
    });

    defineProps({
        isActive: {
            type: Boolean,
            required: true,
        },
    });
</script>
