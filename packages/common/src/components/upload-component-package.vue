<template>
    <el-dialog
        :model-value="uploadDialogVisible"
        width="500"
        :before-close="handleClose"
        append-to-body
        :z-index="2000">
        <template #header>
            <span class="upload-title">上传组件包</span>
            {{ siteStore?.currentSite?.name }}
        </template>
        <el-upload
            class="upload-demo"
            drag
            :auto-upload="true"
            :http-request="customUpload"
            :show-file-list="true"
            accept=".zip"
            :multiple="false"
            :before-upload="beforeUpload"
            ref="uploadRef">
            <el-icon class="el-icon--upload">
                <upload-filled />
            </el-icon>
            <div class="el-upload__text">拖拽文件到此处或 <em>点击上传</em></div>
            <template #tip>
                <div class="el-upload__tip">请上传zip/rar格式的组件包文件，文件大小不能超过20MB</div>
            </template>
        </el-upload>
        <template #footer>
            <div class="dialog-footer">
                <el-button @click="onCancel">取消</el-button>
                <el-button type="primary" @click="onConfirm"> 确定 </el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { ref } from 'vue';
    import { useFeedback } from '@smartdesk/common/composables';
    import { componentApi } from '@smartdesk/common/api';
    import type { UploadInstance, UploadProps, UploadRequestOptions } from 'element-plus';
    import { useSiteStore } from '@smartdesk/common/stores';

    const props = defineProps<{
        uploadDialogVisible?: boolean;
    }>();

    const emit = defineEmits(['update:uploadDialogVisible', 'uploadSuccess']);

    const siteStore = useSiteStore();
    const feedback = useFeedback();

    // 20MB 大小限制
    const MAX_FILE_SIZE = 20 * 1024 * 1024;

    const uploadRef = ref<UploadInstance>();

    // 上传前验证
    const beforeUpload: UploadProps['beforeUpload'] = (file) => {
        if (file.size > MAX_FILE_SIZE) {
            feedback.error('文件大小不能超过20MB');
            return false;
        }
        return true;
    };

    // 自定义上传方法
    const customUpload = async (options: UploadRequestOptions) => {
        if (!siteStore.currentSiteCode) {
            feedback.error('请选择站点');
            return;
        }

        try {
            const res = await componentApi.importComponent(siteStore?.currentSiteCode, options.file);

            if (res.code === 200) {
                feedback.success('上传成功');
                handleUploadSuccess();
            } else {
                feedback.error(res.msg || '上传失败');
                handleUploadError();
            }
        } catch (error) {
            console.error('上传出错:', error);
            feedback.error('上传出错');
            handleUploadError();
        }
    };

    // 关闭对话框
    const handleClose = () => {
        if (uploadRef.value) {
            uploadRef.value.clearFiles();
        }
        emit('update:uploadDialogVisible', false);
    };

    // 取消按钮
    const onCancel = () => {
        handleClose();
    };

    // 确定按钮
    const onConfirm = () => {
        if (uploadRef.value) {
            uploadRef.value.submit();
        }
    };

    // 上传成功回调
    const handleUploadSuccess = () => {
        emit('uploadSuccess');
        handleClose();
    };

    // 上传失败回调
    const handleUploadError = () => {
        if (uploadRef.value) {
            uploadRef.value.clearFiles();
        }
    };
</script>

<style scoped>
    .upload-title {
        font-size: 24px;
    }
</style>
