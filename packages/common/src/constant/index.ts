import { LabelValue } from '@chances/portal_common_core';

// 坑位元素类型
export const CELL_ITEM_TYPE = {
    // 默认
    DEFAULT: 1,

    // 个性化
    PERSONALIZED: 0,
} as const;

// 页码列表
export const PAGE_SIZES = [20, 50, 100, 200];

// 默认页码
export const DEFAULT_PAGE_SIZE = 20;

// 频次类型
export const scheduleTypeList: LabelValue[] = [
    {
        label: '每日',
        value: 0,
    },
    {
        label: '每周',
        value: 1,
    },
    {
        label: '每月',
        value: 2,
    },
];

// 星期选项
export const weekList: LabelValue[] = [
    { label: '每周一', value: 1 },
    { label: '每周二', value: 2 },
    { label: '每周三', value: 3 },
    { label: '每周四', value: 4 },
    { label: '每周五', value: 5 },
    { label: '每周六', value: 6 },
    { label: '每周日', value: 7 },
];

// 天选项
export const dayList: LabelValue[] = Array.from({ length: 31 }, (_, i) => ({
    label: `${i + 1}号`,
    value: i + 1,
}));
