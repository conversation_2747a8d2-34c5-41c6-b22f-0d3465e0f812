import type { Handler } from '@chances/portal_common_core';
import { ElMessage } from 'element-plus';

// 全局标志位，防止重复处理登录失效
let isHandlingAuthError = false;

// 认证处理器
export const authHandler: Handler = {
    onRequest: async (config) => {
        // 在请求发送前可以进行一些处理，添加 token
        const token = sessionStorage.getItem('token');
        if (token) {
            config.headers.token = token;
        }
        return config;
    },
    onResponse: async (response) => {
        // 从响应头中获取 token 并更新
        const token = response.headers['token'];
        if (token) {
            sessionStorage.setItem('token', token);
        }

        // token 失效，清除 token，重定向到 login 页面
        if (response.data && response.data.code === 100406) {
            // 如果已经在处理认证错误，直接返回，避免重复处理
            if (isHandlingAuthError) {
                return response.data;
            }

            // 设置标志位，防止重复处理
            isHandlingAuthError = true;

            sessionStorage.removeItem('token');
            ElMessage.error('您的账号会话已过期，请重新登陆');
            setTimeout(() => {
                window.location.href = '/login';
            }, 2000);
        }

        // 简化响应，去掉 AxiosResponse 包装
        return response.data;
    },
};
