import { ElMessage, ElMessageBox } from 'element-plus';

export const useFeedback = () => {
    // 确认弹窗
    const confirm = async (message: string, title: string = '提示', type: any = 'info') => {
        return await ElMessageBox.confirm(message, {
            title: title,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: type,
        })
            .then(() => {
                return true;
            })
            .catch(() => {
                return false;
            });
    };

    // 成功消息
    const success = (message: string) => {
        ElMessage.success(message);
    };

    // 警告消息
    const warning = (message: string) => {
        ElMessage.warning(message);
    };

    // 失败消息
    const error = (message: string) => {
        ElMessage.error(message);
    };

    return {
        confirm,
        success,
        warning,
        error,
    };
};
