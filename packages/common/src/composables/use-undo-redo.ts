import { computed, nextTick, Ref, ref } from 'vue';

/**
 * undo/redo
 *
 * @param stateRef 要管理的 ref 对象
 * @returns 包含 undo/redo/saveHistory/clearHistory/canUndo/canRedo 的对象。
 */
export function useUndoRedo<T extends Record<string, any>>(stateRef: Ref<T>) {
    // 深拷贝函数，用于快照状态
    const deepClone = (obj: any): any => {
        if (obj === null || typeof obj !== 'object') return obj;
        if (Array.isArray(obj)) return obj.map(deepClone);
        const cloned = {} as any;
        Object.entries(obj).forEach(([k, v]) => {
            cloned[k] = deepClone(v);
        });
        return cloned;
    };

    // 历史栈和索引（index 指向当前状态的索引，从0开始）
    const historyStack = ref<T[]>([]);
    const historyIndex = ref(0);
    const maxLength = 100;

    // Flags
    const historyProcessing = ref(false); // 防递归
    const isLoadingHistory = ref(false); // 防并发

    // 初始化状态
    const initHistoryStack = (data: T) => {
        clearHistory();
        historyStack.value.push(deepClone(data));
        historyIndex.value = 0;
    };

    // 保存历史
    const saveHistory = () => {
        if (historyProcessing.value) return; // 防递归

        const snapshot = deepClone(stateRef.value); // 保存当前状态

        // 截断 redo 部分（如果有）
        if (historyStack.value.length > historyIndex.value + 1) {
            historyStack.value.splice(historyIndex.value + 1);
        }

        // 限制 maxLength：如果满，移除最早，并调整 index
        if (historyStack.value.length >= maxLength) {
            historyStack.value.shift();
            historyIndex.value = Math.max(0, historyIndex.value - 1);
        }

        // 推入新快照，并更新 index 到最新
        historyStack.value.push(snapshot);
        historyIndex.value = historyStack.value.length - 1;
    };

    // undo 函数
    const undo = async (callback?: () => void) => {
        if (isLoadingHistory.value || historyIndex.value <= 0) return;

        historyProcessing.value = true;
        isLoadingHistory.value = true;

        // 回滚到上一个索引
        historyIndex.value--;
        const history = historyStack.value[historyIndex.value];
        stateRef.value = deepClone(history) as T;
        await nextTick(); // 等待响应式更新
        if (typeof callback === 'function') callback();

        historyProcessing.value = false;
        isLoadingHistory.value = false;
    };

    // redo 函数
    const redo = async (callback?: () => void) => {
        if (isLoadingHistory.value || historyIndex.value >= historyStack.value.length - 1) return;

        historyProcessing.value = true;
        isLoadingHistory.value = true;

        // 前进到下一个索引
        historyIndex.value++;
        const history = historyStack.value[historyIndex.value];
        stateRef.value = deepClone(history) as T;
        await nextTick();
        if (typeof callback === 'function') callback();

        historyProcessing.value = false;
        isLoadingHistory.value = false;
    };

    // 清除历史
    const clearHistory = () => {
        historyStack.value = [];
        historyIndex.value = 0;
    };

    // canUndo、canRedo
    const canUndo = computed(() => historyIndex.value > 0);
    const canRedo = computed(() => historyIndex.value < historyStack.value.length - 1);

    return { initHistoryStack, undo, redo, saveHistory, clearHistory, canUndo, canRedo };
}
