import { ref, Ref, watch } from 'vue';
import { useRouter } from 'vue-router';

/**
 * 路由返回处理组合函数
 *
 * @param options.targetRoute 目标路由路径：例如 '/desktop' 或 '/list'
 * @param options.initialState 初始状态值：例如 false 或 { open: false }
 * @param options.onReturn 返回时执行的回调函数：(state: Ref<T>) => void，接收状态 ref
 * */
export function useRouteReturnHandler<T>(options: {
    targetRoute: string;
    initialState: T;
    onReturn: (state: Ref<T>) => void;
}) {
    const { targetRoute, initialState, onReturn } = options;
    const router = useRouter();

    // 状态 ref（泛型 T）
    const state = ref<T>(initialState) as Ref<T>;

    // 监听路由变化，检测“返回”到目标路由时触发 onReturn
    watch(
        () => router.currentRoute.value.path,
        (newPath, oldPath) => {
            if (newPath === targetRoute && oldPath !== targetRoute) {
                // 确认是从其他路径“返回”而来
                onReturn(state);
            }
        }
    );

    // 更新状态
    const setState = (newValue: T) => {
        state.value = newValue;
    };

    // 支持 keep-alive 的 activated 钩子：用户可在组件的 activated() 中调用此方法，模拟“返回”触发
    const onActivated = () => {
        if (router.currentRoute.value.path === targetRoute) {
            onReturn(state);
        }
    };

    return {
        state,
        setState,
        onActivated,
    };
}
