import { useRouter } from 'vue-router';

// 跳转设计器组合函数
export const useJumpToDesigner = () => {
    const router = useRouter();

    // 设计器类型
    type DESIGNER_TYPES = 'desktop' | 'page' | 'section';

    /**
     * 根据类型和编码跳转到对应设计器
     *
     * @param type 类型
     * @param code 编码
     * @param params 其他参数
     * */
    const jumpToDesigner = (type: DESIGNER_TYPES, code?: string, params?: Record<string, any>): void => {
        if (!code) {
            return;
        }

        switch (type) {
            case 'desktop':
                jumpToDesktopDesigner(type, code, params);
                break;
            case 'page':
                jumpToPageDesigner(type, code, params);
                break;
            case 'section':
                jumpToSectionDesigner(type, code, params);
                break;
            default:
                break;
        }
    };

    /**
     * 跳转到桌面设计器
     *
     * @param type 类型
     * @param desktopCode 桌面编码
     * @param params 其他参数
     * */
    const jumpToDesktopDesigner = (type: DESIGNER_TYPES, desktopCode?: string, params?: Record<string, any>): void => {
        router.push({
            path: '/page_designer',
            query: {
                desktopCode: desktopCode,
                mode: type,
                ...params,
            },
        });
    };

    /**
     * 跳转到页面设计器
     *
     * @param type 类型
     * @param pageCode 页面编码
     * @param params 其他参数
     * */
    const jumpToPageDesigner = (type: DESIGNER_TYPES, pageCode?: string, params?: Record<string, any>): void => {
        router.push({
            path: '/page_designer',
            query: {
                pageCode: pageCode,
                mode: type,
                ...params,
            },
        });
    };

    /**
     * 跳转到楼层定义设计器
     *
     * @param type 类型
     * @param sectionCode 楼层定义编码
     * @param params 其他参数
     * */
    const jumpToSectionDesigner = (type: DESIGNER_TYPES, sectionCode?: string, params?: Record<string, any>): void => {
        router.push({
            path: '/section_designer',
            query: {
                sectionCode: sectionCode,
                mode: type,
                ...params,
            },
        });
    };

    return {
        jumpToDesigner,
    };
};
