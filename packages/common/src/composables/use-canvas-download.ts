import { ref, Ref } from 'vue';
import html2canvas from 'html2canvas-pro';
import { RestResultResponse } from '@chances/portal_common_core';

type DownloadableElement = HTMLCanvasElement | HTMLElement; // 支持 Canvas 和普通元素
type ImageFormat = 'png' | 'jpeg';

export interface CanvasDownloadOptions {
    filename?: string;
    format?: ImageFormat;
    quality?: number;
    scale?: number; // 高清缩放因子（默认使用设备像素比）
    useCORS?: boolean; // 是否处理跨域资源
    backgroundColor?: string; // 背景颜色（用于修复透明背景问题）
    onClone?: (element: HTMLElement) => HTMLElement; // 克隆元素时的回调（用于修复渲染问题）
}

export interface CanvasDownloadAPI {
    download: () => Promise<void>;
    dataURL: Ref<string | null>;
    error: Ref<Error | null>;
    isLoading: Ref<boolean>; // 加载状态
    uploadToServer: () => Promise<RestResultResponse<string> | null>; // 新增上传方法
    getImageFile: () => File | null; // 获取File对象
}

export function useCanvasDownload(
    elementRef: Ref<DownloadableElement | null>,
    options?: CanvasDownloadOptions,
    uploadFunc?: (file: File) => Promise<RestResultResponse<string>>
): CanvasDownloadAPI {
    const dataURL = ref<string | null>(null);
    const error = ref<Error | null>(null);
    const isLoading = ref(false);

    const defaultOptions: CanvasDownloadOptions = {
        filename: 'canvas-image',
        format: 'png',
        quality: 1.0,
        scale: window.devicePixelRatio || 1,
        useCORS: true,
        backgroundColor: undefined,
    };

    const mergedOptions = { ...defaultOptions, ...options };

    // 获取 MIME 类型
    const getMimeType = (): string => {
        return `image/${mergedOptions.format === 'jpeg' ? 'jpeg' : 'png'}`;
    };

    // 处理 Canvas 导出
    const exportCanvas = async (canvas: HTMLCanvasElement): Promise<string> => {
        return canvas.toDataURL(getMimeType(), mergedOptions.format === 'jpeg' ? mergedOptions.quality : undefined);
    };

    // 处理普通元素导出（使用 html2canvas）
    const exportElement = async (element: HTMLElement): Promise<string> => {
        const clonedElement = mergedOptions.onClone?.(element) || element;

        const canvas = await html2canvas(clonedElement, {
            useCORS: mergedOptions.useCORS,
            scale: mergedOptions.scale,
            backgroundColor: mergedOptions.backgroundColor,
            allowTaint: !mergedOptions.useCORS, // 允许加载跨域图片（需谨慎）
            logging: false, // 关闭调试日志
        });

        return canvas.toDataURL(getMimeType(), mergedOptions.quality);
    };

    // 将 dataURL 转换为 File 对象
    const dataURLtoFile = (dataUrl: string, filename: string): File | null => {
        if (!dataUrl) return null;

        const arr = dataUrl.split(',');
        if (arr.length < 2) return null;

        const mime = arr[0].match(/:(.*?);/)?.[1];
        if (!mime) return null;

        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);

        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }

        return new File([u8arr], filename, { type: mime });
    };

    // 获取图片文件
    const getImageFile = (): File | null => {
        if (!dataURL.value) return null;

        const filename = `${mergedOptions.filename}.${mergedOptions.format}`;
        return dataURLtoFile(dataURL.value, filename);
    };

    // 导出图片并上传到服务器
    const uploadToServer = async (): Promise<RestResultResponse<string> | null> => {
        if (!uploadFunc) {
            console.error('上传函数未提供');
            return null;
        }

        if (!dataURL.value) {
            try {
                await generateImage();
            } catch (err) {
                error.value = err instanceof Error ? err : new Error('生成图片失败');
                return null;
            }
        }

        const file = getImageFile();
        if (!file) {
            error.value = new Error('生成文件失败');
            return null;
        }

        try {
            return await uploadFunc(file);
        } catch (err) {
            error.value = err instanceof Error ? err : new Error('上传失败');
            return null;
        }
    };

    // 生成图片数据
    const generateImage = async (): Promise<string> => {
        const element = elementRef.value;
        if (!element) {
            throw new Error('目标元素未找到');
        }

        // 根据元素类型选择导出方式
        let dataUrl: string;
        if (element instanceof HTMLCanvasElement) {
            dataUrl = await exportCanvas(element);
        } else {
            dataUrl = await exportElement(element);
        }

        dataURL.value = dataUrl;
        return dataUrl;
    };

    const handleDownload = async (): Promise<void> => {
        isLoading.value = true;
        error.value = null;

        try {
            await generateImage();

            // 创建下载链接（添加时间戳避免重复）
            const link = document.createElement('a');
            link.download = `${mergedOptions.filename}_${Date.now()}.${mergedOptions.format}`;
            link.href = dataURL.value as string;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        } catch (err) {
            error.value = err instanceof Error ? err : new Error('导出失败');
            console.error('导出错误:', err);
        } finally {
            isLoading.value = false;
        }
    };

    return {
        download: handleDownload,
        dataURL,
        error,
        isLoading,
        uploadToServer,
        getImageFile,
    };
}
