import { ref } from 'vue';
import { LoadingInstance } from 'element-plus/es/components/loading/src/loading';
import { ElLoading } from 'element-plus';

const loadingCount = ref<number>(0);
let loadingInstance: LoadingInstance | null = null;

/**
 * 全局 loading 组合函数
 * */
export const useGlobalLoading = () => {
    // 展示 loading
    const show = (): void => {
        loadingCount.value++;
        if (loadingCount.value === 1) {
            loadingInstance = ElLoading.service({
                lock: true,
                text: '加载中...',
                background: 'rgba(0, 0, 0, 0.7)',
                fullscreen: true,
            });
        }
    };

    // 隐藏 loading
    const hide = (): void => {
        if (loadingCount.value > 0) {
            loadingCount.value--;
            if (loadingCount.value === 0 && loadingInstance) {
                loadingInstance.close();
                loadingInstance = null;
            }
        }
    };

    return { show, hide };
};
