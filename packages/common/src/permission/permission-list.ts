// 项目前缀
export const PERMISSION_PREFIX = 'smartdesk';

// 实体
export const ENTITY = {
    SITE: 'site',
    DESKTOP: 'desktop',
    NAV: 'nav',
    NAV_GROUP: 'navGroup',
    PAGE: 'page',
    PAGE_SECTION: 'pageSection',
    PAGE_CELL: 'pageCell',
    SECTION: 'section',
    LAYOUT: 'layout',
    COMPONENT: 'component',
    COMPONENT_STYLE: 'componentStyle',
    LINK_TYPE: 'linkType',
    PERSONAL_RULE_FOLDER: 'personalRuleFolder',
    PERSONAL_RULE: 'personalRule',
    BOX: 'box',
};

// 通用操作
export const COMMON_ACTION = {
    // 新增
    CREATE: 'create',
    // 引用
    QUOTE: 'quote',
    // 编辑
    EDIT: 'update',
    // 送审
    AUDIT: 'audit',
    // 全部送审
    AUDIT_ALL: 'auditAll',
    // 上线
    ONLINE: 'online',
    // 下线
    OFFLINE: 'offline',
    // 删除
    DELETE: 'delete',
    // 启用
    ENABLE: 'enable',
    // 禁用
    DISABLE: 'disable',
    // 批量送审
    BATCH_AUDIT: 'batchAudit',
    // 批量上线
    BATCH_ONLINE: 'batchOnline',
    // 批量下线
    BATCH_OFFLINE: 'batchOffline',
    // 批量删除
    BATCH_DELETE: 'batchDelete',
    // 批量启用
    BATCH_ENABLE: 'batchEnable',
    // 批量禁用
    BATCH_DISABLE: 'batchDisable',
};

// 特殊操作
export const SPECIAL_ACTION = {
    // 导入
    IMPORT: 'import',
    // 导出
    EXPORT: 'export',
    // 更换
    CHANGE: 'change',
    // 权限配置
    PERMISSION_CONFIG: 'permissionConfig',
    // 另存为楼层定义
    SAVE_AS_SECTION: 'saveAsSection',
    // 新增坑位
    ADD_CELL: 'addCell',
    // 新增子导航
    ADD_CHILD_NAV: 'addChildNav',
    // 复制
    COPY: 'copy',
    // 编辑坑位样式
    EDIT_STYLE: 'editStyle',
};

// 分隔符
export const SPLITTER = ':';

// 生成权限编码
export const generatePermissionCode = (entity: string, action: string) => {
    return PERMISSION_PREFIX + SPLITTER + entity + SPLITTER + action;
};

// 管理端数据权限
export const ADMIN_BIZ_PERMISSION = {
    // 网站数据权限
    SITE: {
        // 新增
        CREATE: generatePermissionCode(ENTITY.SITE, COMMON_ACTION.CREATE),
        // 编辑
        EDIT: generatePermissionCode(ENTITY.SITE, COMMON_ACTION.EDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.SITE, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.SITE, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.SITE, COMMON_ACTION.DISABLE),
        // 批量启用
        BATCH_ENABLE: generatePermissionCode(ENTITY.SITE, COMMON_ACTION.BATCH_ENABLE),
        // 批量禁用
        BATCH_DISABLE: generatePermissionCode(ENTITY.SITE, COMMON_ACTION.BATCH_DISABLE),
        // 批量删除
        BATCH_DELETE: generatePermissionCode(ENTITY.SITE, COMMON_ACTION.BATCH_DELETE),
    },
    // 桌面数据权限
    DESKTOP: {
        // 新增
        CREATE: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.CREATE),
        // 编辑
        EDIT: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.EDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.DISABLE),
        // 送审
        AUDIT: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.AUDIT),
        // 上线
        ONLINE: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.ONLINE),
        // 下线
        OFFLINE: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.OFFLINE),
    },
    // 导航数据权限
    NAV: {
        // 新增
        CREATE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.CREATE),
        // 编辑
        EDIT: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.EDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.DISABLE),
        // 送审
        AUDIT: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.AUDIT),
        // 上线
        ONLINE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.ONLINE),
        // 下线
        OFFLINE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.OFFLINE),
        // 批量启用
        BATCH_ENABLE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.BATCH_ENABLE),
        // 批量禁用
        BATCH_DISABLE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.BATCH_DISABLE),
        // 批量删除
        BATCH_DELETE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.BATCH_DELETE),
        // 批量送审
        BATCH_AUDIT: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.BATCH_AUDIT),
        // 批量上线
        BATCH_ONLINE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.BATCH_ONLINE),
        // 批量下线
        BATCH_OFFLINE: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.BATCH_OFFLINE),
        // 新增子导航
        ADD_CHILD_NAV: generatePermissionCode(ENTITY.NAV, SPECIAL_ACTION.ADD_CHILD_NAV),
    },
    // 导航分组数据权限
    NAV_GROUP: {
        // 新增
        CREATE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.CREATE),
        // 编辑
        EDIT: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.EDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.DISABLE),
        // 送审
        AUDIT: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.AUDIT),
        // 上线
        ONLINE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.ONLINE),
        // 下线
        OFFLINE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.OFFLINE),
        // 批量启用
        BATCH_ENABLE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.BATCH_ENABLE),
        // 批量禁用
        BATCH_DISABLE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.BATCH_DISABLE),
        // 批量删除
        BATCH_DELETE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.BATCH_DELETE),
        // 批量送审
        BATCH_AUDIT: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.BATCH_AUDIT),
        // 批量上线
        BATCH_ONLINE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.BATCH_ONLINE),
        // 批量下线
        BATCH_OFFLINE: generatePermissionCode(ENTITY.NAV_GROUP, COMMON_ACTION.BATCH_OFFLINE),
    },
    // 页面数据权限
    PAGE: {
        // 新增
        CREATE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.CREATE),
        // 编辑
        EDIT: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.EDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.DISABLE),
        // 送审
        AUDIT: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.AUDIT),
        // 上线
        ONLINE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.ONLINE),
        // 下线
        OFFLINE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.OFFLINE),
        // 批量启用
        BATCH_ENABLE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.BATCH_ENABLE),
        // 批量禁用
        BATCH_DISABLE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.BATCH_DISABLE),
        // 批量删除
        BATCH_DELETE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.BATCH_DELETE),
        // 批量送审
        BATCH_AUDIT: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.BATCH_AUDIT),
        // 批量上线
        BATCH_ONLINE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.BATCH_ONLINE),
        // 批量下线
        BATCH_OFFLINE: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.BATCH_OFFLINE),
        // 复制
        COPY: generatePermissionCode(ENTITY.PAGE, SPECIAL_ACTION.COPY),
    },
    // 页面楼层数据权限
    PAGE_SECTION: {
        // 编辑
        EDIT: generatePermissionCode(ENTITY.PAGE_SECTION, COMMON_ACTION.EDIT),
    },
    // 楼层定义数据权限
    SECTION: {
        // 新增
        CREATE: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.CREATE),
        // 编辑
        EDIT: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.EDIT),
        // 送审
        AUDIT: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.AUDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.DISABLE),
        // 批量启用
        BATCH_ENABLE: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.BATCH_ENABLE),
        // 批量禁用
        BATCH_DISABLE: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.BATCH_DISABLE),
        // 批量送审
        BATCH_AUDIT: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.BATCH_AUDIT),
        // 批量删除
        BATCH_DELETE: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.BATCH_DELETE),
        // 复制
        COPY: generatePermissionCode(ENTITY.SECTION, SPECIAL_ACTION.COPY),
    },
    // 布局数据权限
    LAYOUT: {
        // 编辑
        EDIT: generatePermissionCode(ENTITY.LAYOUT, COMMON_ACTION.EDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.LAYOUT, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.LAYOUT, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.LAYOUT, COMMON_ACTION.DISABLE),
        // 批量启用
        BATCH_ENABLE: generatePermissionCode(ENTITY.LAYOUT, COMMON_ACTION.BATCH_ENABLE),
        // 批量禁用
        BATCH_DISABLE: generatePermissionCode(ENTITY.LAYOUT, COMMON_ACTION.BATCH_DISABLE),
        // 批量删除
        BATCH_DELETE: generatePermissionCode(ENTITY.LAYOUT, COMMON_ACTION.BATCH_DELETE),
    },
    // 组件数据权限
    COMPONENT: {
        // 导入
        IMPORT: generatePermissionCode(ENTITY.COMPONENT, SPECIAL_ACTION.IMPORT),
        // 导出
        EXPORT: generatePermissionCode(ENTITY.COMPONENT, SPECIAL_ACTION.EXPORT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.COMPONENT, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.COMPONENT, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.COMPONENT, COMMON_ACTION.DISABLE),
        // 批量启用
        BATCH_ENABLE: generatePermissionCode(ENTITY.COMPONENT, COMMON_ACTION.BATCH_ENABLE),
        // 批量禁用
        BATCH_DISABLE: generatePermissionCode(ENTITY.COMPONENT, COMMON_ACTION.BATCH_DISABLE),
        // 批量删除
        BATCH_DELETE: generatePermissionCode(ENTITY.COMPONENT, COMMON_ACTION.BATCH_DELETE),
    },
    // 跳转链接
    LINK_TYPE: {
        // 新增
        CREATE: generatePermissionCode(ENTITY.LINK_TYPE, COMMON_ACTION.CREATE),
        // 编辑
        EDIT: generatePermissionCode(ENTITY.LINK_TYPE, COMMON_ACTION.EDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.LINK_TYPE, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.LINK_TYPE, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.LINK_TYPE, COMMON_ACTION.DISABLE),
        // 批量启用
        BATCH_ENABLE: generatePermissionCode(ENTITY.LINK_TYPE, COMMON_ACTION.BATCH_ENABLE),
        // 批量禁用
        BATCH_DISABLE: generatePermissionCode(ENTITY.LINK_TYPE, COMMON_ACTION.BATCH_DISABLE),
        // 批量删除
        BATCH_DELETE: generatePermissionCode(ENTITY.LINK_TYPE, COMMON_ACTION.BATCH_DELETE),
    },
    // 推荐策略目录
    PERSONAL_RULE_FOLDER: {
        // 新增
        CREATE: generatePermissionCode(ENTITY.PERSONAL_RULE_FOLDER, COMMON_ACTION.CREATE),
        // 编辑
        EDIT: generatePermissionCode(ENTITY.PERSONAL_RULE_FOLDER, COMMON_ACTION.EDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.PERSONAL_RULE_FOLDER, COMMON_ACTION.DELETE),
    },
    // 推荐策略
    PERSONAL_RULE: {
        // 新增
        CREATE: generatePermissionCode(ENTITY.PERSONAL_RULE, COMMON_ACTION.CREATE),
        // 编辑
        EDIT: generatePermissionCode(ENTITY.PERSONAL_RULE, COMMON_ACTION.EDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.PERSONAL_RULE, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.PERSONAL_RULE, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.PERSONAL_RULE, COMMON_ACTION.DISABLE),
        // 替换引用
        CHANGE: generatePermissionCode(ENTITY.PERSONAL_RULE, SPECIAL_ACTION.CHANGE),
    },
    // 机顶盒
    BOX: {
        // 新增
        CREATE: generatePermissionCode(ENTITY.BOX, COMMON_ACTION.CREATE),
        // 编辑
        EDIT: generatePermissionCode(ENTITY.BOX, COMMON_ACTION.EDIT),
        // 删除
        DELETE: generatePermissionCode(ENTITY.BOX, COMMON_ACTION.DELETE),
        // 启用
        ENABLE: generatePermissionCode(ENTITY.BOX, COMMON_ACTION.ENABLE),
        // 禁用
        DISABLE: generatePermissionCode(ENTITY.BOX, COMMON_ACTION.DISABLE),
        // 批量启用
        BATCH_ENABLE: generatePermissionCode(ENTITY.BOX, COMMON_ACTION.BATCH_ENABLE),
        // 批量禁用
        BATCH_DISABLE: generatePermissionCode(ENTITY.BOX, COMMON_ACTION.BATCH_DISABLE),
        // 批量删除
        BATCH_DELETE: generatePermissionCode(ENTITY.BOX, COMMON_ACTION.BATCH_DELETE),
        // 送审
        AUDIT: generatePermissionCode(ENTITY.NAV, COMMON_ACTION.AUDIT),
        // 批量送审
        BATCH_AUDIT: generatePermissionCode(ENTITY.BOX, COMMON_ACTION.BATCH_AUDIT),
    },
};

// 设计器端数据权限
export const DESIGN_BIZ_PERMISSION = {
    // 桌面数据权限
    DESKTOP: {
        // 编辑
        EDIT: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.EDIT),
        // 送审
        AUDIT: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.AUDIT),
        // 全部送审
        AUDIT_ALL: generatePermissionCode(ENTITY.DESKTOP, COMMON_ACTION.AUDIT_ALL),
    },
    // 页面数据权限
    PAGE: {
        // 编辑
        EDIT: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.EDIT),
        // 送审
        AUDIT: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.AUDIT),
        // 全部送审
        AUDIT_ALL: generatePermissionCode(ENTITY.PAGE, COMMON_ACTION.AUDIT_ALL),
    },
    // 页面楼层数据权限
    PAGE_SECTION: {
        // 编辑
        EDIT: generatePermissionCode(ENTITY.PAGE_SECTION, COMMON_ACTION.EDIT),
        // 送审
        AUDIT: generatePermissionCode(ENTITY.PAGE_SECTION, COMMON_ACTION.AUDIT),
        // 全部送审
        AUDIT_ALL: generatePermissionCode(ENTITY.PAGE_SECTION, COMMON_ACTION.AUDIT_ALL),
        // 批量送审
        BATCH_AUDIT: generatePermissionCode(ENTITY.PAGE_SECTION, COMMON_ACTION.BATCH_AUDIT),
        // 批量上线
        BATCH_ONLINE: generatePermissionCode(ENTITY.PAGE_SECTION, COMMON_ACTION.BATCH_ONLINE),
        // 批量下线
        BATCH_OFFLINE: generatePermissionCode(ENTITY.PAGE_SECTION, COMMON_ACTION.BATCH_OFFLINE),
        // 新增页面楼层
        CREATE: generatePermissionCode(ENTITY.PAGE_SECTION, COMMON_ACTION.CREATE),
        // 引用页面楼层
        QUOTE: generatePermissionCode(ENTITY.PAGE_SECTION, COMMON_ACTION.QUOTE),
        // 更换页面楼层
        CHANGE: generatePermissionCode(ENTITY.PAGE_SECTION, SPECIAL_ACTION.CHANGE),
        // 另存为楼层定义
        SAVE_AS_SECTION: generatePermissionCode(ENTITY.PAGE_SECTION, SPECIAL_ACTION.SAVE_AS_SECTION),
        // 删除
        DELETE: generatePermissionCode(ENTITY.PAGE_SECTION, COMMON_ACTION.DELETE),
        // 权限配置
        PERMISSION_CONFIG: generatePermissionCode(ENTITY.PAGE_SECTION, SPECIAL_ACTION.PERMISSION_CONFIG),
        // 新增坑位
        ADD_CELL: generatePermissionCode(ENTITY.PAGE_SECTION, SPECIAL_ACTION.ADD_CELL),
    },
    // 坑位数据权限
    PAGE_CELL: {
        // 编辑
        EDIT: generatePermissionCode(ENTITY.PAGE_CELL, COMMON_ACTION.EDIT),
        // 编辑样式
        EDIT_STYLE: generatePermissionCode(ENTITY.PAGE_CELL, SPECIAL_ACTION.EDIT_STYLE),
        // 送审
        AUDIT: generatePermissionCode(ENTITY.PAGE_CELL, COMMON_ACTION.AUDIT),
        // 全部送审
        AUDIT_ALL: generatePermissionCode(ENTITY.PAGE_CELL, COMMON_ACTION.AUDIT_ALL),
        // 新增坑位
        CREATE: generatePermissionCode(ENTITY.PAGE_CELL, COMMON_ACTION.CREATE),
        // 删除
        DELETE: generatePermissionCode(ENTITY.PAGE_CELL, COMMON_ACTION.DELETE),
        // 权限配置
        PERMISSION_CONFIG: generatePermissionCode(ENTITY.PAGE_CELL, SPECIAL_ACTION.PERMISSION_CONFIG),
    },
    // 楼层定义数据权限
    SECTION: {
        // 编辑
        EDIT: generatePermissionCode(ENTITY.SECTION, COMMON_ACTION.EDIT),
    },
};
