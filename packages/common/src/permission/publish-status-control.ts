import { AdminStatus, Page, PageCell, PageSection, PublishStatus } from '@smartdesk/common/types';

// 判断实体能否点击送审
export const canAudit = (data: PublishStatus): boolean => {
    // 只有审核状态为待送审、审核通过、审核驳回时，才可以点击送审
    return data.auditStatus === 0 || data.auditStatus === 2 || data.auditStatus === 3;
};

// 判断实体能否点击上线
export const canOnline = (data: PublishStatus): boolean => {
    // 判断能否点击送审
    if (!canAudit(data)) {
        return false;
    }

    // 只有上下线状态为待上线、已下线时，才可以点击上线
    return data.onlineStatus === 0 || data.onlineStatus === 2;
};

// 判断实体能否点击下线
export const canOffline = (data: PublishStatus): boolean => {
    // 判断能否点击送审
    if (!canAudit(data)) {
        return false;
    }

    // 只有上下线状态为已上线时，才可以点击下线
    return data.onlineStatus === 1;
};

// 判断实体能否点击删除
export const canDelete = (data: PublishStatus): boolean => {
    if (data.auditStatus) {
        // 如果实体有审核状态，则判断能否点击送审
        if (!canAudit(data)) {
            return false;
        }
    }

    // 只有删除状态为正常时，才可以点击删除
    return data.delFlag === 0;
};

// 判断实体能否点击删除
export const canDeleteByAdmin = (data: AdminStatus): boolean => {
    // 只有删除状态为正常时，才可以点击删除
    return data.delFlag === 0;
};

// 判断实体能否点击启用
export const canEnable = (data: AdminStatus): boolean => {
    // 只有启用状态为禁用，才可以点击启用
    return data.status === 0;
};

// 判断实体能否点击禁用
export const canDisable = (data: AdminStatus): boolean => {
    // 只有启用状态为启用，才可以点击禁用
    return data.status === 1;
};

// 判断实体能否点击编辑
export const canEdit = (data: PublishStatus): boolean => {
    // 只有审核状态为待送审、审核通过、审核驳回时，才可以点击编辑
    return data.auditStatus === 0 || data.auditStatus === 2 || data.auditStatus === 3;
};

// 判断楼层是否为引用楼层
export const isRefSection = (data: PageSection): boolean => {
    if (data) {
        return data.refSectionId != null && data.refSectionCode != null;
    }
    return false;
};

// 判断楼层是否为引用楼层
export const isRefSectionCellItem = (page: Page, pageCell: PageCell): boolean => {
    if (page && pageCell) {
        return page.code != pageCell.pageCode;
    }
    return false;
};

// 判断实体列表能否点击送审
export const canBatchAudit = (dataList: PublishStatus[]): boolean => {
    if (!dataList || dataList.length === 0) {
        return false;
    }

    // 只有每个都能点击送审，才可以送审
    return dataList.every((item) => canAudit(item));
};

// 判断实体列表能否点击上线
export const canBatchOnline = (dataList: PublishStatus[]): boolean => {
    if (!dataList || dataList.length === 0) {
        return false;
    }

    // 只有每个都能点击上线，才可以上线
    return dataList.every((item) => canOnline(item));
};

// 判断实体列表能否点击下线
export const canBatchOffline = (dataList: PublishStatus[]): boolean => {
    if (!dataList || dataList.length === 0) {
        return false;
    }

    // 只有每个都能点击下线，才可以下线
    return dataList.every((item) => canOffline(item));
};

// 判断实体列表能否点击删除
export const canBatchDelete = (dataList: PublishStatus[]): boolean => {
    if (!dataList || dataList.length === 0) {
        return false;
    }

    // 只有每个都能点击删除，才可以删除
    return dataList.every((item) => canDelete(item));
};

// 判断实体列表能否点击删除
export const canBatchDeleteByAdmin = (dataList: AdminStatus[]): boolean => {
    if (!dataList || dataList.length === 0) {
        return false;
    }

    // 只有每个都能点击删除，才可以删除
    return dataList.every((item) => canDeleteByAdmin(item));
};

// 判断实体列表能否点击启用
export const canBatchEnable = (dataList: AdminStatus[]): boolean => {
    if (!dataList || dataList.length === 0) {
        return false;
    }

    // 只有每个都能点击启用，才可以启用
    return dataList.every((item) => canEnable(item));
};

// 判断实体列表能否点击禁用
export const canBatchDisable = (dataList: AdminStatus[]): boolean => {
    if (!dataList || dataList.length === 0) {
        return false;
    }

    // 只有每个都能点击禁用，才可以禁用
    return dataList.every((item) => canDisable(item));
};

// 判断是否能批量导出
export const canBatchExport = (dataList: AdminStatus[]): boolean => {
    return !(!dataList || dataList.length === 0);
};
