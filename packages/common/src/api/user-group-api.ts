import { userGroupHttpClient } from '@smartdesk/common/http';
import { RestResultSetResponse, UserGroup } from '@smartdesk/common/types';

export interface UserGroupApi {
    /**
     * 枚举值
     */
    getUserGroupList(): Promise<RestResultSetResponse<Array<UserGroup>>>;
}

export const userGroupApi: UserGroupApi = {
    /**
     * 枚举值
     */
    getUserGroupList(): Promise<RestResultSetResponse<Array<UserGroup>>> {
        return userGroupHttpClient
            .post(`/usergroup/list`)
            .params({
                page: 0,
                size: 1000,
                status: 1,
                checkValid: true,
            })
            .send();
    },
};
