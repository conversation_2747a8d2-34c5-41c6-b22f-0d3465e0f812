import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestResponse } from '@chances/portal_common_core';

// 实体类型
export type EntityType =
    | 'Desktop'
    | 'Nav'
    | 'NavGroup'
    | 'Page'
    | 'PageSection'
    | 'PageCell'
    | 'PageCellItem'
    | 'Box'
    | 'Section';

// 操作类型
export type ActionType = 'CREATE' | 'UPDATE' | 'DELETE' | 'ONLINE' | 'OFFLINE' | 'ENABLE' | 'DISABLE' | 'SORT';

export interface PublishApi {
    /**
     * 发布实体
     *
     * @param entity 实体类型
     * @param code 实体code
     * @param action 操作类型
     */
    publishSelf(entity: EntityType, code: string, action: ActionType): Promise<RestResponse>;

    /**
     * 发布实体及关联实体
     *
     * @param entity 实体类型
     * @param code 实体code
     * @param action 操作类型
     */
    publishComplete(entity: EntityType, code: string, action: ActionType): Promise<RestResponse>;

    /**
     * 批量发布实体
     *
     * @param entity 实体类型
     * @param codes 实体code列表
     * @param action 操作类型
     */
    batchPublishSelf(entity: EntityType, codes: string[], action: ActionType): Promise<RestResponse>;

    /**
     * 批量发布实体及关联实体
     *
     * @param entity 实体类型
     * @param codes 实体code列表
     * @param action 操作类型
     */
    batchPublishComplete(entity: EntityType, codes: string[], action: ActionType): Promise<RestResponse>;
}

export const publishApi: PublishApi = {
    /**
     * 批量发布实体及关联实体
     *
     * @param entity 实体类型
     * @param codes 实体code列表
     * @param action 操作类型
     */
    batchPublishComplete(entity: EntityType, codes: string[], action: ActionType): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/publish/batch/complete/${entity}/${action}`, codes).send();
    },

    /**
     * 批量发布实体
     *
     * @param entity 实体类型
     * @param codes 实体code列表
     * @param action 操作类型
     */
    batchPublishSelf(entity: EntityType, codes: string[], action: ActionType): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/publish/batch/self/${entity}/${action}`, codes).send();
    },

    /**
     * 发布实体及关联实体
     *
     * @param entity 实体类型
     * @param code 实体code
     * @param action 操作类型
     */
    publishComplete(entity: EntityType, code: string, action: ActionType): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/publish/complete/${entity}/${code}/${action}`).send();
    },

    /**
     * 发布实体
     *
     * @param entity 实体类型
     * @param code 实体code
     * @param action 操作类型
     */
    publishSelf(entity: EntityType, code: string, action: ActionType): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/publish/self/${entity}/${code}/${action}`).send();
    },
};
