import { iamHttpClient } from '@smartdesk/common/http';
import { LabelValue, RestResultResponse } from '@chances/portal_common_core';
import { Dimension } from '@smartdesk/common/types';

// 维度相关 API 接口
export interface DimensionApi {
    // 查询组织树
    findDimensionTree(): Promise<RestResultResponse<Dimension[]>>;

    // 查询组织选项列表
    findDimensionList(): Promise<RestResultResponse<LabelValue[]>>;
}

export const dimensionApi: DimensionApi = {
    // 查询组织树
    findDimensionTree(): Promise<RestResultResponse<Dimension[]>> {
        return iamHttpClient.get('/dimension/tree').send();
    },

    // 查询组织选项列表
    findDimensionList(): Promise<RestResultResponse<LabelValue[]>> {
        return iamHttpClient.get('/dimension/options').params({ type: 'org' }).send();
    },
};
