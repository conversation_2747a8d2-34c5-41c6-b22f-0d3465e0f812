import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestResponse, RestResultResponse } from '@chances/portal_common_core';
import { LayoutFile, PageCell, PageCellItem } from '@smartdesk/common/types';

export interface CellApi {
    /**
     * 获取坑位及坑位元素的管理信息
     *
     * @param pageCellCode 坑位编码
     */
    getPageCellData(pageCellCode: string): Promise<RestResultResponse<PageCell>>;

    /**
     * 修改页面坑位管理
     *
     * @param code 坑位code
     * @param pageCellData 坑位数据
     */
    updatePageCellData(code: string, pageCellData: PageCell): Promise<RestResultResponse<PageCell>>;

    /**
     * 修改坑位样式
     *
     * @param code 坑位 code
     * @param pageCellData 坑位数据
     * */
    updatePageCellLayout(code: string, pageCellData: PageCell): Promise<RestResultResponse<PageCell>>;

    /**
     * 批量修改坑位样式
     *
     * @param componentStyleCode 组件样式编码
     * @param codeLayoutMap 坑位编码坑位样式映射表
     * */
    batchUpdatePageCellLayout(
        componentStyleCode: string,
        codeLayoutMap: Record<string, LayoutFile>
    ): Promise<RestResponse>;

    /**
     * 修改坑位组件
     *
     * @param code 坑位 code
     * @param pageCell 坑位
     * */
    updatePageCellComponent(code: string, pageCell: PageCell): Promise<RestResultResponse<PageCell>>;

    /**
     * 滚动模式下：新增坑位
     * */
    createPageCell(pageCell: Partial<PageCell>): Promise<RestResultResponse<PageCell>>;

    /**
     * 新增坑位元素
     *
     * @param pageCellItem 坑位元素
     * */
    createPageCellItem(pageCellItem: PageCellItem): Promise<RestResultResponse<PageCellItem>>;

    /**
     * 删除坑位元素
     *
     * @param codes 坑位code列表
     * */
    deletePageCellItems(codes: string[]): Promise<RestResponse>;

    /**
     * 修改坑位元素
     *
     * @param code 坑位元素code
     * @param pageCellItem 坑位元素
     */
    updatePageCellItem(code: string, pageCellItem: PageCellItem): Promise<RestResultResponse<PageCellItem>>;
}

export const cellApi: CellApi = {
    /**
     * 获取页面坑位及坑位元素管理信息
     *
     * @param pageCellCode 页面坑位编码
     */
    getPageCellData(pageCellCode: string): Promise<RestResultResponse<PageCell>> {
        return smartDeskHttpClient.get(`/cell`).params({ code: pageCellCode }).send();
    },

    /**
     * 修改坑位管理信息
     *
     * @param code 坑位code
     * @param pageCellData 坑位
     */
    updatePageCellData(code: string, pageCellData: PageCell): Promise<RestResultResponse<PageCell>> {
        return smartDeskHttpClient.post(`/cell/${code}`, pageCellData).send();
    },

    /**
     * 修改坑位样式
     *
     * @param code 坑位 code
     * @param pageCellData 坑位
     * */
    updatePageCellLayout(code: string, pageCellData: PageCell): Promise<RestResultResponse<PageCell>> {
        return smartDeskHttpClient.post(`/cell/${code}/layout`, pageCellData).send();
    },

    /**
     * 批量修改坑位样式
     *
     * @param componentStyleCode 组件样式编码
     * @param codeLayoutMap 坑位编码坑位样式映射表
     * */
    batchUpdatePageCellLayout(
        componentStyleCode: string,
        codeLayoutMap: Record<string, LayoutFile>
    ): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/cell/layout`, codeLayoutMap).params({ componentStyleCode }).send();
    },

    /**
     * 修改坑位组件
     *
     * @param code 坑位 code
     * @param pageCell 坑位
     * */
    updatePageCellComponent(code: string, pageCell: PageCell): Promise<RestResultResponse<PageCell>> {
        return smartDeskHttpClient.post(`/cell/${code}/component`, pageCell).send();
    },

    /**
     * 滚动模式下：新增坑位
     * */
    createPageCell(pageCell: Partial<PageCell>): Promise<RestResultResponse<PageCell>> {
        return smartDeskHttpClient.post(`/cell`, pageCell).send();
    },

    /**
     * 新增坑位元素
     *
     * @param pageCellItem 坑位元素
     * */
    createPageCellItem(pageCellItem: PageCellItem): Promise<RestResultResponse<PageCellItem>> {
        return smartDeskHttpClient.post(`/cell/item`, pageCellItem).send();
    },

    /**
     * 删除坑位元素
     *
     * @param codes 坑位code列表
     * */
    deletePageCellItems(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/cell/item/delete`, codes).send();
    },

    /**
     * 修改坑位元素管理数据
     *
     * @param code 坑位元素code
     * @param pageCellItem 坑位元素
     */
    updatePageCellItem(code: string, pageCellItem: PageCellItem): Promise<RestResultResponse<PageCellItem>> {
        return smartDeskHttpClient.post(`/cell/item/${code}`, pageCellItem).send();
    },
};
