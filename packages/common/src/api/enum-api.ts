import { cmsHttpClient, iamHttpClient } from '@smartdesk/common/http';
import { Enumeration, RestResultResponse } from '@chances/portal_common_core';

// 枚举 API
export interface EnumApi {
    // 获取枚举
    getEnumByCode(parentCode: string): Promise<RestResultResponse<Enumeration>>;

    // 获取子枚举
    childEnums(parentId: number): Promise<RestResultResponse<Enumeration[]>>;

    // 校验枚举
    checkEnum(form: any): Promise<RestResultResponse<boolean>>;

    // 保存枚举
    createEnum(form: any): Promise<RestResultResponse<Enumeration>>;

    // 更新枚举
    updateEnum(form: any): Promise<RestResultResponse<Enumeration>>;

    // 删除枚举
    deleteEnum(id: number): Promise<RestResultResponse<Enumeration>>;

    // 分页查询
    treeEnum(data: any): Promise<RestResultResponse<any>>;

    /**
     * 枚举值
     * @param params  枚举code
     */
    searchCmsEnum(params: any): Promise<RestResultResponse<Array<any>>>;
}

export const enumApi: EnumApi = {
    // 获取枚举
    getEnumByCode(parentCode: string) {
        return iamHttpClient.get(`/enum/${parentCode}`).send();
    },

    // 获取子枚举
    childEnums(parentId: number) {
        return iamHttpClient.get(`/enum/child`).params({ parentId }).send();
    },

    // 校验枚举
    checkEnum(form: any) {
        return iamHttpClient.get(`/enum/exist`).params(form).send();
    },

    // 保存枚举
    createEnum(form: any) {
        return iamHttpClient.post(`/enum/create`).data(form).send();
    },

    // 更新枚举
    updateEnum(form: any) {
        return iamHttpClient.post(`/enum/update`).data(form).send();
    },

    // 删除枚举
    deleteEnum(id: number) {
        return iamHttpClient.delete(`/enum/delete`).params({ id }).send();
    },

    // 分页查询
    treeEnum(data) {
        return iamHttpClient.get('/enum/tree').params(data).send();
    },

    /**
     * 枚举值
     * @param params  枚举code
     */
    searchCmsEnum(params: any): Promise<RestResultResponse<Array<any>>> {
        return cmsHttpClient.post(`/search/enum`).data(params).send();
    },
};
