import { smartDeskHttpClient } from '@smartdesk/common/http';
import { LabelValue, RestPageResultResponse, RestResponse, RestResultResponse } from '@chances/portal_common_core';
import { Layout, LayoutSearchForm, PaginationParams, SectionLayoutLayoutFile } from '@smartdesk/common/types';

export interface LayoutApi {
    /**
     * 根据编码获取布局
     * */
    getLayoutByCode(code: string): Promise<RestResultResponse<Layout>>;

    /**
     * 更新布局
     * */
    updateLayout(code: string, data: Layout): Promise<RestResultResponse<Layout>>;

    /**
     * 更新布局文件
     * */
    updateLayoutFile(code: string, data: SectionLayoutLayoutFile): Promise<RestResultResponse<Layout>>;

    /**
     * 分页获取布局列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getLayouts(params: Partial<LayoutSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Layout>>;

    // 查询网站下所有桌面布局选项
    listDesktopLayoutOptions(siteCode: string): Promise<RestResultResponse<Array<LabelValue>>>;

    // 条件查询
    findLayouts(params: any, pageInfo: Partial<PaginationParams>): Promise<RestPageResultResponse<Layout>>;

    // 新增布局
    createLayout(data: Layout): Promise<RestResultResponse<Layout>>;

    // 禁用
    disableLayout(code: string): Promise<RestResponse>;

    // 启用
    enableLayout(code: string): Promise<RestResponse>;

    // 批量禁用
    batchDisableLayout(data: any): Promise<RestResponse>;

    // 批量启用
    batchEnableLayout(data: any): Promise<RestResponse>;

    // 批量删除
    batchDeleteLayout(data: any): Promise<RestResponse>;
}

export const layoutApi: LayoutApi = {
    /**
     * 根据编码获取布局
     * */
    getLayoutByCode(code: string): Promise<RestResultResponse<Layout>> {
        return smartDeskHttpClient.get(`/layout`).params({ code }).send();
    },

    /**
     * 更新布局管理信息
     * */
    updateLayout(code: string, data: Layout): Promise<RestResultResponse<Layout>> {
        return smartDeskHttpClient.post(`/layout/${code}`).data(data).send();
    },

    /**
     * 更新布局文件
     * */
    updateLayoutFile(code: string, data: SectionLayoutLayoutFile): Promise<RestResultResponse<Layout>> {
        return smartDeskHttpClient.post(`/layout/${code}/file`).data(data).send();
    },

    /**
     * 分页获取布局列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getLayouts(params: Partial<LayoutSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Layout>> {
        return smartDeskHttpClient.post(`/layout/list`).data(params).params(pageInfo).send();
    },

    // 查询网站下所有桌面布局选项
    listDesktopLayoutOptions(siteCode: string): Promise<RestResultResponse<Array<LabelValue>>> {
        return smartDeskHttpClient.get('/layout/desktop/options', { params: { siteCode } }).send();
    },

    // 条件查询
    findLayouts(params: any, pageInfo: any): Promise<RestPageResultResponse<Layout>> {
        return smartDeskHttpClient.post('/layout/list').data(params).params(pageInfo).send();
    },

    // 新增布局
    createLayout(data: Layout): Promise<RestResultResponse<Layout>> {
        return smartDeskHttpClient.post('/layout/add', data).send();
    },

    // 禁用
    disableLayout(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/layout/disable/${code}`).send();
    },

    // 启用
    enableLayout(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/layout/enable/${code}`).send();
    },

    // 批量禁用
    batchDisableLayout(data: any): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/layout/batch/disable`, data).send();
    },

    // 批量启用
    batchEnableLayout(data: any): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/layout/batch/enable`, data).send();
    },

    // 批量删除
    batchDeleteLayout(data: any): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/layout/delete`, data).send();
    },
};
