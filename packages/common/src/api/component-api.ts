import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestPageResultResponse, RestResponse } from '@chances/portal_common_core';
import {
    Component,
    ComponentSearchForm,
    ComponentStyle,
    ComponentStyleSearchForm,
    PaginationParams,
} from '@smartdesk/common/types';

export interface ComponentApi {
    /**
     * 查询组件列表
     *
     * @param prams 查询表单
     * @param pageInfo 分页参数
     */
    getComponents(
        prams: Partial<ComponentSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<Component>>;

    /**
     * 查询组件样式列表
     *
     * @param params 查询组件样式的参数
     * @param pageInfo 分页参数
     * */
    getComponentStyles(
        params: Partial<ComponentStyleSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ComponentStyle>>;

    // 查询组件列表
    findComponentPage(
        params: Partial<ComponentSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<Component>>;

    // 修改组件
    updateComponent(code: string, form: Component): Promise<RestResponse>;

    /**
     * 批量删除组件
     * @param codes
     */
    batchDeleteComponent(codes: string[]): Promise<RestResponse>;

    /**
     * 批量启用组件
     * @param codes
     */
    batchEnableComponent(codes: string[]): Promise<RestResponse>;

    /**
     * 批量禁用组件
     * @param codes
     */
    batchDisableComponent(codes: string[]): Promise<RestResponse>;

    /**
     * 导入组件
     * @param siteCode 网站编码
     * @param file 文件
     */
    importComponent(siteCode: string, file: File): Promise<RestResponse>;

    /**
     * 导出组件
     */
    exportComponent(code: string): Promise<Blob>;

    /**
     * 导出组件
     */
    batchExportComponent(codes: string[]): Promise<Blob>;
}

export const componentApi: ComponentApi = {
    /**
     * 查询组件列表
     *
     * @param prams 查询表单
     * @param pageInfo 分页参数
     */
    getComponents(
        prams: Partial<ComponentSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<Component>> {
        return smartDeskHttpClient.post(`/component/list`).params(pageInfo).data(prams).send();
    },

    /**
     * 查询组件样式列表
     *
     * @param params 查询组件样式的参数
     * @param pageInfo 分页参数
     * */
    getComponentStyles(
        params: Partial<ComponentStyleSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ComponentStyle>> {
        return smartDeskHttpClient.post(`/component/style/list`).params(pageInfo).data(params).send();
    },

    // 条件查询
    findComponentPage(
        params: Partial<ComponentSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<Component>> {
        return smartDeskHttpClient.post('/component/list').data(params).params(pageInfo).send();
    },

    // 修改组件
    updateComponent(code: string, form: Component): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/component/${code}`).data(form).send();
    },

    /**
     * 批量删除组件
     * @param codes
     */
    batchDeleteComponent(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/component/batch/delete`).data(codes).send();
    },

    /**
     * 批量启用组件
     * @param codes
     */
    batchEnableComponent(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/component/batch/enable`).data(codes).send();
    },

    /**
     * 批量禁用组件
     * @param codes
     */
    batchDisableComponent(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/component/batch/disable`).data(codes).send();
    },

    /**
     * 导入组件
     * @param siteCode 网站编码
     * @param file 文件
     */
    importComponent(siteCode: string, file: File): Promise<RestResponse> {
        return smartDeskHttpClient.upload(`/component/upload/${siteCode}`, file).send();
    },

    /**
     * 导出组件
     */
    exportComponent(code: string): Promise<Blob> {
        return smartDeskHttpClient.get(`/component/download/${code}`, { responseType: 'blob' }).send();
    },

    /**
     * 导出组件
     */
    batchExportComponent(codes: string[]): Promise<Blob> {
        return smartDeskHttpClient.post(`/component/download`, codes, { responseType: 'blob' }).send();
    },
};
