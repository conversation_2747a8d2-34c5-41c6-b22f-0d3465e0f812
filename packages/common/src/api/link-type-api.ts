import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestPageResultResponse, RestResponse, RestResultResponse } from '@chances/portal_common_core';
import { LinkType, LinkTypeSearchForm, PaginationParams } from '@smartdesk/common/types';

export interface LinkTypeApi {
    /**
     * 分页获取路由配置
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getLinkTypes(
        params: Partial<LinkTypeSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<LinkType>>;

    /*
     * 新增跳转链接
     * @param Params
     */
    createLinkType(form: LinkType): Promise<RestResultResponse<LinkType>>;

    /*
     * 更新跳转链接
     * @param Params
     */
    updateLinkType(code: string, form: LinkType): Promise<RestResultResponse<LinkType>>;

    /**
     * 删除跳转链接
     * @param codes
     */
    batchDeleteLinkType(codes: Partial<string[]>): Promise<RestResponse>;

    /**
     * 启用跳转链接
     * @param code
     */
    enableLinkType(code: string): Promise<RestResponse>;

    /**
     * 禁用跳转链接
     * @param code
     */
    disableLinkType(code: string): Promise<RestResponse>;

    /**
     * 批量启用跳转链接
     * @param codes
     */
    batchEnableLinkType(codes: string[]): Promise<RestResponse>;

    /**
     * 批量禁用跳转链接
     * @param codes
     */
    batchDisableLinkType(codes: string[]): Promise<RestResponse>;
}

export const linkTypeApi: LinkTypeApi = {
    /**
     * 分页获取路由配置
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getLinkTypes(
        params: Partial<LinkTypeSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<LinkType>> {
        return smartDeskHttpClient.post(`/link_type/list`).data(params).params(pageInfo).send();
    },

    /*
     * 新增跳转链接
     * @param Params
     */
    createLinkType(form: LinkType): Promise<RestResultResponse<LinkType>> {
        return smartDeskHttpClient.post(`/link_type`).data(form).send();
    },

    /*
     * 更新跳转链接
     * @param Params
     */
    updateLinkType(code: string, form: LinkType): Promise<RestResultResponse<LinkType>> {
        return smartDeskHttpClient.post(`/link_type/${code}`).data(form).send();
    },

    /**
     * 批量删除跳转链接
     * @param codes
     */
    batchDeleteLinkType(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/link_type/batch/delete`).data(codes).send();
    },

    /**
     * 启用跳转链接
     * @param code
     */
    enableLinkType(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/link_type/${code}/enable`).send();
    },

    /**
     * 禁用跳转链接
     * @param code
     */
    disableLinkType(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/link_type/${code}/disable`).send();
    },

    /**
     * 批量启用跳转链接
     * @param codes
     */
    batchEnableLinkType(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/link_type/batch/enable`).data(codes).send();
    },

    /**
     * 批量禁用跳转链接
     * @param codes
     */
    batchDisableLinkType(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/link_type/batch/disable`).data(codes).send();
    },
};
