import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestPageResultResponse, RestResponse, RestResultResponse } from '@chances/portal_common_core';
import { Box, BoxCapabilityModel, BoxSearchForm, PaginationParams } from '@smartdesk/common/types';

export interface BoxApi {
    /**
     * 分页获取机顶盒
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getBoxs(params: Partial<BoxSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Box>>;

    /**
     * 获取机顶盒列表
     *
     */
    getBoxList(): Promise<RestPageResultResponse<Box>>;

    /**
     * 设置机顶盒功能
     *
     */
    settingBoxCapability(boxCapabilityModel: BoxCapabilityModel): Promise<RestResponse>;

    /*
     * 新增机顶盒
     * @param Params
     */
    createBox(form: Box): Promise<RestResultResponse<Box>>;

    /*
     * 更新机顶盒
     * @param Params
     */
    updateBox(id: number, form: Box): Promise<RestResultResponse<Box>>;

    /**
     * 删除机顶盒
     * @param code
     */
    deleteBox(code: string): Promise<RestResponse>;

    /**
     * 启用机顶盒
     * @param code
     */
    enableBox(code: string): Promise<RestResponse>;

    /**
     * 禁用机顶盒
     * @param code
     */
    disableBox(code: string): Promise<RestResponse>;

    /**
     * 批量启用机顶盒
     * @param codes
     */
    batchEnableBox(codes: string[]): Promise<RestResponse>;

    /**
     * 批量禁用机顶盒
     * @param codes
     */
    batchDisableBox(codes: string[]): Promise<RestResponse>;

    /**
     * 批量删除机顶盒
     * @param codes
     */
    batchDeleteBox(codes: Partial<string[]>): Promise<RestResponse>;
}

export const boxApi: BoxApi = {
    /**
     * 分页获取机顶盒
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getBoxs(params: Partial<BoxSearchForm>, pageInfo: PaginationParams): Promise<RestPageResultResponse<Box>> {
        return smartDeskHttpClient.post(`/box/page`).data(params).params(pageInfo).send();
    },

    /**
     * 获取机顶盒列表
     *
     */
    getBoxList(): Promise<RestPageResultResponse<Box>> {
        return smartDeskHttpClient.get(`/box/list`).send();
    },
    /**
     * 设置机顶盒功能
     *
     */
    settingBoxCapability(boxCapabilityModel: BoxCapabilityModel): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/box/capability`).data(boxCapabilityModel).send();
    },
    /*
     * 新增机顶盒
     * @param Params
     */
    createBox(form: Box): Promise<RestResultResponse<Box>> {
        return smartDeskHttpClient.post(`/box`).data(form).send();
    },

    /*
     * 更新机顶盒
     * @param Params
     */
    updateBox(id: number, form: Box): Promise<RestResultResponse<Box>> {
        return smartDeskHttpClient.post(`/box/${id}`).data(form).send();
    },

    /**
     * 批量删除机顶盒
     * @param codes
     */
    batchDeleteBox(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/box/batch/delete`).data(codes).send();
    },

    /**
     * 启用机顶盒
     * @param code
     */
    deleteBox(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/box/${code}/delete`).send();
    },

    /**
     * 启用机顶盒
     * @param code
     */
    enableBox(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/box/${code}/enable`).send();
    },

    /**
     * 禁用机顶盒
     * @param code
     */
    disableBox(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/box/${code}/disable`).send();
    },

    /**
     * 批量启用机顶盒
     * @param codes
     */
    batchEnableBox(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/box/batch/enable`).data(codes).send();
    },

    /**
     * 批量禁用机顶盒
     * @param codes
     */
    batchDisableBox(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/box/batch/disable`).data(codes).send();
    },
};
