import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestPageResultResponse, RestResultResponse } from '@chances/portal_common_core';
import { ComponentStyle } from '@smartdesk/common/types';

// 组件相关 API 接口
export interface ComponentStyleApi {
    // 查询组件列表
    findComponentStylePage(params: any, pageInfo: any): Promise<RestPageResultResponse<any>>;

    /*
     * 更新跳转链接
     * @param Params
     */
    updateComponentStyle(code: string, form: ComponentStyle): Promise<RestResultResponse<ComponentStyle>>;
}

export const componentStyleApi: ComponentStyleApi = {
    // 条件查询
    findComponentStylePage(params: any, pageInfo: any): Promise<RestPageResultResponse<any>> {
        return smartDeskHttpClient.post('/component/style/list').data(params).params(pageInfo).send();
    },

    /*
     * 更新跳转链接
     * @param Params
     */
    updateComponentStyle(code: string, form: ComponentStyle): Promise<RestResultResponse<ComponentStyle>> {
        return smartDeskHttpClient.post(`/component/style/${code}`).data(form).send();
    },
};
