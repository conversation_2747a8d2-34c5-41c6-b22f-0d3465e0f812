import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestResultResponse } from '@chances/portal_common_core';
import { PersonalRuleFolderModel } from '@smartdesk/common/types';

// 推荐策略目录APi
export interface PersonalRuleFolderApi {
    // 获取推荐策略目录树
    getPersonalRuleFolderTree(
        isRoot?: boolean,
        parentCode?: string,
        keyword?: string
    ): Promise<RestResultResponse<PersonalRuleFolderModel[]>>;

    // 创建推荐策略目录
    createFolder(form: Partial<PersonalRuleFolderModel>): Promise<RestResultResponse<PersonalRuleFolderModel>>;

    // 删除推荐策略目录
    deleteFolder(code: string): Promise<RestResultResponse<PersonalRuleFolderModel>>;

    // 更新推荐策略目录
    updateFolder(
        code: string,
        form: Partial<PersonalRuleFolderModel>
    ): Promise<RestResultResponse<PersonalRuleFolderModel>>;
}

export const personalRuleFolderApi: PersonalRuleFolderApi = {
    /**
     * 获取推荐策略目录树
     */
    getPersonalRuleFolderTree(
        isRoot?: boolean,
        parentCode?: string,
        keyword?: string
    ): Promise<RestResultResponse<PersonalRuleFolderModel[]>> {
        return smartDeskHttpClient
            .post(`/personal_rule_folder/tree`)
            .params({
                isRoot: isRoot,
                parentCode: parentCode,
                keyword: keyword,
            })
            .send();
    },

    // 创建推荐策略目录
    createFolder(form: Partial<PersonalRuleFolderModel>): Promise<RestResultResponse<PersonalRuleFolderModel>> {
        return smartDeskHttpClient.post(`/personal_rule_folder`).data(form).send();
    },

    // 删除推荐策略目录
    deleteFolder(code: string): Promise<RestResultResponse<PersonalRuleFolderModel>> {
        return smartDeskHttpClient.post(`/personal_rule_folder/delete/${code}`).send();
    },

    // 更新推荐策略目录
    updateFolder(
        code: string,
        form: Partial<PersonalRuleFolderModel>
    ): Promise<RestResultResponse<PersonalRuleFolderModel>> {
        return smartDeskHttpClient.post(`/personal_rule_folder/${code}`).data(form).send();
    },
};
