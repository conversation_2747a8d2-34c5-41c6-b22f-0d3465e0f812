import { smartDeskHttpClient } from '@smartdesk/common/http';
import { LabelValue, RestPageResultResponse, RestResponse, RestResultResponse } from '@chances/portal_common_core';
import { PaginationParams, Site, SiteSearchForm } from '@smartdesk/common/types';

// 网站相关 API 接口
export interface SiteApi {
    // 查询网站列表，并以 label 和 value 的形式展示
    optionsSite(): Promise<RestResultResponse<Array<LabelValue>>>;

    /**
     * 条件查询网站列表
     *
     * @param searchForm 网站查询表单
     * @param pageParam 分页参数
     * @return 网站分页列表
     * */
    findSites(searchForm: Partial<SiteSearchForm>, pageParam: PaginationParams): Promise<RestPageResultResponse<Site>>;

    /**
     * 新增网站
     *
     * @param form 网站表单
     * @return 网站
     * */
    createSite(form: Partial<Site>): Promise<RestResultResponse<Site>>;

    /**
     * 修改网站
     *
     * @param code 网站编码
     * @param form 网站表单
     * @return 网站
     * */
    updateSite(code: string, form: Partial<Site>): Promise<RestResultResponse<Site>>;

    /**
     * 删除网站
     *
     * @param code 网站编码
     * @return 删除结果
     * */
    deleteSite(code: string): Promise<RestResponse>;

    /**
     * 批量删除网站
     *
     * @param codes 网站编码列表
     * @return 批量删除结果
     * */
    batchDeleteSite(codes: string[]): Promise<RestResponse>;

    /**
     * 启用网站
     *
     * @param code 网站编码
     * @return 启用结果
     * */
    enableSite(code: string): Promise<RestResponse>;

    /**
     * 禁用网站
     *
     * @param code 网站编码
     * @return 禁用结果
     * */
    disableSite(code: string): Promise<RestResponse>;

    /**
     * 批量启用网站
     *
     * @param codes 网站编码列表
     * @return 批量启用结果
     * */
    batchEnableSite(codes: string[]): Promise<RestResponse>;

    /**
     * 批量禁用网站
     *
     * @param codes 网站编码列表
     * @return 批量禁用结果
     * */
    batchDisableSite(codes: string[]): Promise<RestResponse>;
}

export const siteApi: SiteApi = {
    // 查询网站列表，并以 label 和 value 的形式展示
    optionsSite(): Promise<RestResultResponse<Array<LabelValue>>> {
        return smartDeskHttpClient.get('/site/options').send();
    },

    /**
     * 条件查询网站列表
     *
     * @param searchForm 网站查询表单
     * @param pageParam 分页参数
     * @return 网站分页列表
     * */
    findSites(searchForm: Partial<SiteSearchForm>, pageParam: PaginationParams): Promise<RestPageResultResponse<Site>> {
        return smartDeskHttpClient.post('/site/list').data(searchForm).params(pageParam).send();
    },

    /**
     * 新增网站
     *
     * @param form 网站表单
     * @return 网站
     * */
    createSite(form: Partial<Site>): Promise<RestResultResponse<Site>> {
        return smartDeskHttpClient.post('/site', form).send();
    },

    /**
     * 修改网站
     *
     * @param code 网站编码
     * @param form 网站表单
     * @return 网站
     * */
    updateSite(code: string, form: Partial<Site>): Promise<RestResultResponse<Site>> {
        return smartDeskHttpClient.post(`/site/${code}`, form).send();
    },

    /**
     * 删除网站
     *
     * @param code 网站编码
     * @return 删除结果
     * */
    deleteSite(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/site/${code}/delete`).send();
    },

    /**
     * 批量删除网站
     *
     * @param codes 网站编码列表
     * @return 批量删除结果
     * */
    batchDeleteSite(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/site/batch/delete`, codes).send();
    },

    /**
     * 启用网站
     *
     * @param code 网站编码
     * @return 启用结果
     * */
    enableSite(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/site/${code}/enable`).send();
    },

    /**
     * 禁用网站
     *
     * @param code 网站编码
     * @return 禁用结果
     * */
    disableSite(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/site/${code}/disable`).send();
    },

    /**
     * 批量启用网站
     *
     * @param codes 网站编码列表
     * @return 批量启用结果
     * */
    batchEnableSite(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/site/batch/enable`, codes).send();
    },

    /**
     * 批量禁用网站
     *
     * @param codes 网站编码列表
     * @return 批量禁用结果
     * */
    batchDisableSite(codes: string[]): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/site/batch/disable`, codes).send();
    },
};
