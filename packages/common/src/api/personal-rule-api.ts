import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestPageResultResponse, RestResponse, RestResultResponse } from '@chances/portal_common_core';
import { PaginationParams, PersonalRule, PersonalRuleSearchForm } from '@smartdesk/common/types';

export interface PersonalRuleApi {
    /**
     * 分页获取推荐策略
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getPersonalRules(
        params: Partial<PersonalRuleSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<PersonalRule>>;

    /**
     * 分页获取推荐策略
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getPersonalRuleList(
        params: Partial<PersonalRuleSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<PersonalRule>>;

    /**
     * 新增推荐策略
     * @param form 推荐策略
     */
    createPersonalRule(form: PersonalRule): Promise<RestResponse>;

    /**
     * 删除推荐策略
     * @param code 编码
     */
    deletePersonalRule(code: string): Promise<RestResponse>;

    /**
     * 启用推荐策略
     * @param code 编码
     */
    enablePersonalRule(code: string): Promise<RestResponse>;

    /**
     * 禁用推荐策略
     * @param code 编码
     */
    disablePersonalRule(code: string): Promise<RestResponse>;

    /**
     * 更新推荐策略
     * @param code 编码
     * @param form 推荐策略
     */
    updatePersonalRule(code: string, form: PersonalRule): Promise<RestResponse>;

    /**
     * 获取同步数据
     * @param code 编码
     */
    getSyncData(code: Partial<string>): Promise<RestResultResponse<any>>;

    /**
     * 更新引用
     * @param code 编码
     * @param data 数据
     */
    updateReference(code: string, data: Record<string, any[]>): Promise<RestResponse>;

    /**
     * 替换引用
     * @param code 编码
     * @param data 数据
     */
    replaceReference(code: string, data: Record<string, any[]>): Promise<RestResponse>;

    /**
     * 获取所有推荐策略
     */
    getAllPersonalRules(): Promise<RestResultResponse<any>>;
}

export const personalRuleApi: PersonalRuleApi = {
    /**
     * 分页获取推荐策略
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getPersonalRules(
        params: Partial<PersonalRuleSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<PersonalRule>> {
        return smartDeskHttpClient.post(`/personal_rule/list`).data(params).params(pageInfo).send();
    },

    /**
     * 分页获取推荐策略
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getPersonalRuleList(
        params: Partial<PersonalRuleSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<PersonalRule>> {
        return smartDeskHttpClient.post(`/personal_rule/list`).data(params).params(pageInfo).send();
    },

    /**
     * 新增推荐策略
     * @param form 推荐策略
     */
    createPersonalRule(form: PersonalRule): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/personal_rule`).data(form).send();
    },

    /**
     * 更新推荐策略
     * @param code 编码
     * @param form 推荐策略
     * @returns
     */
    updatePersonalRule(code: string, form: PersonalRule): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/personal_rule/${code}`).data(form).send();
    },

    /**
     * 删除推荐策略
     * @param code 编码
     */
    deletePersonalRule(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.delete(`/personal_rule/delete/${code}`).send();
    },

    /**
     * 启用推荐策略
     * @param code 编码
     */
    enablePersonalRule(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/personal_rule/enable/${code}`).send();
    },

    /**
     * 禁用推荐策略
     * @param code 编码
     */
    disablePersonalRule(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/personal_rule/disable/${code}`).send();
    },

    /**
     * 获取同步数据
     * @param code 编码
     */
    getSyncData(code: string): Promise<RestResultResponse<any>> {
        return smartDeskHttpClient.get(`/personal_rule/reference/${code}`).send();
    },

    /**
     * 更新引用
     * @param code 编码
     * @param data 数据
     */
    updateReference(code: string, data: Record<string, any[]>): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/personal_rule/update/reference/${code}`).data(data).send();
    },

    /**
     * 替换引用
     * @param code 编码
     * @param data 数据
     */
    replaceReference(code: string, data: Record<string, any[]>): Promise<RestResponse> {
        return smartDeskHttpClient.post(`/personal_rule/replace/reference/${code}`).data(data).send();
    },

    /**
     * 获取所有推荐策略
     */
    getAllPersonalRules(): Promise<RestResultResponse<any>> {
        return smartDeskHttpClient.get(`/personal_rule/all`).send();
    },
};
