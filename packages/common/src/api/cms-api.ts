import { cmsHttpClient } from '@smartdesk/common/http';
import { Enumeration, RestPageResultResponse, RestResultResponse } from '@chances/portal_common_core';
import {
    AlbumVO,
    CastQueryParam,
    CastVO,
    ChannelQueryParam,
    ChannelVO,
    CmsQueryParam,
    ColumnQueryParam,
    ContentData,
    ContentPointQueryParam,
    ContentPointVO,
    ContentQueryParam,
    EnumParam,
    EpgCategory,
    EpgShowFlag,
    IopQueryParam,
    IOPVO,
    LinkQueryParam,
    LinkVO,
    MaterialFolderVO,
    MediaKitVO,
    PackageVO,
    PaginationParams,
    PartnerVO,
    ProgramVO,
    ScheduleQueryParam,
    ScheduleVO,
    SeriesVO,
    ShowFlagQueryParam,
    SubjectQueryParam,
    SubjectVO,
    ThirdPartyDataSourceVO,
} from '@smartdesk/common/types';

export interface CmsApi {
    /**
     * 查询内容
     *
     * @param params 内容查询表单
     */
    searchContent(params: Partial<Map<string, String[]>>): Promise<RestResultResponse<Map<string, ContentData>>>;

    /**
     * 查询楼层内容
     * @param params 内容查询表单
     */
    searchSectionContent(
        params: Partial<
            Map<
                string,
                Array<{
                    dsCode: string;
                    size: number;
                }>
            >
        >
    ): Promise<RestResultResponse<Map<string, ContentData[]>>>;

    /**
     * 查询单剧集
     *
     * @param params 内容查询表单
     * @param pageInfo 分页参数
     */
    searchMovie(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ProgramVO>>;

    /**
     * 查询连续剧子集
     *
     * @param params 内容查询表单
     * @param pageInfo
     */
    searchEpisode(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ProgramVO>>;

    /**
     * 查询系列剧子集
     *
     * @param params 内容查询表单
     * @param pageInfo
     */
    searchEpisode2(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ProgramVO>>;

    /**
     * 查询连续剧
     *
     * @param params 内容查询表单
     * @param pageInfo
     */
    searchSeries(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<SeriesVO>>;

    /**
     * 查询系列剧
     *
     * @param params 内容查询表单
     * @param pageInfo
     */
    searchSeries2(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<SeriesVO>>;

    /**
     * 查询片段
     * @param params 片段查询表单
     * @param pageInfo 分页
     */
    searchPoint(
        params: Partial<ContentPointQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ContentPointVO>>;

    /**
     * 查询外部链接
     *
     * @param params 外部链接查询表单
     * @param pageInfo
     */
    searchLink(params: Partial<LinkQueryParam>, pageInfo: PaginationParams): Promise<RestPageResultResponse<LinkVO>>;

    /**
     * 查询演职员
     *
     * @param params 演职员查询表单
     * @param pageInfo
     */
    searchCast(params: Partial<CastQueryParam>, pageInfo: PaginationParams): Promise<RestPageResultResponse<CastVO>>;

    /**
     * 查询专栏
     *
     * @param params 专栏查询表单
     * @param pageInfo
     */
    searchColumn(
        params: Partial<ColumnQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<AlbumVO>>;

    /**
     * 查询专题
     *
     * @param params 专题查询表单
     * @param pageInfo
     */
    searchSubject(
        params: Partial<SubjectQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<SubjectVO>>;

    /**
     * 查询动态专题
     *
     * @param params 专题查询表单
     * @param pageInfo
     */
    searchActivitySubject(
        params: Partial<SubjectQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<SubjectVO>>;

    /**
     * 查询统一媒资包
     *
     * @param params 统一媒资包查询表单
     * @param pageInfo
     */
    searchMediaKit(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<MediaKitVO>>;

    /**
     * 查询频道
     *
     * @param params 频道查询表单
     * @param pageInfo
     */
    searchChannel(
        params: Partial<ChannelQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ChannelVO>>;

    /**
     * 查询节目单
     *
     * @param params 节目单查询表单
     * @param pageInfo
     */
    searchSchedule(
        params: Partial<ScheduleQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ScheduleVO>>;

    /**
     * 查询栏目树
     *
     * @param isRoot 是否为根结点
     * @param parentCode 父编码
     * @param keyword 关键字
     */
    searchCategory(
        isRoot?: boolean,
        parentCode?: string,
        keyword?: string
    ): Promise<RestResultResponse<Array<EpgCategory>>>;

    /**
     * 查询IOP
     *
     * @param params IOP查询表单
     * @param pageInfo 分页参数
     */
    searchIOP(params: Partial<IopQueryParam>, pageInfo: PaginationParams): Promise<RestPageResultResponse<IOPVO>>;

    /**
     * 查询推荐池
     *
     * @param params 推荐池查询表单
     * @param pageInfo 分页参数
     */
    searchDataSource(
        params: Partial<CmsQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ThirdPartyDataSourceVO>>;

    /**
     * 枚举值
     * @param params  枚举code
     */
    searchEnum(params: EnumParam): Promise<RestResultResponse<Array<Enumeration>>>;

    /**
     * 查询cp|op
     *
     * @param params cp|op
     */
    searchPartner(params: string): Promise<RestResultResponse<Array<PartnerVO>>>;

    /**
     * 查询cp|op树
     *
     * @param params cp|op
     */
    searchPartnerTree(params: string): Promise<RestResultResponse<Array<PartnerVO>>>;

    /**
     * 查询素材
     * @param params 素材分类类型
     */
    searchMaterialFolder(params: string): Promise<RestResultResponse<Array<MaterialFolderVO>>>;

    /**
     * 查询资费包
     */
    searchPackage(): Promise<RestResultResponse<Array<PackageVO>>>;

    /**
     * 查角标
     * */
    searchCorner(
        data: Partial<ShowFlagQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestResultResponse<Array<EpgShowFlag>>>;
}

export const cmsApi: CmsApi = {
    /**
     * 查询内容
     *
     * @param params 内容查询表单
     */
    searchContent(params: Partial<Map<string, String[]>>): Promise<RestResultResponse<Map<string, ContentData>>> {
        return cmsHttpClient.post(`/search/content`).data(params).send();
    },

    /**
     * 查询楼层内容
     *
     * @param params 内容查询表单
     */
    searchSectionContent(
        params: Partial<
            Map<
                string,
                Array<{
                    dsCode: string;
                    size: number;
                }>
            >
        >
    ): Promise<RestResultResponse<Map<string, ContentData[]>>> {
        return cmsHttpClient.post(`/search/fetchSectionContent`).data(params).send();
    },

    /**
     * 查询单剧集
     *
     * @param params 内容查询表单
     * @param pageInfo 分页参数
     */
    searchMovie(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ProgramVO>> {
        return cmsHttpClient.post(`/search/movie`).data(params).params(pageInfo).send();
    },

    /**
     * 查询连续剧子集
     *
     * @param params 内容查询表单
     * @param pageInfo
     */
    searchEpisode(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ProgramVO>> {
        return cmsHttpClient.post(`/search/episode`).data(params).params(pageInfo).send();
    },

    /**
     * 查询系列剧子集
     *
     * @param params 内容查询表单
     * @param pageInfo
     */
    searchEpisode2(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ProgramVO>> {
        return cmsHttpClient.post(`/search/episode2`).data(params).params(pageInfo).send();
    },

    /**
     * 查询连续剧
     *
     * @param params 内容查询表单
     * @param pageInfo
     */
    searchSeries(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<SeriesVO>> {
        return cmsHttpClient.post(`/search/series`).data(params).params(pageInfo).send();
    },

    /**
     * 查询系列剧
     *
     * @param params 内容查询表单
     * @param pageInfo
     */
    searchSeries2(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<SeriesVO>> {
        return cmsHttpClient.post(`/search/series2`).data(params).params(pageInfo).send();
    },

    /**
     * 查询片段
     * @param params 片段查询表单
     * @param pageInfo 分页
     */
    searchPoint(
        params: Partial<ContentPointQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ContentPointVO>> {
        return cmsHttpClient.post(`/search/point`).data(params).params(pageInfo).send();
    },
    /**
     * 查询外部链接
     *
     * @param params 外部链接查询表单
     * @param pageInfo
     */
    searchLink(params: Partial<LinkQueryParam>, pageInfo: PaginationParams): Promise<RestPageResultResponse<LinkVO>> {
        return cmsHttpClient.post(`/search/link`).data(params).params(pageInfo).send();
    },

    /**
     * 查询演职员
     *
     * @param params 演职员查询表单
     * @param pageInfo
     */
    searchCast(params: Partial<CastQueryParam>, pageInfo: PaginationParams): Promise<RestPageResultResponse<CastVO>> {
        return cmsHttpClient.post(`/search/cast`).data(params).params(pageInfo).send();
    },

    /**
     * 查询专栏
     *
     * @param params 专栏查询表单
     * @param pageInfo
     */
    searchColumn(
        params: Partial<ColumnQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<AlbumVO>> {
        return cmsHttpClient.post(`/search/column`).data(params).params(pageInfo).send();
    },

    /**
     * 查询专题
     *
     * @param params 专题查询表单
     * @param pageInfo
     */
    searchSubject(
        params: Partial<SubjectQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<SubjectVO>> {
        return cmsHttpClient.post(`/search/subject`).data(params).params(pageInfo).send();
    },

    /**
     * 查询动态专题
     *
     * @param params 专题查询表单
     * @param pageInfo
     */
    searchActivitySubject(
        params: Partial<SubjectQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<SubjectVO>> {
        return cmsHttpClient.post(`/search/activity_subject`).data(params).params(pageInfo).send();
    },

    /**
     * 查询统一媒资包
     *
     * @param params 统一媒资包查询表单
     * @param pageInfo
     */
    searchMediaKit(
        params: Partial<ContentQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<MediaKitVO>> {
        return cmsHttpClient.post(`/search/mediakit`).data(params).params(pageInfo).send();
    },

    /**
     * 查询频道
     *
     * @param params 频道查询表单
     * @param pageInfo
     */
    searchChannel(
        params: Partial<ChannelQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ChannelVO>> {
        return cmsHttpClient.post(`/search/channel`).data(params).params(pageInfo).send();
    },

    /**
     * 查询节目单
     *
     * @param params 节目单查询表单
     * @param pageInfo
     */
    searchSchedule(
        params: Partial<ScheduleQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ScheduleVO>> {
        return cmsHttpClient.post(`/search/schedule`).data(params).params(pageInfo).send();
    },

    /**
     * 查询栏目树
     *
     * @param isRoot 是否为根结点
     * @param parentCode 父编码
     * @param keyword 关键字
     */
    searchCategory(
        isRoot?: boolean,
        parentCode?: string,
        keyword?: string
    ): Promise<RestResultResponse<Array<EpgCategory>>> {
        return cmsHttpClient
            .post(`/search/category/tree`)
            .params({
                isRoot: isRoot,
                parentCode: parentCode,
                keyword: keyword,
            })
            .send();
    },

    /**
     * 查询IOP
     *
     * @param params IOP查询表单
     * @param pageInfo 分页参数
     */
    searchIOP(params: Partial<IopQueryParam>, pageInfo: PaginationParams): Promise<RestPageResultResponse<IOPVO>> {
        return cmsHttpClient.post(`/search/iop`).data(params).params(pageInfo).send();
    },

    /**
     * 查询推荐池
     *
     * @param params 推荐池查询表单
     * @param pageInfo 分页参数
     */
    searchDataSource(
        params: Partial<CmsQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<ThirdPartyDataSourceVO>> {
        return cmsHttpClient.post(`/search/thirdParty`).data(params).params(pageInfo).send();
    },

    /**
     * 枚举值
     * @param params  枚举code
     */
    searchEnum(params: EnumParam): Promise<RestResultResponse<Array<Enumeration>>> {
        return cmsHttpClient.post(`/search/enum`).data(params).send();
    },

    /**
     * 查询cp|op
     *
     * @param params cp|op
     */
    searchPartner(params: string): Promise<RestResultResponse<Array<PartnerVO>>> {
        return cmsHttpClient.get(`/search/partner/${params}`).send();
    },

    searchPartnerTree(params: string): Promise<RestResultResponse<Array<PartnerVO>>> {
        return cmsHttpClient.get(`/search/partner/tree/${params}`).send();
    },

    /**
     * 查询素材
     * @param params 素材分类类型
     */
    searchMaterialFolder(params: string): Promise<RestResultResponse<Array<MaterialFolderVO>>> {
        return cmsHttpClient.get(`/search/folder/${params}`).send();
    },

    /**
     * 查询资费包
     * @returns
     */
    searchPackage(): Promise<RestResultResponse<Array<PackageVO>>> {
        return cmsHttpClient.get('/search/package').send();
    },

    /**
     * 查角标
     * */
    searchCorner(
        data: Partial<ShowFlagQueryParam>,
        pageInfo: PaginationParams
    ): Promise<RestResultResponse<Array<EpgShowFlag>>> {
        return cmsHttpClient.post('/search/showFlag').data(data).params(pageInfo).send();
    },
};
