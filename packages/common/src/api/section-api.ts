import { smartDeskHttpClient } from '@smartdesk/common/http';
import { Layout, PaginationParams, Section, SectionLayoutFile, SectionSearchForm } from '@smartdesk/common/types';
import { RestPageResultResponse, RestResponse, RestResultResponse } from '@chances/portal_common_core';

// 楼层定义 API
export interface SectionApi {
    /**
     * 根据编码获取楼层定义
     *
     * @param code 楼层定义编码
     * */
    getSectionByCode(code: string): Promise<RestResultResponse<Section>>;

    /**
     * 分页获取楼层定义列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getSections(
        params: Partial<SectionSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<Section>>;

    /**
     * 修改楼层定义信息
     *
     * @param code 楼层定义 code
     * @param section 楼层定义
     * */
    updateSection(code: string, section: Section): Promise<RestResultResponse<Section>>;

    /**
     * 复制楼层定义信息
     *
     * @param section 楼层定义
     * */
    copySection(section: Section): Promise<RestResultResponse<Section>>;

    /**
     * 新增、修改楼层定义布局
     *
     * @param sectionCode 楼层定义编码
     * @param layout 楼层定义布局
     * */
    updateSectionLayout(sectionCode: string, layout: SectionLayoutFile): Promise<RestResultResponse<Section>>;

    /**
     * 楼层定义另存为布局
     *
     * @param sectionCode 楼层定义编码
     * @param sectionLayoutFile 楼层定义布局文件
     * @param layoutForm 布局表单
     * */
    saveSectionAsLayout(
        sectionCode: string,
        sectionLayoutFile: SectionLayoutFile,
        layoutForm: Partial<Layout>
    ): Promise<RestResultResponse<Layout>>;

    // 新增
    addSection(data: any): Promise<RestResultResponse<any>>;

    // 禁用
    disableSection(code: string): Promise<RestResponse>;

    // 启用
    enableSection(code: string): Promise<RestResponse>;

    // 批量禁用
    batchDisableSection(data: any): Promise<RestResponse>;

    // 批量启用
    batchEnableSection(data: any): Promise<RestResponse>;

    // 批量删除
    deleteSections(codes: any): Promise<RestResponse>;

    // 分页查询
    findLayoutPage(params: any, pageInfo: PaginationParams): Promise<RestPageResultResponse<Array<any>>>;

    // 上传楼层定义包
    uploadSectionPackage(file: any, siteCode: string): Promise<RestResponse>;

    // 导出楼层定义包
    exportSectionPackage(siteCode: string, codes: string[]): Promise<any>;
}

export const sectionApi: SectionApi = {
    /**
     * 根据编码获取楼层定义
     *
     * @param code 楼层定义编码
     * */
    getSectionByCode(code: string): Promise<RestResultResponse<Section>> {
        return smartDeskHttpClient.get(`/section`).params({ code }).send();
    },

    /**
     * 分页获取楼层定义列表
     *
     * @param params 请求参数
     * @param pageInfo 分页参数
     */
    getSections(
        params: Partial<SectionSearchForm>,
        pageInfo: PaginationParams
    ): Promise<RestPageResultResponse<Section>> {
        return smartDeskHttpClient.post(`/section/list`).data(params).params(pageInfo).send();
    },

    /**
     * 修改楼层定义信息
     *
     * @param code 楼层定义编码
     * @param section 楼层定义
     * */
    updateSection(code: string, section: Section): Promise<RestResultResponse<Section>> {
        return smartDeskHttpClient.post(`/section/${code}`, section).send();
    },

    /**
     * 复制楼层定义信息
     *
     * @param section 楼层定义
     * */
    copySection(section: Section): Promise<RestResultResponse<Section>> {
        return smartDeskHttpClient.post(`/section/copy`, section).send();
    },

    /**
     * 新增、修改楼层定义布局
     *
     * @param sectionCode 楼层定义编码
     * @param layout 楼层定义布局
     * */
    updateSectionLayout(sectionCode: string, layout: SectionLayoutFile): Promise<RestResultResponse<Section>> {
        return smartDeskHttpClient.post(`/section/${sectionCode}/layout`, layout).send();
    },

    /**
     * 楼层定义另存为布局
     *
     * @param sectionCode 楼层定义编码
     * @param sectionLayoutFile 楼层定义布局文件
     * @param layoutForm 布局表单
     * */
    saveSectionAsLayout(
        sectionCode: string,
        sectionLayoutFile: SectionLayoutFile,
        layoutForm: Partial<Layout>
    ): Promise<RestResultResponse<Layout>> {
        return smartDeskHttpClient
            .post(`/section/${sectionCode}/save_as_layout`, sectionLayoutFile)
            .params(layoutForm)
            .send();
    },

    // 新增
    addSection(data: any): Promise<RestResultResponse<any>> {
        return smartDeskHttpClient.post('/section', data).send();
    },

    // 批量删除
    deleteSections(codes: any): Promise<RestResponse> {
        return smartDeskHttpClient.post('/section/delete', codes).send();
    },

    // 条件查询
    findLayoutPage(params: any, pageInfo: PaginationParams): Promise<RestPageResultResponse<Array<any>>> {
        return smartDeskHttpClient.post('/section/list').data(params).params(pageInfo).send();
    },

    // 禁用
    disableSection(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/section/disable/${code}`).send();
    },

    // 启用
    enableSection(code: string): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/section/enable/${code}`).send();
    },

    // 批量禁用
    batchDisableSection(data: any): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/section/batch/disable`, data).send();
    },

    // 批量启用
    batchEnableSection(data: any): Promise<RestResponse> {
        return smartDeskHttpClient.put(`/section/batch/enable`, data).send();
    },

    // 上传楼层定义包
    uploadSectionPackage(file: any, siteCode: string): Promise<RestResponse> {
        return smartDeskHttpClient.upload(`/section/import`, file).params({ siteCode }).send();
    },

    // 导出楼层定义包
    exportSectionPackage(siteCode: string, codes: string[]): Promise<any> {
        return smartDeskHttpClient.post(`/section/export`, codes).params({ siteCode }).responseType('blob').send();
    },
};
