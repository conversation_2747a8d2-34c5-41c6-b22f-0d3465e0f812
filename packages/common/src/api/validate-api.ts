import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestResultResponse } from '@chances/portal_common_core';
import { ValidateCodeForm, ValidateNameForm } from '@smartdesk/common/types';

// 校验相关 API 接口
export interface ValidateApi {
    /**
     * 校验名称是否重复
     *
     * @param form 校验名称表单
     * @return 校验结果
     * */
    validateNameDuplicate(form: Partial<ValidateNameForm>): Promise<RestResultResponse<Boolean>>;

    /**
     * 校验编码是否重复
     *
     * @param form 校验编码表单
     * @return 校验结果
     * */
    validateCodeDuplicate(form: Partial<ValidateCodeForm>): Promise<RestResultResponse<Boolean>>;
}

export const validateApi: ValidateApi = {
    /**
     * 校验名称是否重复
     *
     * @param form 校验名称表单
     * @return 校验结果
     * */
    validateNameDuplicate(form: Partial<ValidateNameForm>): Promise<RestResultResponse<Boolean>> {
        return smartDeskHttpClient.post(`/validate/name`).data(form).send();
    },

    /**
     * 校验编码是否重复
     *
     * @param form 校验编码表单
     * @return 校验结果
     * */
    validateCodeDuplicate(form: Partial<ValidateCodeForm>): Promise<RestResultResponse<Boolean>> {
        return smartDeskHttpClient.post(`/validate/code`).data(form).send();
    },
};
