import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestResultResponse } from '@chances/portal_common_core';

export interface StorageApi {
    /**
     * 上传文件
     *
     * @param file 文件
     */
    uploadFile(file: File): Promise<RestResultResponse<string>>;
}

export const storageApi: StorageApi = {
    /**
     * 上传文件
     *
     * @param file 文件
     */
    uploadFile(file: File): Promise<RestResultResponse<string>> {
        return smartDeskHttpClient.upload(`/storage/upload`, file).send();
    },
};
