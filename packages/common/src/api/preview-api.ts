import { smartDeskHttpClient } from '@smartdesk/common/http';
import { RestResultResponse } from '@chances/portal_common_core';
import { Servers } from '@smartdesk/common/types';

export interface PreviewApi {
    // 获取预览地址
    getPreviewUrl(): Promise<RestResultResponse<String>>;

    // 获取预览服务器配置
    getServerConfig(): Promise<RestResultResponse<Servers[]>>;
}

export const previewApi: PreviewApi = {
    // 获取预览地址
    getPreviewUrl(): Promise<RestResultResponse<String>> {
        return smartDeskHttpClient.get('/preview').send();
    },

    // 获取预览服务器配置
    getServerConfig(): Promise<RestResultResponse<Servers[]>> {
        return smartDeskHttpClient.get('/server_config').send();
    },
};
