// 页面楼层数据
export type PageSectionData = {
    // 页面楼层编码
    code: string;

    // 坑位数据列表
    cells: CellData[];
};

// 坑位数据
export type CellData = {
    // 坑位编码
    code: string;

    // 坑位名称
    name: string;

    // 坑位元素数据列表
    items: CellItemData[];
};

// 坑位元素数据
export type CellItemData = {
    // 显示标题
    itemTitle?: string;

    // 显示副标题
    itemSubTitle?: string;

    // 对象类型
    itemType?: string;

    // 对象编码
    itemCode?: string;

    // 显示图标
    itemIcons?: Record<string, any>;

    // 跳转类型
    dataLinkType?: string;

    // 对象接口地址
    dataLink?: string;

    // 角标
    cornerMarks?:  any[];

    // 频道编码
    channelCode?: string;

    // 节目单开始时间
    startTime?: string;

    // 节目单结束时间
    endTime?: string;

    // 内容详细数据
    content?: ContentData;

    // 导航详细数据
    nav?: NavData;
};

// 内容数据
export type ContentData = {
    // 标题
    title?: string;
    // 副标题
    subTitle?: string;
    // 内容编码
    code?: string;
    // 评分
    score?: string;
    // 角标
    cornerMarks?: Record<string, any>;
    // 发行国家
    country?: string;
    // 发行年份
    year?: string;
    // 演职员
    actors?: string;
    // 导演
    directors?: string;
    // 内容类型
    type?: string;
    // 基础类型
    baseTags?: string;
    // 运营标签
    opTags?: string;
    // 播放时长
    runTime?: string;
    // 最新集数
    episodeNum?: string;
    // 总集数
    totalCount?: string;
    // 最新期数
    issueNum?: string;
    // 一句话看点
    viewPoint?: string;
    // 内容海报
    pictures?: Record<string, any>;
};

// 导航数据
export type NavData = {
    // 导航编码
    code?: string;
    // 所属页面编码
    ownerPageCode?: string;
    // 目标页面编码
    pageCode?: string;
    // 图标
    icons?: Record<string, any>;
    // 规则
    rules?: Record<string, any>;
    // 排序
    orderNo?: number;
    // 标题
    title?: string;
};
