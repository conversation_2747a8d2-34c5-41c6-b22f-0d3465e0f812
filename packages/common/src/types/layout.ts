// 桌面的布局文件
export type DesktopLayoutFile = PageLayoutFile;

// 页面的布局文件
export interface PageLayoutFile extends BaseLayoutFile {
    // 楼层定义列表
    sections: SectionLayoutFile[];
}

// 页面楼层的布局文件
export type PageSectionLayoutFile = SectionLayoutFile;

// 坑位的布局文件
export interface CellLayoutFile extends BaseLayoutFile {}

// 楼层定义的布局文件
export type SectionLayoutFile = SectionLayoutLayoutFile;

// 楼层布局的布局文件
export interface SectionLayoutLayoutFile extends BaseLayoutFile {
    // 坑位列表
    cells: CellLayoutFile[];
}

// 组件的布局文件
export type ComponentLayoutFile = Record<string, any>;

// 组件样式的布局文件
export type ComponentStyleLayoutFile = Record<string, any>;

// 基础布局文件
export interface BaseLayoutFile {
    // 编码
    code: string;

    // 名称
    name: string;

    // 组件类型
    component: string;

    // 组件样式编码
    componentStyleCode: string;

    // 布局文件
    layout: LayoutFile;

    // 布局文件版本
    layoutVersion: number;
}

// 布局文件
export interface LayoutFile {
    // 位置
    rect: Rect;

    // 属性：存放组件样式
    props: Record<string, any>;

    // 内边距：设计时使用，限制楼层内部边距
    padding?: Padding;

    // 内部元素间距：设计时使用，限制坑位之间间距
    gap?: number;

    // 设计模式：分为固定模式、滚动模式，固定模式下不可水平滚动，坑位不可超出楼层边界
    mode?: DesignMode;

    // 标准尺寸：滚动模式下使用，坑位的标准尺寸
    standardSize?: StandardSize;
}

// 位置
export interface Rect {
    // y 轴距离
    top: number;

    // x 轴距离
    left: number;

    // 宽度
    width: number;

    // 高度
    height: number;
}

// 内边距
export interface Padding {
    // 上边距
    top: number;

    // 左边距
    left: number;

    // 右边距
    right: number;

    // 下边距
    bottom: number;
}

// 设计模式
export enum DesignMode {
    // 固定模式 - 坑位限制在楼层内
    FIXED = 'fixed',
    // 滚动模式 - 坑位可水平扩展
    SCROLLABLE = 'scrollable',
}

// 标准尺寸
export interface StandardSize {
    // 标准宽度
    width: number;

    // 标准高度
    height: number;
}
