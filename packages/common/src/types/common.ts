// 分页信息
export interface PageInfo {
    // 页码
    page: number;

    // 页大小
    size: number;

    // 页大小数组
    sizeArray: number[];

    // 总数
    totalElements: number;
}

// 分页参数
export interface PaginationParams {
    // 页码
    page?: number;

    // 每页数量
    size?: number;

    // 是否分页
    paged?: boolean;

    // 排序规则，格式：字段1,asc;字段2,desc
    sort?: string;
}

export interface CmsPageInfo {
    total: number;
    curPage: number;
    pageCount: number;
    pageSize: number;
}

export interface RestResultSetResponse<T> {
    status: number;
    msg: string;
    resultSet: T;
    pageInfo: CmsPageInfo;
}

// CMS 基础查询参数
export interface CmsQueryParam {
    // 关键字
    keyword: string;
}
