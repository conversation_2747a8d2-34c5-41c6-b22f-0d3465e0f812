// 专栏
export interface AlbumVO {
    // 专栏id
    id: number;

    // 专栏编码
    code: string;

    // 专栏标题
    title: string;

    // 专栏副标题
    subTitle: string;

    // 海报图
    poster: string;

    // 横图
    still: string;

    // 图标
    icon: string;

    // 背景图
    backgroundImg: string;

    // TOP 图
    topImg: string;

    // 描述
    description: string;

    // 专栏类型：0 普通、1 同系列
    type: number;

    // 排序规则 json
    orderRuleJson: string;
}

// 基础内容
export interface BaseContentVO {
    // 内容id
    id: number;

    // 内容编码
    code: string;

    // 内容名称
    name: string;

    // 显示标题
    title: string;

    // 副标题
    subTitle: string;

    // 提供商
    cpCode: string;

    // 可用状态
    enableStatus: number;

    // 发行国家
    area: string;

    // 发行年份
    year: string;

    // 发行公司
    studio: string;

    // 制片
    producers: string;

    // 演员
    actors: string;

    // 导演
    director: string;

    // 编剧
    writer: string;

    // 内容简介
    description: string;

    // 内容看点
    viewPoints: string;

    // 评分
    score: string;

    // 配音
    vodDub: string;

    // 字幕
    vodCaption: string;

    // 关键字
    keyWord: string;

    // 备注
    remark: string;

    // 预留字段1
    reserve1: string;

    // 预留字段2
    reserve2: string;

    // 预留字段3
    reserve3: string;

    // 预留字段4
    reserve4: string;

    // 预留字段5
    reserve5: string;

    // 图标URL
    icon: string;

    // 横图
    still: string;

    // 海报
    poster: string;

    // 显示时长
    displayRunTime: string;

    // 实际时长
    runTime: string;

    // 分类
    mainFolder: string;

    // 子分类
    subFolder: string;

    // 高清标识
    hdType: number;

    // 预览编码
    previewCode: string;

    // 关联内容编码
    relCode: string;

    // 外部系统编码
    externalCode: string;

    // 主持人列表
    comperes: string;

    // 运营图片1
    opimg1: string;

    // 运营图片2
    opimg2: string;

    // 业务域
    bizDomain: string;

    // 节目类型编码
    typeCode: string;

    // 基础角标
    baseCornerMark: string;

    // 运营角标
    opCornerMark: string;

    // 免费播放时长
    freePlayTime: number;

    // 编目状态
    catalogStatus: number;

    // CDN注入状态
    cdnStatus: number;

    // 播放日期
    playDate: string;

    // 影片分级
    rating: string;

    // 正片开始时间
    beginSeconds: string;

    // 正片结束时间
    endSeconds: number;

    // 生效时间
    validTime: string;

    // 失效时间
    expireTime: string;

    // 建议价格
    suggestedPrice: string;

    // 背景图
    background: string;

    // 收费模式
    charge: string;

    // 内容来源
    comeFrom: string;

    // 封面ID
    coverId: string;

    // 扩展数据
    extData: string;

    // 支付状态
    payStatus: number;

    // 演员编码列表
    actorCodes: string;

    // 安全标签
    securityTag: number;

    // 安全标签详情
    securityTagDetail: string;

    // VIP类型
    vipType: number;

    // 资费角标
    payCornerMark: string;

    // 渠道业务标识
    csbs: string;

    // 特殊角标
    specialCornerMark: string;

    // 播放指数
    playRate: number;

    // 打点信息
    points: string;

    // 竖图
    vImg: string;

    // 横图
    hImg: string;
}

// 演员
export interface CastVO {
    // 演员 id
    id: number;

    // 演员编码
    code: string;

    // 性别
    sex: number;

    // 演员中文名
    name: string;

    // 演员英文名
    englishName: string;

    // 姓
    firstName: string;

    // 中间名
    middleName: string;

    // 名
    lastName: string;

    // 曾用名
    usedName: string;

    // 查询名册
    personSearchName: string;

    // 生日
    birthday: string;

    // 籍贯
    hometown: string;

    // 教育程度
    education: string;

    // 身高
    height: string;

    // 体重
    weight: string;

    // 血型
    bloodGroup: string;

    // 婚姻状态
    marriage: string;

    // 爱好
    favorite: string;

    // 主页
    webpage: string;

    // 结果
    result: string;

    // 可用状态
    enableStatus: number;

    // 创建时间
    createTime: string;

    // 更新时间
    updateTime: string;

    // 出生地
    birthPlace: string;

    // 职业
    profession: string;

    // 背景图
    background: string;

    // 产品
    products: string;

    // 关联演员
    relatedActor: string;

    // 描述信息
    description: string;

    // 头像
    headIcon: string;

    // 横图
    horizontal: string;

    // 错误描述
    errorDescription: string;

    // 审核状态
    auditStatus: number;

    // 类型
    type: string;

    // 发布时间
    publishDate: string;

    // 创建人 ID
    createrId: string;

    // 更新人 ID
    updaterId: string;

    // 状态
    status: number;
}

// 直播频道
export interface ChannelVO {
    // 直播频道 ID
    id: number;

    // 预留字段6
    reserve6: string;

    // 播放地址
    playUrl: string;

    // 直播频道编码
    liveCode: string;

    // 频道号
    channelNum: string;

    // 频道logo
    channelLogo: string;

    // 产品编码
    productCode: string;

    // 城市
    city: string;

    // 可回看
    lookbackAvailable: number;

    // 可时移
    timeshiftAvailable: number;

    // 用户分组
    userGroups: string;

    // 机顶盒类型
    stbTypes: string;

    // 频道角标
    channelCornerMark: number;

    // 回看天数
    lookbackDays: number;

    // 内容编码
    code: string;

    // 预留字段1
    reserve1: string;

    // 预留字段2
    reserve2: string;

    // 预留字段3
    reserve3: string;

    // 预留字段4
    reserve4: string;

    // 预留字段5
    reserve5: string;

    // 显示标题
    title: string;

    // 显示标题
    titleBrief: string;

    // 提供商
    provider: string;

    // 提供商 ID
    providerId: string;

    // 入库日期
    creationDate: string;

    // 服务名称
    serviceName: string;

    // 外部编码
    externalCode: string;

    // 运营图1
    opimg1: string;

    // 运营图2
    opimg2: string;

    // 业务域
    bizDomain: string;

    // 地区
    area: string;

    // offeringId
    offeringId: string;

    // 服务类型
    serviceType: string;

    // 服务编码
    serviceCode: string;

    // 可用状态
    enableStatus: number;

    // 支付状态
    payStatus: number;
}

// IOP
export interface IOPVO {
    // 内容 ID
    id: number;

    // 可用状态
    enableStatus: number;

    // 标题
    title: string;

    // 内容编码
    code: string;

    // 内容类型
    type: string;

    // 内容信息
    content: string;

    // 图片
    image: string;

    // 跳转链接
    url: string;

    // url 类型
    urlType: string;

    // 外部编码
    externalCode: string;

    // 时间间隔
    intervals: number;

    // 展示时间
    displayTime: number;

    // 顶部距离
    top: number;

    // 左边距离
    left: number;

    // 宽度
    width: number;

    // 高度
    height: number;
}

// 链接
export interface LinkVO {
    // 外链 ID
    id: number;

    // 创建时间
    createTime: string;

    // 编码
    code: string;

    // 内容类型
    contentType: string;

    // 描述信息
    description: string;

    // 名称
    name: string;

    // 副标题
    subTitle: string;

    // 标题
    title: string;

    // 高清标识
    hdType: string;

    // 链接类型
    linkType: string;

    // 外部链接
    url: string;

    // 图标
    icon: string;

    // 海报、竖图
    poster: string;

    // 剧照、横图
    still: string;

    //类型
    type: string;
}

// 媒资包
export interface MediaKitVO extends BaseContentVO {
    // 类型
    type: string;
    // 连续剧总集数
    episodeNumber: number;

    // 搜索名称
    searchName: string;

    // displayRunTime
    displayRunTime: string;

    // 播放频道
    playChannel: string;

    // epgShowColumn
    epgShowColumn: string;

    // 当前更新至多少期
    maxIssueNo: string;

    // 当前更新至多少集
    maxEpisodeIndex: number;

    // 所属区县
    county: string;

    // 跟播信息
    followInfo: string;

    // 免费播放开始时间
    freePlayTimeBegin: number;

    // 免费播放结束时间
    freePlayTimeEnd: number;

    // 媒资包类型
    mediaKitType: number;

    // 组合方式
    showType: string;

    // 全部图片
    totalPics: string;
}

// 节目
export interface ProgramVO extends BaseContentVO {
    // 热度
    heatValue: number;

    // 最大播出时长
    maximumViewingLength: string;

    // 产品标识
    product: string;

    // 院线信息
    courtyard: string;

    // 类型
    type: string;

    // 搜索名称
    searchName: string;

    // 标题类型
    titleType: string;

    // 播放频道
    playChannel: string;

    // 问题号
    issueNo: string;

    // 免费播放开始时间
    freePlayTimeBegin: number;

    // 免费播放结束时间
    freePlayTimeEnd: number;
}

// 电影
export interface MovieVO extends ProgramVO {
    // 类型
    programType: string;

    // 是否绑定媒资包
    relKitFlag: boolean;
}

// 节目单
export interface ScheduleVO {
    // 节目单id
    id: number;

    // 频道id
    channelId: number;

    // 频道code
    channelCode: string;

    // 开始时间（ISO 8601格式）
    beginTime: string;

    // 结束时间（ISO 8601格式）
    endTime: string;

    // UNIX时间戳开始时间（毫秒）
    unixBeginTime: number;

    // UNIX时间戳结束时间（毫秒）
    unixEndTime: number;

    // 节目ID
    programId: string;

    // 节目名称
    programName: string;

    // 剧集序号
    episodeIndex: string;

    // 预览信息
    preview: string;

    // 图片路径1
    img1Path: string;

    // 图片路径2
    img2Path: string;

    // 图片路径3
    img3Path: string;

    // 语言版本
    language: string;

    // 版权限制（0-无限制 1-受限）
    limitedCopyright: number;

    // 引流频道编码
    drainageChannelCode: string;

    // 版权限制提示
    copyrightLimitedTip: string;

    // 频道显示名称
    channelName: string;

    // 节目单名称
    name: string;

    // 节目单编码
    code: string;

    // 内容code
    contentCode: string;

    // 外部code
    externalCode: string;

    // 主文件夹路径
    mainFolder: string;

    // 子文件夹路径
    subFolder: string;

    // 标签列表（逗号分隔）
    tags: string;

    // 海报URL
    poster: string;

    // 图标URL
    icon: string;

    // 业务域标识
    bizDomain: string;

    // 描述
    description: string;

    // 导演列表（逗号分隔）
    directors: string;

    // 演员列表（逗号分隔）
    actors: string;

    // 地区
    area: string;

    // 类型
    type: string;

    // 可用状态（0-禁用 1-启用）
    enableStatus: number;

    // 短说明
    summaryShort: string;

    // 标题
    title: string;

    // 副标题
    titleBrief: string;

    // 首映年份（YYYY格式）
    year: string;

    // 节目类型
    genre: string;

    // 首映信息
    premiere: string;

    // 图片1原始路径
    img1Source: string;

    // 图片2原始路径
    img2Source: string;

    // 图片3原始路径
    img3Source: string;

    // 详细内容
    content: string;

    // 简要内容
    briefContent: string;

    // 副标题
    subTitle: string;

    // 开始时间（与beginTime语义重复）
    startTime: string;

    // 发布日期（ISO 8601格式）
    publishDate: string;

    // 审核状态（需补充状态码）
    auditStatus: number;

    // 预留字段1
    reserve1: string;

    // 组织机构ID
    orgId: number;

    // 原始编码
    originCode: string;

    // 节目单标题
    scheduleTitle: string;

    // 外部系统ID
    externalId: number;

    // 创建时间（ISO 8601格式）
    createTime: string;

    // 更新时间（ISO 8601格式）
    updateTime: string;

    // 创建者ID
    createrId: string;

    // 更新者ID
    updaterId: string;

    // 状态（需补充状态码）
    status: number;
}

// 连续剧
export interface SeriesVO extends BaseContentVO {
    // 热度值
    heatValue: number;

    // 是否限免
    limitedTimeFree: boolean;

    // 连续剧总集数
    episodeNumber: number;

    // 连续剧类型（如：周播剧/日更剧等）
    seriesType: string;

    // EPG展示栏目
    epgShowColumn: string;

    // 最大剧集序号
    maxEpisodeIndex: number;

    // 最新发行日期（ISO 8601格式）
    maxIssueNo: string;

    // 追剧信息（JSON格式字符串）
    followInfo: string;
}

// 专题
export interface SubjectVO {
    // 专题id
    id: number;

    // 专题名称
    title: string;

    // 专题编码
    code: string;

    // 专题类型（如：精选/专题/活动）
    subjectType: string;

    // 图标URL
    icon: string;

    // 剧照URL
    still: string;

    // 海报URL
    poster: string;

    // 关联首页ID
    mainPageId: number;

    // 分辨率（格式：长,宽，示例："1280,720"）
    screenType: string;

    // 运营角标文字/图标
    opCornerMark: string;

    // 频道业务标识（CS/BS）
    csbs: string;
}

// 三方数据源
export interface ThirdPartyDataSourceVO {
    // 数据源唯一标识
    id: number;

    // 关联栏目编码
    categoryCode: string;

    // 数据源编码
    code: string;

    // 数据源名称
    name: string;

    // 所属项目名称
    projectName: string;

    // 栏目区块ID
    sectionId: string;

    // 应用场景ID
    senceId: string;

    // 数据源类型（0-推荐 1-人工配置 2-规则配置）
    type: number;

    // 规则配置（JSON格式字符串）
    ruleJson: string;

    // 自定义栏目编码
    customerCategoryCode: string;

    // 父级推荐池ID
    parentId: number;

    // 排序序号
    orderNo: number;

    // 生效区域（逗号分隔的地区编码）
    areas: string;

    // 用户分组（逗号分隔的用户组ID）
    userGroups: string;

    // 设备类型白名单（逗号分隔的设备型号）
    stbTypes: string;

    // EPG用户分群标识
    epgGroups: string;

    // 区县编码过滤（逗号分隔的行政区划代码）
    citys: string;

    // 盒子账号单双号限制（odd:单号 even:双号 null/空:不限制）
    stbOddEven: string | null;

    // APK版本范围（语义化版本规范）
    apkVersions: string;

    // 会员身份过滤（逗号分隔的会员等级）
    membership: string;
}

// 栏目
export interface EpgCategory {
    // 栏目唯一标识
    id: number;

    // 栏目名称（展示用）
    name: string;

    // 栏目标题（显示标题）
    title: string;

    // 栏目业务编码（唯一键）
    code: string;

    // 生效状态（0-无效 1-有效）
    activeStatus: number;

    // 子节点排序索引
    childIndex: number;

    // 父级栏目编码（树形结构）
    parentCode: string;

    // 定位路径字符串
    locateString: string;

    // 栏目图标URL
    categoryIcon: string;

    // 背景图URL
    background: string;

    // 栏目描述信息
    description: string;

    // 支持的业务类型（逗号分隔）
    serviceTypes: string;

    // 基础角标文字
    baseCornerMark: string;

    // 搜索关键词
    keyword: string;

    // 是否过滤搜索（0-否 1-是）
    filterSearch: number;

    // 栏目平台类型（0:EPG栏目 1:厂商栏目 2:榜单栏目）
    platformType: number;

    // 排序规则（旧版）
    orderRule: string;

    // 关联榜单编码
    refCode: string;

    // 排序规则（JSON格式存储）
    orderRuleJson: string;

    // 所属专区编码
    zoneCode: string;

    // 子栏目列表（非持久化字段）
    children: EpgCategory[];

    // 是否是叶子结点
    isLeaf: boolean;
}

// cp|op
export interface PartnerVO {
    // id
    id: number;
    // 名称
    name: string;
    // 类型
    type: string;
    // 类型
    code: string;
    // 描述
    description: string;
    // 首字母
    initials: string;
    // 可用状态  0:禁用 1:启用
    enableStatus: number;
    // 父级ID
    parentId: number;
    // 定位字符串
    locateString: string;
    // 全路径名称
    locateTitle: string;
}

// 素材
export interface MaterialFolderVO {
    // id
    id: number;
    // 名称
    name: string;
    // 类型
    type: string;
    // 类型
    code: string;
    // 栏目code
    categoryCode: string;
    //
    status: number;
    //
    auditStatus: number;
    //
    parentId: number;
    //
    locateString: string;
    //
    locateTitle: string;

    createTime: string;

    updateTime: string;

    initials: string;

    orderNo: number;

    tags: Array<MaterialTagVO>;
}

// 素材标签
export interface MaterialTagVO {
    // id
    id: number;
    // 名称
    name: string;
    // 编码
    code: string;
    // 父ID
    folderId: number;
    // 标签类型
    folderType: string;
    // 状态
    status: number;
}

// 资费包
export interface PackageVO {
    // id
    id: number;
    // 创建时间
    createTime: string;

    // 创建人
    createrId: string;

    // 外部code
    externalCode: string;
    // 状态
    status: number;

    // 更新时间
    updateTime: string;

    // 更新人
    updaterId: string;
    //
    autoOrder: boolean;

    // 可以状态
    enableStatus: number;

    // 标题
    title: string;

    //
    licensingWindowStart: string;

    //
    licensingWindowEnd: string;

    // 订购编号
    orderNumber: string;

    // 价格
    price: number;

    // 租用有效期
    rentalPeriod: number;

    // 搜索名称
    searchName: string;

    // 索引名称
    sortName: string;

    // 资费类型
    type: string;

    // cdn状态
    cdnStatus: number;

    // 收费类型
    chargeType: number;

    // 外部id
    externalId: number;

    // 描述
    description: string;

    // 名称
    name: string;

    // 编码
    code: string;

    //
    countContent: number;

    // 序号
    sortIndex: number;

    // 是否支持单点
    singleOrder: number;
}

// 片段
export interface ContentPointVO {
    // 片段ID
    id: number;
    // 创建时间
    createTime: string;
    // 内容编码
    contentCode: string;
    // 打点ID
    pointId: string;
    // 开始时间
    startTime: number;
    // 结束时间
    endTime: number;
    // 标题
    title: string;
    // 横图封面地址
    img1Url: string;
    // 竖图封面地址
    img2Url: string;
    // 竖图封面图路径
    img1Path: string;
    // 竖图封面图路径
    img2Path: string;
}

// 角标
export interface EpgShowFlag {
    // 角标ID
    id: number;

    // 角标名称
    name: string;

    // 角标编码
    code: string;

    // 角标类型
    type: number;

    // 角标图标
    icon: string;

    // 角标图标2
    icon2: string;

    // 角标位置
    position: number;
}
