import 'pinia';
import { PersistOptions } from '../types';

// 扩展 Pinia 的类型定义
declare module 'pinia' {
    // 扩展所有 store 实例
    export interface _StoreWithState<Id, S, G, A> {
        $clear(): Promise<void>;
    }

    // 扩展 store 配置选项
    export interface DefineStoreOptionsBase<S, Store> {
        persist?: PersistOptions;
    }

    // 扩展组合式 API 的配置
    export interface DefineSetupStoreOptions<Id extends string, S> {
        persist?: PersistOptions;
    }
}
