import { defineStore } from 'pinia';
import { ref } from 'vue';
import { STORAGE_DRIVER } from '@chances/portal_common_core';
import { EpgShowFlag } from '@smartdesk/common/types';
import { cmsApi } from '@smartdesk/common/api';

// 角标存储
export const useCornerMarkStore = defineStore(
    'corner-mark-store',
    () => {
        // 角标列表
        const cornerMarkList = ref<EpgShowFlag[]>([]); // 可以更精确地定义类型，比如 CornerMarkModel[]

        // 设置角标列表
        const setCornerMarkList = (list: any[]) => {
            cornerMarkList.value = list;
        };

        // 根据 code 查询某个角标项
        const getCornerMarkByCode = (code: string) => {
            return cornerMarkList.value.find((item) => item.code === code);
        };

        // 根据 codes 查询某个角标列表
        const getCornerMarkByCodes = (codes: string[]): EpgShowFlag[] => {
            return codes
                .map((code) => cornerMarkList.value.find((item) => item.code === code))
                .filter((item): item is EpgShowFlag => item !== undefined);
        };

        // 初始化函数（调用接口获取角标列表）
        const init = async () => {
            try {
                const res = await cmsApi.searchCorner({ types: [1, 2] }, { paged: false });
                if (res.code === 200) {
                    setCornerMarkList(res.result);
                } else {
                    console.warn('获取角标失败:', res.msg);
                }
            } catch (err) {
                console.error('角标初始化失败:', err);
            }
        };

        return {
            init,
            cornerMarkList,
            setCornerMarkList,
            getCornerMarkByCode,
            getCornerMarkByCodes,
        };
    },
    {
        persist: {
            enabled: true,
            storeName: 'smart-desk-portal-store',
            driver: STORAGE_DRIVER.LOCAL_STORAGE,
            storeKey: 'corner-mark-store',
        },
    }
);
