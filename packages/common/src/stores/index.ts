import { Pinia } from 'pinia';

// 公共
import { useSiteStore } from './site-store';
import { useCornerMarkStore } from './corner-mark-store';

// 公共
export { useSiteStore } from './site-store';
export { useCornerMarkStore } from './corner-mark-store';

// 注册所有 store
export default (piniaInstance?: Pinia) => {
    if (!piniaInstance) {
        console.warn('Pinia 实例不存在，stores 无法正常注册');
        return;
    }

    // 公共
    const siteStore = useSiteStore(piniaInstance);
    siteStore.init();

    // 公共
    const cornerMarkStoreStore = useCornerMarkStore(piniaInstance);
    cornerMarkStoreStore.init();

    return {
        // 公共
        siteStore,
    };
};
