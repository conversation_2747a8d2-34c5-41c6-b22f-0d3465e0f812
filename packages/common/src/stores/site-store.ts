import { defineStore } from 'pinia';
import { ref } from 'vue';
import { STORAGE_DRIVER } from '@chances/portal_common_core';
import { Site } from '@smartdesk/common/types';
import { siteApi } from '@smartdesk/common/api';

// 网站存储
export const useSiteStore = defineStore(
    'site-store',
    () => {
        // 当前网站编码
        const currentSiteCode = ref<string>('');

        // 当前网站
        const currentSite = ref<Site>();

        // 网站列表
        const siteList = ref<Array<Site>>([]);

        // 切换当前网站编码
        const switchSite = (siteCode: string) => {
            currentSiteCode.value = siteCode;
            currentSite.value = siteList.value.find((item) => item.code === siteCode);
        };

        // 根据查询条件获取网站列表
        const fetchSite = async () => {
            const res = await siteApi.findSites(
                {
                    delFlag: 0,
                    status: 1,
                },
                { paged: false }
            );
            if (res.code === 200 && res.result.length > 0) {
                siteList.value = res.result;
            }
            return res;
        };

        // 初始化
        const init = async () => {
            // 查询网站列表
            await fetchSite();

            // 如果当前没有选中的网站，默认选中第一个网站
            if (!currentSiteCode.value) {
                await switchSite(siteList.value[0].code);
            }
        };

        return {
            currentSiteCode,
            currentSite,
            siteList,
            switchSite,
            fetchSite,
            init,
        };
    },
    {
        persist: {
            enabled: true,
            storeName: 'smart-desk-portal-store',
            driver: STORAGE_DRIVER.LOCAL_STORAGE,
            storeKey: 'site-store',
        },
    }
);
