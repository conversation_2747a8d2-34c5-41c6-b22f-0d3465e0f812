import { useEnumStore } from '@chances/portal_common_core';
import { useSiteStore } from '@smartdesk/common/stores';

// 网站
const siteStore = useSiteStore();

//枚举
const enumStore = useEnumStore();

// 定义搜索条件key的名称
const searchFormKeyEnum = {
    siteCode: '网站',
    desktopCode: '桌面',
    name: '关键词',
    title: '关键词',
    delFlags: '删除状态',
    statuses: '可用状态',
    orgIds: '组织',
    layoutTypes: '布局类型',
    tags: '标签',
    bizGroups: '业务分组',
    onlineStatuses: '上下线状态',
    auditStatuses: '审核状态',
    visibleStatuses: '可见状态',
    sectionTypes: '楼层类型',
    resolutions: '分辨率',
    boxGroups: '机顶盒厂商',
    componentCategorys: '组件分类',
    componentCatalogs: '组件类别',
};

export const SearchFormEnum = [
    // 网站
    {
        name: 'siteCode',
        keyFormat: searchFormKeyEnum.siteCode,
        type: 'none',
        valueFormat: '',
        close: (form: any) => {
            form.siteCode = '';
        },
    },
    // 桌面
    {
        name: 'desktopCode',
        keyFormat: searchFormKeyEnum.desktopCode,
        type: 'none',
        valueFormat: '',
        close: (form: any) => {
            form.desktopCode = '';
        },
    },
    // 关键词
    {
        name: 'name',
        keyFormat: searchFormKeyEnum.name,
        type: 'none',
        valueFormat: '',
        close: (form: any) => {
            form.name = '';
        },
    },
    // 关键词
    {
        name: 'title',
        keyFormat: searchFormKeyEnum.title,
        type: 'none',
        valueFormat: '',
        close: (form: any) => {
            form.title = '';
        },
    },
    // 删除状态
    {
        name: 'delFlags',
        keyFormat: searchFormKeyEnum.delFlags,
        type: 'enum',
        valueFormat: 'delFlag',
        close: (form: any) => {
            form.delFlags = [];
        },
    },
    // 可用状态
    {
        name: 'statuses',
        keyFormat: searchFormKeyEnum.statuses,
        type: 'enum',
        valueFormat: 'enableStatus',
        close: (form: any) => {
            form.statuses = [];
        },
    },
    // 组织
    {
        name: 'orgIds',
        keyFormat: searchFormKeyEnum.orgIds,
        type: 'entity',
        valueFormat: (ids: number[], orgList: any[]): string => {
            return ids.map((id) => orgList.find((org) => org.value == id)?.label).join('|');
        },
        close: (form: any) => {
            form.orgIds = [];
        },
    },
    // 网站
    {
        name: 'siteCode',
        keyFormat: searchFormKeyEnum.siteCode,
        type: 'entity',
        valueFormat: (siteCode: string, siteCodeList: any[]): string => {
            return siteCodeList.find((site) => site.code == siteCode)?.name;
        },
        close: (form: any) => {},
    },
    // 布局类型
    {
        name: 'layoutTypes',
        keyFormat: searchFormKeyEnum.layoutTypes,
        type: 'enum',
        valueFormat: 'layoutType',
        close: (form: any) => {
            form.layoutTypes = [];
        },
    },
    // 标签
    {
        name: 'tags',
        keyFormat: searchFormKeyEnum.tags,
        type: 'none',
        valueFormat: '',
        close: (form: any) => {
            form.tags = '';
        },
    },
    // 业务分组
    {
        name: 'bizGroups',
        keyFormat: searchFormKeyEnum.bizGroups,
        type: 'enum',
        valueFormat: 'bizGroup',
        close: (form: any) => {
            form.bizGroups = [];
        },
    },
    // 上下线状态
    {
        name: 'onlineStatuses',
        keyFormat: searchFormKeyEnum.onlineStatuses,
        type: 'enum',
        valueFormat: 'onlineStatus',
        close: (form: any) => {
            form.onlineStatuses = [];
        },
    },
    // 审核状态
    {
        name: 'auditStatuses',
        keyFormat: searchFormKeyEnum.auditStatuses,
        type: 'enum',
        valueFormat: 'auditStatus',
        close: (form: any) => {
            form.auditStatuses = [];
        },
    },
    // 可见状态
    {
        name: 'visibleStatuses',
        keyFormat: searchFormKeyEnum.visibleStatuses,
        type: 'enum',
        valueFormat: 'visibleStatus',
        close: (form: any) => {
            form.visibleStatuses = [];
        },
    },
    // 楼层类型
    {
        name: 'sectionTypes',
        keyFormat: searchFormKeyEnum.sectionTypes,
        type: 'enum',
        valueFormat: 'sectionType',
        close: (form: any) => {
            form.sectionTypes = [];
        },
    },
    // 分辨率
    {
        name: 'resolutions',
        keyFormat: searchFormKeyEnum.resolutions,
        type: 'enum',
        valueFormat: 'resolution',
        close: (form: any) => {
            form.resolutions = [];
        },
    },
    // 机顶盒厂商
    {
        name: 'boxGroups',
        keyFormat: searchFormKeyEnum.boxGroups,
        type: 'enum',
        valueFormat: 'boxGroupEnum',
        close: (form: any) => {
            form.boxGroups = [];
        },
    },
    // 组件分类
    {
        name: 'componentCategorys',
        keyFormat: searchFormKeyEnum.componentCategorys,
        type: 'enum',
        valueFormat: 'componentCategory',
        close: (form: any) => {
            form.componentCategorys = [];
        },
    },
    // 组件类别
    {
        name: 'componentCatalogs',
        keyFormat: searchFormKeyEnum.componentCatalogs,
        type: 'enum',
        valueFormat: 'componentCatalog',
        close: (form: any) => {
            form.componentCatalogs = [];
        },
    },
];

// 判断是否存在值
export function isValueNotEmpty(val: any) {
    if (Array.isArray(val)) return val.length > 0;
    if (typeof val === 'string') return val.trim() !== '';
    if (typeof val === 'number') return true;
    if (val === null || val === undefined) return false;
    return true;
}

// 获取key名称
export function getKeyFormat(field: string) {
    const config = SearchFormEnum.find((item) => item.name === field);
    return config ? config.keyFormat : field;
}

// 获取搜索条件值
export function getValueFormat(field: string, value: any, orgOptions?: any[]) {
    const config = SearchFormEnum.find((item) => item.name === field);
    if (!config) return value;
    if (config.name === 'siteCode') {
        return siteStore.siteList.find((site) => site.code == value)?.name;
    }
    if (typeof config.valueFormat === 'function') {
        return config.valueFormat(value, orgOptions || []);
    }
    if (config.type === 'enum') {
        switch (field) {
            case 'delFlags':
            case 'statuses':
            case 'onlineStatuses':
            case 'auditStatuses':
            case 'visibleStatuses':
                return value
                    .map(
                        (status: any) =>
                            (enumStore.getNameCodeNumberOptionsByKey(config.valueFormat as string) || []).find(
                                (item) => item.code === status
                            )?.name
                    )
                    .filter(Boolean)
                    .join('｜');
                break;
            case 'layoutTypes':
            case 'bizGroups':
            case 'sectionTypes':
            case 'resolutions':
            case 'boxGroups':
            case 'componentCategorys':
            case 'componentCatalogs':
                return value
                    .map(
                        (status: any) =>
                            (enumStore.getEnumsByKey(config.valueFormat as string) || []).find(
                                (item) => item.code === status
                            )?.name
                    )
                    .filter(Boolean)
                    .join('｜');
                break;
        }
    }
    return value;
}

// 处理tag关闭事件
export function handleTagClose(field: string, form: any) {
    const config = SearchFormEnum.find((item) => item.name === field);
    if (config && typeof config.close === 'function') {
        config.close(form);
    } else {
        const val = form[field];
        if (Array.isArray(val)) form[field] = [];
        else form[field] = '';
    }
}
