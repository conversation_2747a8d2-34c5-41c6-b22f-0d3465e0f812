// 获取审核状态的dot状态
export const getAuditStatusDotStatus = (auditStatus: number) => {
    switch (auditStatus) {
        case 0:
            return 'default';
        case 1:
            return 'warning';
        case 2:
            return 'success';
        case 3:
            return 'error';
        default:
            return 'info';
    }
};

// 获取上线状态的dot状态
export const getOnlineStatusDotStatus = (onlineStatus: number) => {
    switch (onlineStatus) {
        case 0:
            return 'default';
        case 1:
            return 'success';
        case 2:
            return 'error';
        default:
            return 'info';
    }
};

// 获取可用状态的dot状态
export const getStatusDotStatus = (status: number) => {
    switch (status) {
        case 0:
            return 'error';
        case 1:
            return 'success';
        default:
            return 'info';
    }
};

// 获取删除状态的dot状态
export const getDelFlagDotStatus = (delFlag: number) => {
    switch (delFlag) {
        case 0:
            return 'success';
        case -1:
            return 'warning';
        case -2:
            return 'error';
        default:
            return 'info';
    }
};

// 获取可见状态的dot状态
export const getVisibleStatusDotStatus = (visibleStatus: number) => {
    switch (visibleStatus) {
        case 0:
            return 'error';
        case 1:
            return 'success';
        default:
            return 'info';
    }
};
