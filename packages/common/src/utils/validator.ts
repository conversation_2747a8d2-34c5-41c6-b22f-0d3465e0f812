import { validateApi } from '@smartdesk/common/api';
import { ValidateCodeForm, ValidateNameForm } from '@smartdesk/common/types';

// 名称重复校验器
export const nameDuplicateValidator = (getValidateNameForm: () => Partial<ValidateNameForm>) => {
    return async (rule: any, value: string, callback: (error?: Error) => void) => {
        if (!value) {
            return callback();
        }

        const validateNameForm = getValidateNameForm();
        const res = await validateApi.validateNameDuplicate(validateNameForm);
        if (res.code === 200 && res.result) {
            callback(new Error('名称已存在'));
        } else {
            callback();
        }
    };
};

// 编码重复校验器
export const codeDuplicateValidator = (getValidateCodeForm: () => Partial<ValidateCodeForm>) => {
    return async (rule: any, value: string, callback: (error?: Error) => void) => {
        if (!value) {
            return callback();
        }

        const validateCodeForm = getValidateCodeForm();
        const res = await validateApi.validateCodeDuplicate(validateCodeForm);
        if (res.code === 200 && res.result) {
            callback(new Error('编码已存在'));
        } else {
            callback();
        }
    };
};
