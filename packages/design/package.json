{"name": "@smartdesk/design", "version": "1.0.0", "description": "可视化设计器模块", "type": "module", "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"dev": "vite --config vite.config.ts", "build": "vue-tsc && vite build --config vite.config.ts", "dev:lib": "vite --config vite.lib.config.ts", "build:lib": "vue-tsc && vite build --config vite.lib.config.ts", "type-check": "vue-tsc --build"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@epgui/epg-components": "1.0.4", "@smartdesk/common": "workspace:*", "axios": "^1.9.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "element-plus": "^2.9.10", "mitt": "^3.0.1", "vue-draggable-plus": "^0.6.0"}, "devDependencies": {"@iconify-json/mdi": "^1.2.3", "@types/node": "^24.0.14", "@vitejs/plugin-vue": "^6.0.0", "typescript": "^5.5.3", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.8.0", "vite": "^6.3.5", "vite-plugin-dts": "^4.0.0", "vue-tsc": "^2.0.0"}, "peerDependencies": {"@chances/portal_common_core": "1.0.20250807100708", "pinia": "2.3.1", "vue": "3.5.13", "vue-router": "4.5.0"}}