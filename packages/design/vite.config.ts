import { defineConfig } from 'vite';
// @ts-ignore
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import Icons from 'unplugin-icons/vite';
import IconsResolver from 'unplugin-icons/resolver';
import Components from 'unplugin-vue-components/vite';

export default defineConfig(({}) => {
    return {
        plugins: [
            vue(),
            Components({
                resolvers: [
                    IconsResolver({
                        prefix: 'i',
                        enabledCollections: ['mdi'],
                    }),
                ],
                dts: false,
            }),
            Icons({
                compiler: 'vue3',
                autoInstall: true,
                scale: 1,
                defaultClass: 'inline-block',
            }),
        ],
        build: {
            outDir: 'dist',
            rollupOptions: {
                input: resolve(__dirname, 'index.html'),
            },
        },
        resolve: {
            alias: {
                '@smartdesk/design': resolve(__dirname, 'src'),
                '@smartdesk/common': resolve(__dirname, '../common/src'),
            },
        },
        server: {
            proxy: {
                '/smartdesk_admin': {
                    target: 'http://localhost:10015',
                    changeOrigin: true,
                },
                '/cms_adapter': {
                    target: 'http://localhost:8808',
                    changeOrigin: true,
                },
                '/cms-picture': {
                    target: 'http://localhost:81',
                    changeOrigin: true,
                },
                '/cs_iam': {
                    target: 'http://localhost:7002',
                    changeOrigin: true,
                },
            },
        },
    };
});
