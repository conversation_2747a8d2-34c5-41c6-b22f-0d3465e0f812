<template>
    <el-tree
        :data="treeData"
        :props="{ children: 'children' }"
        node-key="id"
        :current-node-key="selectedNodeKey"
        highlight-current
        :default-expanded-keys="defaultExpandedKeys"
        style="width: 100%; height: 100%">
        <template #default="{ node, data }">
            <div class="tree-node" :class="{ 'is-desktop': data.type === 'desktop' }">
                <span class="node-name">
                    <template v-if="data.type === 'desktop'">
                        {{ data.name }}
                        {{ data.children?.length ? '（' + data.children.length + '）' : '' }}
                    </template>
                    <template v-else>
                        {{ data.title }}
                        {{ data.children?.length ? '（' + data.children.length + '）' : '' }}
                    </template>
                </span>
                <span class="node-page" v-if="data.type !== 'desktop' && data.pageCode">
                    <el-button type="primary" link @click.stop="onClickChangePage(data)">
                        {{ pageList.find((item) => item.code === data.pageCode)?.name || '' }}
                    </el-button>
                </span>
            </div>
        </template>
    </el-tree>
</template>

<script setup lang="ts">
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { onMounted, ref } from 'vue';
    import { desktopApi, navApi, pageApi } from '@smartdesk/common/api';
    import { Desktop, Nav, Page } from '@smartdesk/common/types';

    defineOptions({
        name: 'NavListSelector',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();

    // 树数据
    const treeData = ref<any[]>([]);

    // 桌面列表
    const desktopList = ref<Desktop[]>([]);

    // 导航列表
    const navList = ref<Nav[]>([]);

    // 页面列表
    const pageList = ref<Page[]>([]);

    // 选中节点 key（type + code）
    const selectedNodeKey = ref('');

    // 默认展开的 keys（例如当前桌面）
    const defaultExpandedKeys = ref<string[]>([]);

    // 点击切换页面
    const onClickChangePage = async (row: Nav) => {
        // 获取当前路径和 hash
        const path = window.location.pathname;
        const hash = window.location.hash;

        // 生成新的 query 字符串
        const newQuery = {
            mode: 'page',
            desktopCode: row.ownerPageCode,
            navCode: row.code,
            pageCode: row.pageCode,
            bizGroup: pageDesignerStore.bizGroup,
        };
        const search = new URLSearchParams(newQuery).toString();

        // 拼接新地址
        const newUrl = `${path}?${search}${hash}`;

        // 修改地址栏、加入历史记录、不刷新页面
        window.history.replaceState(null, '', newUrl);

        // 刷新页面
        pageDesignerStore.switchMode('page');
        await pageDesignerStore.setBizGroupDesktopNav(pageDesignerStore.bizGroup, row.ownerPageCode, row.code);
        await pageDesignerStore.refreshPage(row.pageCode);
    };

    /**
     * 递归设置节点 type 和 id
     */
    const setTypeAndId = (nodes: any[], type: string) => {
        nodes.forEach((node) => {
            node.type = type;
            node.id = type + node.code;
            if (node.children) {
                setTypeAndId(node.children, type);
            }
        });
    };

    // 根据业务分组，获取桌面列表
    const getDesktopList = async () => {
        const res = await desktopApi.getDesktopList(
            {
                siteCode: pageDesignerStore.page.siteCode,
                bizGroup: pageDesignerStore.bizGroup,
                delFlag: 0,
                status: 1,
            },
            { paged: false }
        );

        if (res.code === 200) {
            desktopList.value = res.result;
        }
    };

    // 根据桌面列表，获取导航列表
    const getNavList = async () => {
        const res = await navApi.findNavTree({
            siteCode: pageDesignerStore.page.siteCode,
            desktopCodes: desktopList.value.map((desktop) => desktop.code),
            delFlag: 0,
            status: 1,
        });

        if (res.code === 200) {
            navList.value = res.result;
        }
    };

    // 查询页面列表
    const getPageList = async () => {
        const res = await pageApi.getPageList({ siteCode: pageDesignerStore.page.siteCode }, { paged: false });
        if (res.code === 200) {
            pageList.value = res.result;
        }
    };

    // 构建双树数据
    const buildTreeData = () => {
        const navMap = new Map<string, Nav[]>();
        navList.value.forEach((nav) => {
            if (!nav.parentCode) {
                const code = nav.ownerPageCode;
                if (!navMap.has(code)) navMap.set(code, []);
                navMap.get(code)!.push(nav);
            }
        });

        treeData.value = desktopList.value.map((desktop) => {
            const children = navMap.get(desktop.code) || [];
            setTypeAndId(children, 'nav');
            return {
                type: 'desktop',
                code: desktop.code,
                id: 'desktop' + desktop.code,
                name: desktop.name,
                children,
            };
        });
    };

    onMounted(async () => {
        await getDesktopList();
        await getNavList();
        buildTreeData();

        await getPageList();

        // 设置默认展开（当前桌面）
        if (pageDesignerStore.desktopCode) {
            defaultExpandedKeys.value = ['desktop' + pageDesignerStore.desktopCode];
        }

        // 设置选中节点（当前导航）
        if (pageDesignerStore.navCode) {
            selectedNodeKey.value = 'nav' + pageDesignerStore.navCode;
        }
    });
</script>

<style scoped>
    .tree-node {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .node-name {
        flex: 1;
        margin-right: 10px;
    }

    .node-page {
        flex: 0 0 auto;
    }

    .is-desktop .node-name {
        font-weight: bold;
    }
</style>
