<template>
    <movie-content v-model="selectedContent" :contentType="contentType" v-if="isContentType()" />
    <category-content v-model="selectedContent" v-if="contentType === 'category'" />
    <third-party-content v-model="selectedContent" v-if="contentType === 'thirdParty'" />
    <link-content v-model="selectedContent" :contentType="contentType" v-if="contentType === 'link'" />
    <cast-content v-model="selectedContent" :contentType="contentType" v-if="contentType === 'cast'" />
    <channel-content v-model="selectedContent" :contentType="contentType" v-if="contentType === 'channel'" />
    <schedule-content v-model="selectedContent" :contentType="contentType" v-if="contentType === 'schedule'" />
    <column-content v-model="selectedContent" :contentType="contentType" v-if="contentType === 'column'" />
    <subject-content
        v-model="selectedContent"
        :contentType="contentType"
        v-if="contentType === 'subject' || contentType === 'activitySubject'" />
    <point-content v-model="selectedContent" :contentType="contentType" v-if="contentType === 'clip'" />
    <desktop-content v-model="selectedContent" :contentType="contentType" v-if="contentType === 'desktop'" />
    <page-content v-model="selectedContent" :contentType="contentType" v-if="contentType === 'page'" />
</template>

<script setup lang="ts">
    import { ref } from 'vue';
    import MovieContent from './content/movie-content.vue';
    import CategoryContent from './content/category-content.vue';
    import CastContent from './content/cast-content.vue';
    import LinkContent from './content/link-content.vue';
    import ChannelContent from './content/channel-content.vue';
    import ScheduleContent from './content/schedule-content.vue';
    import ColumnContent from './content/column-content.vue';
    import SubjectContent from './content/subject-content.vue';
    import PointContent from './content/point-content.vue';
    import ThirdPartyContent from './content/third-party-content.vue';
    import DesktopContent from './content/desktop-content.vue';
    import PageContent from './content/page-content.vue';

    // 内容选择器
    defineOptions({
        name: 'ContentSelector',
    });

    // 参数
    const props = defineProps<{
        // 对象类型
        contentType: string | undefined;
    }>();

    // 当前选中内容
    const selectedContent = ref();

    // 是否是内容类型
    const isContentType = () => {
        const validTypes = new Set(['movie', 'series', 'episode', 'series2', 'episode2', 'mediakit']);
        return validTypes.has(props.contentType || '');
    };

    defineExpose({
        selectedContent,
    });
</script>
