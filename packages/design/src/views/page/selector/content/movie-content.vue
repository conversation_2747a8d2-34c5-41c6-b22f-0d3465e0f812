<template>
    <el-form label-width="100px" :model="searchForm" :label-suffix="':'" :size="'default'">
        <el-row :gutter="24">
            <el-col :span="6">
                <el-form-item label="内容类型">
                    <el-select v-model="props.contentType" disabled placeholder="请选择内容类型">
                        <el-option
                            v-for="item in contentTypeOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="内容提供商">
                    <el-select
                        multiple
                        collapse-tags
                        clearable
                        placeholder="请选择内容提供商"
                        v-model="searchForm.contentCpCodes">
                        <el-option
                            v-for="item in contentCpCodeOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="语言">
                    <el-select multiple collapse-tags clearable placeholder="请选择语言" v-model="searchForm.vodDubs">
                        <el-option
                            v-for="item in languageOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="内容产地">
                    <el-select multiple collapse-tags clearable placeholder="请选择内容产地" v-model="searchForm.areas">
                        <el-option
                            v-for="item in areaOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="6">
                <el-form-item label="节目类型">
                    <el-select
                        placeholder="请选择节目类型"
                        clearable
                        v-model="searchForm.typeCode"
                        @change="changeTypeCode">
                        <el-option
                            v-for="item in contentTypeCodeOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="基础类型">
                    <el-input
                        v-model="basTagNames"
                        placeholder="请选择基础类型"
                        readonly
                        @focus="onBastagsFocus"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="上映年份">
                    <el-select multiple collapse-tags clearable placeholder="请选择上映年份" v-model="searchForm.years">
                        <el-option
                            v-for="item in yearOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="资费包">
                    <el-select placeholder="请选择资费包" clearable v-model="searchForm.packageCode">
                        <el-option
                            v-for="item in packageListOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="6">
                <el-form-item label="运营分类">
                    <el-select
                        placeholder="请选择运营分类"
                        clearable
                        v-model="searchForm.opType"
                        @change="changeOpType">
                        <el-option
                            v-for="item in opTypeOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="运营标签">
                    <el-input
                        v-model="opTagNames"
                        placeholder="请选择运营标签"
                        readonly
                        @focus="onOpTagFocus"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="演员/嘉宾">
                    <el-input placeholder="请输入演员/嘉宾" clearable v-model="searchForm.actors"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="导演">
                    <el-input placeholder="请输入导演" clearable v-model="searchForm.directors"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="6">
                <el-form-item label="内容编码">
                    <el-input placeholder="请输入内容编码" clearable v-model="searchForm.code"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="内容名称">
                    <el-input placeholder="请输入内容名称" clearable v-model="searchForm.name"> </el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="审核人">
                    <el-select placeholder="请选择审核人" clearable v-model="searchForm.auditUserId">
                        <el-option
                            v-for="item in languageOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-row>
                    <el-form-item label="评分">
                        <el-col :span="10">
                            <el-input
                                v-model="searchForm.scoreBegin"
                                @input="searchForm.scoreBegin = filterNumberInput(searchForm.scoreBegin + '')">
                            </el-input>
                        </el-col>
                        ~
                        <el-col :span="10">
                            <el-input
                                v-model="searchForm.scoreEnd"
                                @input="searchForm.scoreEnd = filterNumberInput(searchForm.scoreEnd + '')">
                            </el-input>
                        </el-col>
                    </el-form-item>
                </el-row>
            </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="6">
                <el-form-item label="关键字">
                    <el-input placeholder="请输入关键字" clearable v-model="searchForm.keyword">
                        <template #append>
                            <el-button :icon="Search" @click="onSearchData" />
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>

    <el-table
        v-loading="loading"
        :data="contentList"
        :style="{ width: '100%' }"
        :row-key="(row: any) => row.code"
        highlight-current-row
        border
        @current-change="handleCurrentChange"
        :current-row-key="selectedRow">
        <el-table-column label="选择" width="55">
            <template #default="scope">
                <el-radio
                    class="hidden-label"
                    v-model="selectedRow"
                    :label="scope.row.code"
                    @change="handleRowSelect(scope.row)">
                </el-radio>
            </template>
        </el-table-column>
        <el-table-column v-if="props.contentType != 'mediakit'" label="名称">
            <template #default="scope">
                {{ scope.row.name ? scope.row.name : scope.row.title }}
            </template>
        </el-table-column>
        <el-table-column v-if="props.contentType === 'mediakit'" property="title" label="标题"></el-table-column>
        <el-table-column property="code" label="编码" width="300"></el-table-column>
        <el-table-column label="提供商" width="120">
            <template #default="scope">
                {{ getCpNameByCode(scope.row.cpCode) }}
            </template>
        </el-table-column>
        <el-table-column property="year" label="年份" width="120"></el-table-column>
    </el-table>

    <base-pagination :page-info="pageInfo" @change="handlePageChange" />
    <select-tag
        v-model:visible="showTagDialog"
        :tagOption="tagOptions"
        :tagType="tagType"
        :checkedTags="checkedTags"
        @on-click-confirm="onTagConfirm" />
</template>

<script setup lang="ts">
    import { onMounted, reactive, ref } from 'vue';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import {
        BaseContentVO,
        ContentQueryParam,
        MaterialFolderVO,
        MaterialTagVO,
        PackageVO,
        PageInfo,
        PaginationParams,
        PartnerVO,
    } from '@smartdesk/common/types';
    import { cmsApi } from '@smartdesk/common/api';
    import { Search } from '@element-plus/icons-vue';
    import { ElMessage } from 'element-plus';

    // 参数
    const props = defineProps<{
        // 当前选中内容
        modelValue: BaseContentVO;
        contentType: string | undefined;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: Object): void;
    }>();
    // 加载
    const loading = ref(false);
    const enumStore = useEnumStore();
    // 选中的行
    const currentRow = ref(null);
    const selectedRow = ref(props.modelValue?.code || '');
    const handleRowSelect = (row: any) => {
        currentRow.value = row;
        selectedRow.value = row.code;
    };
    // 查询表单
    const searchForm = ref<ContentQueryParam>({} as ContentQueryParam);

    // 当前分页信息
    const pageInfo = reactive<PageInfo>({
        page: 1,
        size: 10,
        sizeArray: [10, 20, 50, 100],
        totalElements: 0,
    });

    // 分页参数
    const pageParam = ref<PaginationParams>({} as PaginationParams);

    // 列表
    const contentList = ref<BaseContentVO[]>([]);

    // 内容类型选项
    const contentTypeOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('relevanceContentEnum') || []);

    // 节目类型选项
    const contentTypeCodeOptions = ref<MaterialFolderVO[]>([]);

    // 资费包选项
    const packageListOptions = ref<PackageVO[]>([]);

    // 语言选项
    const languageOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('vodDubEnum') || []);

    // 内容产地选项
    const areaOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('countryEnum') || []);

    // 年份选项
    const yearOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('yearEnum') || []);

    // 提供商
    const contentCpCodeOptions = ref<PartnerVO[]>([]);

    // 标签弹窗
    const showTagDialog = ref(false);

    // 基础标签列表
    const bastTagsList = ref<MaterialTagVO[]>([]);

    // 运营类型
    const opTypeOptions = ref<MaterialFolderVO[]>([]);

    // 运营标签
    const opTagOptions = ref<MaterialTagVO[]>([]);

    const tagOptions = ref<MaterialTagVO[]>([]);

    // 标签类型
    const tagType = ref<string>('');

    // 选中的标签
    const checkedTags = ref<string[]>([]);
    // 查询内容
    const getContentList = async () => {
        loading.value = true;
        pageParam.value.page = pageInfo.page - 1;
        pageParam.value.size = pageInfo.size;
        const validTypes = new Set(['movie', 'series', 'episode', 'series2', 'episode2', 'mediakit']);
        // 定义搜索方法映射
        const searchMethods: Record<string, Function> = {
            movie: cmsApi.searchMovie,
            series: cmsApi.searchSeries,
            episode: cmsApi.searchEpisode,
            series2: cmsApi.searchSeries2,
            episode2: cmsApi.searchEpisode2,
            mediakit: cmsApi.searchMediaKit,
        };
        // 根据 contentType 获取对应的 API 方法
        const searchMethod = searchMethods[props.contentType || ''];
        if (!searchMethod) {
            console.warn(`未找到 ${props.contentType} 对应的搜索方法`);
            return;
        }
        try {
            const res = await searchMethod(searchForm.value, pageParam.value);
            if (res.code === 200) {
                contentList.value = res.result;
                pageInfo.totalElements = res.page.totalElements;
            }
        } catch (error) {
            console.error('获取内容列表失败:', error);
        }
        loading.value = false;
    };

    // 初始化Enum数据
    const initEnumOptions = (): void => {
        languageOptions.value = enumStore.getEnumsByKey('vodDubEnum');
    };
    // 获取cp选项数据
    const getCpCodes = async () => {
        const res = await cmsApi.searchPartner('cp');
        if (res.code === 200) {
            contentCpCodeOptions.value = res.result;
        }
    };
    // 获取节目类型
    const getTypeCodeList = async () => {
        const res = await cmsApi.searchMaterialFolder('base');
        if (res.code === 200) {
            contentTypeCodeOptions.value = res.result;
        }
    };
    // 获取运营类型
    const getOpTypeList = async () => {
        const res = await cmsApi.searchMaterialFolder('management');
        if (res.code === 200) {
            opTypeOptions.value = res.result;
        }
    };
    // 获取资费包列表
    const getPackageList = async () => {
        const res = await cmsApi.searchPackage();
        if (res.code === 200) {
            packageListOptions.value = res.result;
        }
    };
    // 节目类型选中事件
    const changeTypeCode = (typeCode: string) => {
        searchForm.value.contentBastags = [];
        basTagNames.value = '';
    };
    // 运营分类选中事件
    const changeOpType = (opType: string) => {
        searchForm.value.opTags = [];
        opTagNames.value = '';
    };
    // 选择基础标签按钮
    const onBastagsFocus = () => {
        if (searchForm.value.typeCode) {
            bastTagsList.value =
                contentTypeCodeOptions.value.find((item) => item.code === searchForm.value.typeCode)?.tags || [];
            showTagDialog.value = true;
            tagType.value = 'base';
            checkedTags.value = searchForm.value.contentBastags;
            tagOptions.value = bastTagsList.value;
            return;
        }
        ElMessage.error('请选择节目类型');
    };
    // 选择运营标签按钮
    const onOpTagFocus = () => {
        if (searchForm.value.opType) {
            opTagOptions.value = opTypeOptions.value.find((item) => item.code === searchForm.value.opType)?.tags || [];
            showTagDialog.value = true;
            tagType.value = 'management';
            checkedTags.value = searchForm.value.opTags;
            tagOptions.value = opTagOptions.value;
            return;
        }
        ElMessage.error('请选运营分类');
    };
    // 根据cpCode获取 cpName
    const getCpNameByCode = (cpCode: string) => {
        return contentCpCodeOptions.value.find((item) => item.code === cpCode)?.name;
    };
    // 处理选择
    const handleCurrentChange = (currentRow: any) => {
        selectedRow.value = currentRow ? currentRow.code : null;
        emit('update:modelValue', currentRow);
    };
    const basTagNames = ref('');
    const opTagNames = ref('');
    // 基础标签选中事件
    const onBastagConfirm = (bastags: string[]) => {
        searchForm.value.contentBastags = bastags;
        // 过滤出匹配的项，获取 name
        const selectedNames = bastTagsList.value
            .filter((item) => bastags.includes(item.code)) // 筛选出匹配的项
            .map((item) => item.name); // 取出 name
        basTagNames.value = selectedNames.join(',');
    };
    const opTagsNames = ref('');
    // 标签选中
    const onTagConfirm = (tags: string[], tagType: string) => {
        if (tagType === 'base') {
            onBastagConfirm(tags);
        }
        if (tagType === 'management') {
            onOpTagConfirm(tags);
        }
    };
    // 运营标签选中事件
    const onOpTagConfirm = (opTags: string[]) => {
        searchForm.value.opTags = opTags;
        // 过滤出匹配的项，获取 name
        const selectedNames = opTagOptions.value
            .filter((item) => opTags.includes(item.code)) // 筛选出匹配的项
            .map((item) => item.name); // 取出 name
        opTagNames.value = selectedNames.join(',');
    };

    // 处理分页变化
    const handlePageChange = (newPageInfo: PageInfo) => {
        Object.assign(pageInfo, newPageInfo);
        getContentList();
    };
    // 点击搜索按钮
    const onSearchData = () => {
        getContentList();
    };
    const filterNumberInput = (value: string): number => {
        let num = value.replace(/[^\d.]/g, ''); // 只保留数字和小数点
        num = num.replace(/^0+(\d)/, '$1'); // 去掉前导 0
        num = num.replace(/^\./, '0.'); // 如果以 "." 开头，改为 "0."
        num = num.replace(/(\.\d{2})\d+/, '$1'); // 限制小数点后两位

        // 转换为浮点数
        let floatValue = parseFloat(num);

        // 限制范围 0-10
        if (isNaN(floatValue)) return 0;
        if (floatValue > 10) return 10;
        if (floatValue < 0) return 0;

        return floatValue;
    };

    onMounted(() => {
        initEnumOptions();
        getCpCodes();
        getTypeCodeList();
        getPackageList();
        getOpTypeList();
        getContentList();
    });
</script>

<style scoped>
    :deep(.hidden-label) .el-radio__label {
        display: none !important;
    }
</style>
