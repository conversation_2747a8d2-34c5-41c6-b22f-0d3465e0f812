<template>
    <base-tree ref="baseTreeRef" show-checkbox :fetch-api="fetchTreeData" @select="handleSelect"></base-tree>
</template>

<script setup lang="ts">
    import { cmsApi } from '@smartdesk/common/api';
    import { ref } from 'vue';

    // 参数
    const props = defineProps<{
        // 当前选中内容
        modelValue: {
            type: Object;
            required: true;
        };
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: Object): void;
    }>();

    // 树引用
    const baseTreeRef = ref<HTMLElement | null>(null);

    // 查询栏目树数据
    const fetchTreeData = async (params: any) => {
        return cmsApi.searchCategory(params.isRoot, params.parentCode, params.keyword);
    };

    // 处理搜索树选择
    const handleSelect = (node: any) => {
        emit('update:modelValue', node);
    };
</script>
