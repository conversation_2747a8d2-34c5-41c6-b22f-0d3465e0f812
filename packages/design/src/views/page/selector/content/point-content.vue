<template>
    <el-form label-width="100px" :model="searchForm" :label-suffix="':'" :size="'default'">
        <el-row :gutter="24">
            <el-col :span="6">
                <el-form-item label="内容类型">
                    <el-select v-model="props.contentType" disabled placeholder="请选择内容类型">
                        <el-option
                            v-for="item in contentTypeOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="片段类型">
                    <el-select v-model="searchForm.pointType" placeholder="请选片段类型">
                        <el-option
                            v-for="item in contentPointTypeOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="内容名称">
                    <el-input placeholder="请输入内容名称" clearable v-model="searchForm.name"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="内容编码">
                    <el-input placeholder="请输入内容编码" clearable v-model="searchForm.code"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="6">
                <el-form-item label="片段标题">
                    <el-input placeholder="请输入片段标题" clearable v-model="searchForm.pointName"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="片段信息标识">
                    <el-input placeholder="请输入片段信息标识" clearable v-model="searchForm.pointCode"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="关键字">
                    <el-input placeholder="请输入关键字" clearable v-model="searchForm.keyword">
                        <template #append>
                            <el-button :icon="Search" @click="onSearchData" />
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>

    <el-table
        v-loading="loading"
        :data="contentList"
        :style="{ width: '100%' }"
        :row-key="(row: any) => row.id"
        highlight-current-row
        border
        :current-row-key="selectedRow">
        <el-table-column label="选择" width="55">
            <template #default="scope">
                <el-radio
                    class="hidden-label"
                    v-model="selectedRow"
                    :label="scope.row.id"
                    @change="() => handleRadioChange(scope.row)">
                </el-radio>
            </template>
        </el-table-column>
        <el-table-column label="内容名称">
            <template #default="scope">
                {{ scope.row.baseContentVO?.name }}
            </template>
        </el-table-column>
        <el-table-column property="contentCode" label="内容编码"></el-table-column>
        <el-table-column property="title" label="片段标题"></el-table-column>
        <el-table-column property="pointId" label="片段信息标识"></el-table-column>
        <el-table-column property="img1Url" label="横图地址" show-overflow-tooltip></el-table-column>
        <el-table-column property="img2Url" label="竖图地址" show-overflow-tooltip></el-table-column>
        <el-table-column label="横图">
            <template #default="scope">
                <el-image style="width: 100px; height: 100px" :src="scope.row.img1Path">
                    <template #error>
                        <image-error-fallback text="图片损坏" />
                    </template>
                </el-image>
            </template>
        </el-table-column>
        <el-table-column label="竖图">
            <template #default="scope">
                <el-image style="width: 100px; height: 100px" :src="scope.row.img2Path">
                    <template #error>
                        <image-error-fallback text="图片损坏" />
                    </template>
                </el-image>
            </template>
        </el-table-column>
        <el-table-column property="startTime" label="开始时间(秒)"></el-table-column>
        <el-table-column property="endTime" label="结束时间(秒)"></el-table-column>
        <el-table-column property="createTime" label="创建时间"></el-table-column>
    </el-table>
    <base-pagination :page-info="pageInfo" @change="handlePageChange" />
</template>
<script setup lang="ts">
    import { ContentPointQueryParam, ContentPointVO, PageInfo, PaginationParams } from '@smartdesk/common/types';
    import { onMounted, reactive, ref } from 'vue';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import { Search } from '@element-plus/icons-vue';
    import { cmsApi } from '@smartdesk/common/api';

    // 参数
    const props = defineProps<{
        // 当前选中内容
        modelValue: ContentPointVO;
        contentType: string | undefined;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: Object): void;
    }>();
    // 当前分页信息
    const pageInfo = reactive<PageInfo>({
        page: 1,
        size: 10,
        sizeArray: [10, 20, 50, 100],
        totalElements: 0,
    });

    // 分页参数
    const pageParam = ref<PaginationParams>({} as PaginationParams);
    // 列表
    const contentList = ref<ContentPointVO[]>([]);
    // 加载
    const loading = ref(false);
    const enumStore = useEnumStore();
    // 内容类型选项
    const contentTypeOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('relevanceContentEnum') || []);
    // 片段类型
    const contentPointTypeOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('pointContentTypeEnum') || []);
    // 选中的行
    const currentRow = ref<ContentPointVO>({} as ContentPointVO);
    const selectedRow = ref(props.modelValue?.id || '');
    // 统一处理单选
    const handleRadioChange = (row: ContentPointVO) => {
        selectedRow.value = row.id;
        emit('update:modelValue', row);
    };

    // 查询表单
    const searchForm = ref<ContentPointQueryParam>({} as ContentPointQueryParam);

    // 处理分页变化
    const handlePageChange = (newPageInfo: PageInfo) => {
        Object.assign(pageInfo, newPageInfo);
        getContentList();
    };
    // 点击搜索按钮
    const onSearchData = () => {
        getContentList();
    };
    // 查询列表数据
    const getContentList = async () => {
        if (!searchForm.value.pointType) {
            searchForm.value.pointType = contentPointTypeOptions.value[0].code;
        }
        loading.value = true;
        pageParam.value.page = pageInfo.page - 1;
        pageParam.value.size = pageInfo.size;
        const res = await cmsApi.searchPoint(searchForm.value, pageParam.value);
        if (res.code === 200) {
            contentList.value = res.result;
            pageInfo.totalElements = res.page.totalElements;
        }
        loading.value = false;
    };
    // 处理选择
    const handleCurrentChange = (currentRow: any) => {
        selectedRow.value = currentRow ? currentRow.id : null;
        emit('update:modelValue', currentRow);
    };
    onMounted(() => {
        getContentList();
    });
</script>
<script lang="ts">
    export default {
        name: 'PointContent',
    };
</script>
<style scoped>
    /* 隐藏 radio 的 label 文本 */
    :deep(.hidden-label) .el-radio__label {
        display: none !important;
    }
</style>
