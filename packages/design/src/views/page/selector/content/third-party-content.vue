<template>
    <el-form label-width="100px" :model="searchForm" :label-suffix="':'" :size="'default'">
        <el-form-item label="关键字">
            <el-input v-model="searchForm.keyword" placeholder="请输入" />
        </el-form-item>
    </el-form>

    <el-table
        v-loading="loading"
        :data="contentList"
        :style="{ width: '100%' }"
        :row-key="(row: any) => row.code"
        highlight-current-row
        @current-change="handleCurrentChange"
        :current-row-key="selectedRow">
        <el-table-column label="选择" width="55">
            <template #default="scope">
                <el-radio
                    class="hidden-label"
                    v-model="selectedRow"
                    :label="scope.row.code"
                    @change="handleRowSelect(scope.row)">
                </el-radio>
            </template>
        </el-table-column>
        <el-table-column property="name" label="名称"></el-table-column>
        <el-table-column property="code" label="编码"></el-table-column>
    </el-table>

    <base-pagination :page-info="pageInfo" @change="handlePageChange" />
</template>

<script setup lang="ts">
    import { onMounted, reactive, ref } from 'vue';
    import { CmsQueryParam, PageInfo, PaginationParams, ThirdPartyDataSourceVO } from '@smartdesk/common/types';
    import { cmsApi } from '@smartdesk/common/api';

    // 参数
    const props = defineProps<{
        // 当前选中内容
        modelValue: ThirdPartyDataSourceVO;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: ThirdPartyDataSourceVO): void;
    }>();

    // 加载
    const loading = ref(false);

    // 选中的行
    const currentRow = ref<ThirdPartyDataSourceVO | null>(null);
    const selectedRow = ref<string>(props.modelValue?.code || '');

    // 查询表单
    const searchForm = ref<Partial<CmsQueryParam>>({});

    // 当前分页信息
    const pageInfo = reactive<PageInfo>({
        page: 1,
        size: 10,
        sizeArray: [10, 20, 50, 100],
        totalElements: 0,
    });

    // 分页参数
    const pageParam = ref<PaginationParams>({} as PaginationParams);

    // 列表
    const contentList = ref<ThirdPartyDataSourceVO[]>([]);

    // 处理行选择
    const handleRowSelect = (row: ThirdPartyDataSourceVO) => {
        currentRow.value = row;
        selectedRow.value = row.code;
        emit('update:modelValue', row);
    };

    // 处理选择
    const handleCurrentChange = (currentRow: ThirdPartyDataSourceVO) => {
        selectedRow.value = currentRow ? currentRow.code : '';
        emit('update:modelValue', currentRow);
    };

    // 处理分页变化
    const handlePageChange = (newPageInfo: PageInfo) => {
        Object.assign(pageInfo, newPageInfo);
        getContentList();
    };

    // 查询内容
    const getContentList = async () => {
        loading.value = true;
        pageParam.value.page = pageInfo.page - 1;
        pageParam.value.size = pageInfo.size;

        const res = await cmsApi.searchDataSource(searchForm.value, pageParam.value);
        if (res.code === 200) {
            contentList.value = res.result;
            pageInfo.totalElements = res.page.totalElements;
        }

        loading.value = false;
    };

    onMounted(() => {
        getContentList();
    });
</script>

<style scoped>
    :deep(.hidden-label) .el-radio__label {
        display: none !important;
    }
</style>
