<template>
    <el-form label-width="100px" :model="searchForm" :label-suffix="':'" :size="'default'">
        <el-row :gutter="24">
            <el-col :span="6">
                <el-form-item label="内容类型">
                    <el-select v-model="props.contentType" disabled placeholder="请选择内容类型">
                        <el-option
                            v-for="item in contentTypeOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code"></el-option>
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="运营组织">
                    <el-tree-select
                        placeholder="请选择运营组织"
                        v-model="searchForm.cpCodes"
                        :data="contentOpCodeOptions"
                        multiple
                        :render-after-expand="false"
                        show-checkbox
                        check-strictly
                        check-on-click-node
                        style="width: 240px" />
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="内容名称">
                    <el-input placeholder="请输入内容名称" clearable v-model="searchForm.name"></el-input>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="内容编码">
                    <el-input placeholder="请输入内容编码" clearable v-model="searchForm.code"></el-input>
                </el-form-item>
            </el-col>
        </el-row>
        <el-row :gutter="24">
            <el-col :span="6">
                <el-form-item label="关键字">
                    <el-input placeholder="请输入关键字" clearable v-model="searchForm.keyword">
                        <template #append>
                            <el-button :icon="Search" @click="onSearchData" />
                        </template>
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>

    <el-table
        v-loading="loading"
        :data="contentList"
        :style="{ width: '100%' }"
        :row-key="(row: any) => row.code"
        highlight-current-row
        border
        @current-change="handleCurrentChange"
        :current-row-key="selectedRow">
        <el-table-column label="选择" width="55">
            <template #default="scope">
                <el-radio
                    class="hidden-label"
                    v-model="selectedRow"
                    :label="scope.row.code"
                    @change="handleRowSelect(scope.row)">
                </el-radio>
            </template>
        </el-table-column>
        <el-table-column property="title" label="标题"></el-table-column>
        <el-table-column property="code" label="编码"></el-table-column>
        <el-table-column property="type" label="专栏类型">
            <template #default="scope">
                {{ enumStore.getLabelByKeyAndValue('columnType', scope.row.type) }}
            </template>
        </el-table-column>
    </el-table>
    <base-pagination :page-info="pageInfo" @change="handlePageChange" />
</template>
<script setup lang="ts">
    import { AlbumVO, ColumnQueryParam, PageInfo, PaginationParams, PartnerVO } from '@smartdesk/common/types';
    import { onMounted, reactive, ref } from 'vue';
    import { Enumeration, useEnumStore } from '@chances/portal_common_core';
    import { Search } from '@element-plus/icons-vue';
    import { cmsApi } from '@smartdesk/common/api';

    defineOptions({
        name: 'ColumnContent',
    });
    // 参数
    const props = defineProps<{
        // 当前选中内容
        modelValue: AlbumVO;
        contentType: string | undefined;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: Object): void;
    }>();
    // 当前分页信息
    const pageInfo = reactive<PageInfo>({
        page: 1,
        size: 10,
        sizeArray: [10, 20, 50, 100],
        totalElements: 0,
    });

    // 分页参数
    const pageParam = ref<PaginationParams>({} as PaginationParams);
    // 列表
    const contentList = ref<AlbumVO[]>([]);
    // 加载
    const loading = ref(false);
    const enumStore = useEnumStore();
    // 内容类型选项
    const contentTypeOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('relevanceContentEnum') || []);
    // 选中的行
    const currentRow = ref(null);
    const selectedRow = ref(props.modelValue?.code || '');
    const contentOpCodeOptions = ref<PartnerVO[]>([]);
    // 行选中事件
    const handleRowSelect = (row: any) => {
        currentRow.value = row;
        selectedRow.value = row.code;
    };

    // 查询表单
    const searchForm = ref<ColumnQueryParam>({} as ColumnQueryParam);

    // 处理分页变化
    const handlePageChange = (newPageInfo: PageInfo) => {
        Object.assign(pageInfo, newPageInfo);
        getContentList();
    };
    // 点击搜索按钮
    const onSearchData = () => {
        getContentList();
    };
    // 查询列表数据
    const getContentList = async () => {
        loading.value = true;
        pageParam.value.page = pageInfo.page - 1;
        pageParam.value.size = pageInfo.size;
        const res = await cmsApi.searchColumn(searchForm.value, pageParam.value);
        if (res.code === 200) {
            contentList.value = res.result;
            pageInfo.totalElements = res.page.totalElements;
        }
        loading.value = false;
    };
    // 处理选择
    const handleCurrentChange = (currentRow: any) => {
        selectedRow.value = currentRow ? currentRow.code : null;
        emit('update:modelValue', currentRow);
    };
    // 获取op选项数据
    const getOpCodes = async () => {
        const res = await cmsApi.searchPartnerTree('op');
        if (res.code === 200) {
            contentOpCodeOptions.value = res.result;
        }
    };
    onMounted(() => {
        getOpCodes();
        getContentList();
    });
</script>
<script lang="ts">
    export default {
        name: 'ColumnContent',
    };
</script>
<style scoped>
    /* 隐藏 radio 的 label 文本 */
    :deep(.hidden-label) .el-radio__label {
        display: none !important;
    }
</style>
