<template>
    <el-form label-width="100px" :model="searchForm" :label-suffix="':'" :size="'default'">
        <el-row :gutter="24">
            <el-col :span="6">
                <el-form-item label="名称">
                    <el-input placeholder="请输入名称" clearable v-model="searchForm.name" />
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="编码">
                    <el-input placeholder="请输入编码" clearable v-model="searchForm.code" />
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="业务分组">
                    <el-select
                        v-model="searchForm.bizGroups"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in bizGroupOptions"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="组织">
                    <el-tree-select
                        v-model="searchForm.orgIds"
                        placeholder="请选择"
                        :data="orgTree"
                        :props="treeProps"
                        filterable
                        multiple
                        :render-after-expand="false"
                        collapse-tags
                        collapse-tags-tooltip
                        show-checkbox
                        check-strictly
                        check-on-click-node
                        style="width: 180px"
                        node-key="id"
                        value-key="id" />
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="标签">
                    <el-input placeholder="请输入标签" clearable v-model="searchForm.tags" />
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="上下线状态">
                    <el-select
                        v-model="searchForm.onlineStatuses"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in onlineStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="审核状态">
                    <el-select
                        v-model="searchForm.auditStatuses"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in auditStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
            <el-col :span="6">
                <el-form-item label="可见状态">
                    <el-select
                        v-model="searchForm.visibleStatuses"
                        placeholder="请选择"
                        size="default"
                        style="width: 180px"
                        clearable
                        collapse-tags
                        multiple>
                        <el-option
                            v-for="item in visibleStatusOptions"
                            :key="item.code"
                            :label="item.name"
                            :value="item.code" />
                    </el-select>
                </el-form-item>
            </el-col>
        </el-row>
    </el-form>

    <base-table-with-pagination
        :total="totalElements"
        :current-page="currentPage"
        :page-size="pageSize"
        @update:currentPage="handleCurrentPageChange"
        @update:pageSize="handleSizeChange">
        <template #table>
            <el-table
                v-loading="loading"
                :data="pageList"
                :style="{ width: '100%' }"
                :row-key="(row: any) => row.code"
                highlight-current-row
                border
                @current-change="handleCurrentChange"
                :current-row-key="currentRowCode">
                <el-table-column label="选择" width="55">
                    <template #default="scope">
                        <el-radio
                            class="hidden-label"
                            v-model="currentRowCode"
                            :label="scope.row.code"
                            @change="handleRowSelect(scope.row)">
                        </el-radio>
                    </template>
                </el-table-column>
                <el-table-column property="name" label="名称" width="180" show-overflow-tooltip></el-table-column>
                <el-table-column property="code" label="编码" width="180" show-overflow-tooltip></el-table-column>
                <el-table-column property="bizGroup" label="业务分组" width="160">
                    <template #default="{ row }">
                        <el-tag v-if="row.bizGroup" type="success">
                            {{ enumStore.getLabelByKeyAndValue('bizGroup', row.bizGroup) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column prop="resolution" label="分辨率" width="120">
                    <template #default="{ row }">
                        <el-tag type="primary" v-if="row.resolution">
                            {{ enumStore.getLabelByKeyAndValue('resolution', row.resolution) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column property="tags" label="标签" width="160" show-overflow-tooltip></el-table-column>
                <el-table-column prop="icon" label="缩略图" width="150">
                    <template #default="{ row }">
                        <el-image
                            :src="row.icon"
                            :preview-src-list="[row.icon]"
                            hide-on-click-modal
                            preview-teleported
                            style="width: 100px; height: 50px">
                            <template #error>
                                <image-error-fallback text="图片损坏" />
                            </template>
                        </el-image>
                    </template>
                </el-table-column>
                <el-table-column property="orgId" label="组织" min-width="140">
                    <template #default="{ row }">
                        <div>
                            {{ orgOptions.find((org) => org.value === row.orgId)?.label }}
                        </div>
                    </template>
                </el-table-column>
                <el-table-column label="状态" width="280">
                    <template #default="{ row }">
                        <status-columns :publishStatus="row" show-status />
                    </template>
                </el-table-column>
            </el-table>
        </template>
    </base-table-with-pagination>
</template>

<script setup lang="ts">
    import { CastVO, Dimension, Page, PageSearchForm } from '@smartdesk/common/types';
    import { onMounted, ref, watch } from 'vue';
    import { Enumeration, LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { dimensionApi, pageApi } from '@smartdesk/common/api';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';

    // 参数
    const props = defineProps<{
        // 当前选中内容
        modelValue: CastVO;
        contentType: string | undefined;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'update:modelValue', value: Object): void;
    }>();

    // pinia store
    const siteStore = useSiteStore();
    const enumStore = useEnumStore();

    // 查询表单
    const searchForm = ref<Partial<PageSearchForm>>({
        siteCode: siteStore.currentSiteCode,
        delFlag: 0,
        status: 1,
    });

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);

    // 组织树
    const orgTree = ref<Dimension[]>([]);

    // tree配置
    const treeProps = {
        children: 'children',
        label: 'name',
        value: 'id',
    };

    // 组织选项列表
    const orgOptions = ref<LabelValue[]>([]);

    //  业务分组
    const bizGroupOptions = ref<LabelValue[]>(enumStore.getOptionsByKey('bizGroup') || []);

    // 审核状态枚举
    const auditStatusOptions = ref<Partial<Enumeration>[]>(
        enumStore.getNameCodeNumberOptionsByKey('auditStatus') || []
    );

    // 可见枚举
    const visibleStatusOptions = ref<Partial<Enumeration>[]>(
        enumStore.getNameCodeNumberOptionsByKey('visibleStatus') || []
    );

    // 上线枚举
    const onlineStatusOptions = ref<Partial<Enumeration>[]>(
        enumStore.getNameCodeNumberOptionsByKey('onlineStatus') || []
    );

    // 列表加载状态
    const loading = ref(false);

    // 列表数据
    const pageList = ref<Page[]>([]);

    // 当前选中行
    const currentRow = ref(null);

    // 选中的行
    const currentRowCode = ref(props.modelValue?.code || '');

    // 处理页码变化
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1;
        getPageList();
    };

    // 处理页码变化
    const handleCurrentPageChange = (val: number) => {
        currentPage.value = val;
        getPageList();
    };

    // 行选中事件
    const handleCurrentChange = (currentRow: any) => {
        currentRowCode.value = currentRow ? currentRow.code : null;
        emit('update:modelValue', currentRow);
    };

    // 行选中事件
    const handleRowSelect = (row: any) => {
        currentRow.value = row;
        currentRowCode.value = row.code;
    };

    // 查询页面列表
    const getPageList = async () => {
        loading.value = true;

        const res = await pageApi.getPageList(searchForm.value, {
            page: currentPage.value - 1,
            size: pageSize.value,
            sort: 'id,desc',
        });
        if (res.code === 200) {
            pageList.value = res.result;
            totalElements.value = Number(res.page.totalElements);
        }

        loading.value = false;
    };

    // 获取组织树
    const getOrgTree = async () => {
        const res = await dimensionApi.findDimensionTree();
        if (res.code === 200) {
            orgTree.value = res.result;
        }
    };

    // 获取组织选项列表
    const getOrgOptions = async () => {
        const res = await dimensionApi.findDimensionList();
        if (res.code === 200) {
            orgOptions.value = res.result;
        }
    };

    // 监听查询表单
    watch(
        () => searchForm.value,
        () => {
            getPageList();
        },
        { deep: true }
    );

    onMounted(() => {
        getPageList();
        getOrgTree();
        getOrgOptions();
    });
</script>

<style scoped>
    :deep(.hidden-label) .el-radio__label {
        display: none !important;
    }
</style>
