<template>
    <div class="h-screen w-full bg-white" v-if="isLoaded">
        <three-panel-layout :header-height="headerHeight">
            <template #top>
                <page-top-panel :title="title" />
            </template>

            <template #left>
                <page-left-panel />
            </template>

            <template #center>
                <page-center-panel />
            </template>

            <template #right>
                <page-right-panel ref="pageRightPanelRef" />
            </template>
        </three-panel-layout>
    </div>
</template>

<script setup lang="ts">
    import { onBeforeMount, onBeforeUnmount, onMounted, ref } from 'vue';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { usePageDesignerStore, usePanelLayoutStore } from '@smartdesk/design/stores';
    import PageTopPanel from './panel/page-top-panel.vue';
    import PageLeftPanel from './panel/page-left-panel.vue';
    import PageCenterPanel from './panel/page-center-panel.vue';
    import PageRightPanel from './panel/page-right-panel.vue';
    import { useRoute } from 'vue-router';
    import { EventManager } from '@chances/portal_common_core';
    import { PageSectionListRefreshEvent } from '@smartdesk/design/events/page-section-list-refresh-event.ts';
    import { useGlobalLoading } from '@smartdesk/common/composables';

    // 页面设计器
    defineOptions({
        name: 'PageDesigner',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const currentSiteStore = useSiteStore();
    const panelLayoutStore = usePanelLayoutStore();
    const route = useRoute();
    const { show, hide } = useGlobalLoading();

    // ref
    const pageRightPanelRef = ref<InstanceType<typeof PageRightPanel> | null>();

    // 是否加载结束
    const isLoaded = ref<boolean>(false);

    // 顶部高度
    const headerHeight = ref<number>(50);

    // 标题
    const title = ref<string>('');

    // 页面编码
    const pageCode = ref<string>('');

    // 导航编码
    const navCode = ref<string>('');

    // 桌面编码
    const desktopCode = ref<string>('');

    // 业务分组
    const bizGroup = ref<string>('');

    // 模式
    const mode = ref<string>('');

    // 初始化，获取布局和数据
    const init = async () => {
        if (mode.value === 'page') {
            title.value = '页面';
            pageDesignerStore.switchMode('page');
            await pageDesignerStore.setBizGroupDesktopNav(bizGroup.value, desktopCode.value, navCode.value);
            await pageDesignerStore.refreshPage(pageCode.value);
            currentSiteStore.switchSite(pageDesignerStore.page.siteCode);
            return;
        }

        if (mode.value === 'desktop') {
            title.value = '桌面';
            pageDesignerStore.switchMode('desktop');
            await pageDesignerStore.refreshPage(desktopCode.value);
            currentSiteStore.switchSite(pageDesignerStore.page.siteCode);
            return;
        }
    };

    // 初始化根据页面编码查询页面
    onBeforeMount(async () => {
        show();
        mode.value = route?.query?.mode as string;
        bizGroup.value = route?.query?.bizGroup as string;
        desktopCode.value = route?.query?.desktopCode as string;
        navCode.value = route?.query?.navCode as string;
        pageCode.value = route?.query?.pageCode as string;

        await init();

        isLoaded.value = true;
        hide();
    });

    // 初始化
    onMounted(() => {
        // 订阅 page-section-list-refresh-event 事件
        const eventManager = new EventManager<PageSectionListRefreshEvent>(PageSectionListRefreshEvent.type);
        eventManager.subscribe(() => {
            pageRightPanelRef.value?.pageSectionMgmtRef?.findPageSectionList();
        });
    });

    // 卸载
    onBeforeUnmount(() => {
        panelLayoutStore.$clear();
        panelLayoutStore.$dispose();
        pageDesignerStore.$clear();
        pageDesignerStore.$dispose();
    });
</script>

<style scoped>
    :deep(.el-tabs) {
        height: 100%;
    }

    :deep(.el-tab-pane) {
        height: 100%;
    }
</style>
