<template>
    <div ref="pageSectionCanvasRef" class="relative w-full h-full pointer-events-none">
        <div class="relative" :style="contentStyle">
            <div
                class="absolute w-full h-full"
                :class="[
                    {
                        'overflow-x-auto overflow-y-hidden whitespace-nowrap scrollbar-hide': isScrollableMode,
                    },
                ]"
                ref="cellsContainerRef"
                @wheel.prevent="handleWheel"
                :id="`page-section-${pageSection.code}`">
                <div
                    v-for="cell in props.pageSection.pageCellList"
                    :key="cell.code"
                    class="absolute pointer-events-auto z-50 transition-all duration-200 group"
                    :class="cellClass(cell)"
                    @mousedown.stop="
                        pageDesignerStore.batchEditMode ? toggleCellSelection(cell) : onClickCell(cell.code)
                    "
                    :style="cellStyle(cell)"
                    @dragenter.prevent="onCellDragEnter($event, cell.code)"
                    @dragover.prevent="onCellDragOver($event, cell.code)"
                    @dragleave.prevent="onCellDragLeave($event, cell.code)"
                    @drop.prevent="onCellDrop($event, cell)">
                    <page-cell-canvas-editor :cell="cell" :pageSection="props.pageSection" />

                    <div
                        v-if="
                            pageDesignerStore.selectedPageCell?.code === cell.code && !pageDesignerStore.batchEditMode
                        "
                        class="absolute inset-0 bg-white opacity-40 transition-opacity duration-300 rounded-lg z-[51]"></div>

                    <div
                        v-if="isDraggingOver === cell.code && isComponentStyleDrag"
                        class="absolute inset-0 flex items-center justify-center bg-blue-500/20 z-[53]">
                        <div class="bg-white px-3 py-1.5 rounded shadow text-sm font-medium text-blue-600">
                            放置组件样式到此坑位
                        </div>
                    </div>

                    <div
                        class="absolute top-0 right-0 h-7 w-full bg-gray-200/80 text-black text-right text-base cursor-pointer z-[54]">
                        <div class="whitespace-nowrap overflow-auto scrollbar-hide h-full mr-1">
                            {{ ratio(cell) }}
                        </div>
                    </div>

                    <div
                        v-if="
                            pageDesignerStore.selectedPageCell?.code === cell.code &&
                            props.pageSection.delFlag === 0 &&
                            !pageDesignerStore.batchEditMode &&
                            isDraggingOver !== cell.code
                        "
                        class="absolute inset-0 flex items-center justify-center z-[52] transition-opacity duration-200">
                        <div class="w-full flex justify-evenly gap-2">
                            <el-tooltip v-if="isScrollableMode" content="删除坑位">
                                <el-button
                                    v-if="isScrollableMode"
                                    :disabled="
                                        !canDelete(cell) ||
                                        !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.DELETE, {
                                            type: 'org',
                                            value: cell.orgId,
                                        })
                                    "
                                    type="danger"
                                    circle
                                    icon="Delete"
                                    @mousedown.stop="handleDeleteCell(cell.code)" />
                            </el-tooltip>

                            <el-tooltip content="批量设置坑位样式">
                                <el-button
                                    :disabled="
                                        !canEdit(cell) ||
                                        !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT_STYLE, {
                                            type: 'org',
                                            value: cell.orgId,
                                        })
                                    "
                                    type="primary"
                                    circle
                                    icon="CopyDocument"
                                    @mousedown.stop="startBatchEdit(cell)" />
                            </el-tooltip>

                            <el-tooltip content="送审坑位">
                                <el-button
                                    :disabled="
                                        !canAudit(cell) ||
                                        !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.AUDIT, {
                                            type: 'org',
                                            value: cell.orgId,
                                        })
                                    "
                                    type="warning"
                                    circle
                                    @mousedown.stop="handlePublishCell(cell.code)">
                                    <template #icon>
                                        <i-mdi-share class="w-4 h-4" />
                                    </template>
                                </el-button>
                            </el-tooltip>
                        </div>
                    </div>
                </div>

                <div
                    v-if="
                        props.pageSection.delFlag === 0 &&
                        isScrollableMode &&
                        permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.ADD_CELL, {
                            type: 'org',
                            value: pageSection.orgId,
                        })
                    "
                    class="absolute cursor-pointer pointer-events-auto z-50"
                    @mousedown.stop="handleAddCell"
                    :style="addCellButtonStyle">
                    <div
                        class="flex items-center justify-center w-full h-full border-2 border-dashed border-gray-200 cursor-pointer rounded bg-white/70 hover:bg-gray-100/90 hover:border-gray-400 text-2xl text-gray-600 hover:text-gray-800 transition-colors">
                        +
                    </div>
                </div>

                <div
                    v-if="isScrollableMode"
                    class="absolute top-0 left-0 opacity-0 cursor-grab pointer-events-auto"
                    :style="scrollOverlayStyle"></div>
            </div>

            <div v-if="isScrollableMode && showScrollButtons" class="absolute inset-0 pointer-events-none">
                <el-button
                    v-if="canScrollLeft"
                    class="absolute left-2 top-1/2 transform -translate-y-1/2 pointer-events-auto z-[60] shadow-lg"
                    type="primary"
                    circle
                    size="small"
                    @click="scrollLeft">
                    <template #icon>
                        <el-icon><ArrowLeft /></el-icon>
                    </template>
                </el-button>

                <el-button
                    v-if="canScrollRight"
                    class="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-auto z-[60] shadow-lg"
                    type="primary"
                    circle
                    size="small"
                    @click="scrollRight">
                    <template #icon>
                        <el-icon><ArrowRight /></el-icon>
                    </template>
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, type CSSProperties, ref, onMounted, onUnmounted } from 'vue';
    import { ComponentStyle, DesignMode, PageCell, PageSection } from '@smartdesk/common/types';
    import { useFeedback } from '@smartdesk/common/composables';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import PageCellCanvasEditor from './page-cell-canvas-editor.vue';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { canAudit, canDelete, canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue';

    // 页面设计器页面楼层画布编辑器
    defineOptions({
        name: 'PageSectionCanvasEditor',
    });

    // 参数
    const props = defineProps<{
        pageSection: PageSection;
    }>();

    // 事件
    const emit = defineEmits<{
        (e: 'deleteCell', sectionCode: string, cellCode: string): void;
        (e: 'publishCell', sectionCode: string, cellCode: string): void;
    }>();

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const feedback = useFeedback();
    const permissionStore = usePermissionStore();

    // 样式配置器引用
    const pageSectionCanvasRef = ref<HTMLElement | null>(null);
    const cellsContainerRef = ref<HTMLElement | null>(null);

    const selectedCellCode = ref<string>('');

    // 滚动相关状态
    const canScrollLeft = ref<boolean>(false);
    const canScrollRight = ref<boolean>(false);
    const showScrollButtons = ref<boolean>(false);

    // 是否显示页面楼层标题的响应式引用
    const background = computed(() => {
        return props.pageSection.layout?.props?.background ?? '';
    });

    // 是否是滚动模式
    const isScrollableMode = computed(() => props.pageSection.layout.mode === DesignMode.SCROLLABLE);

    // 内容样式
    const contentStyle = computed<CSSProperties>(() => {
        return {
            width: `${props.pageSection.layout.rect.width}px`,
            height: `${props.pageSection.layout.rect.height}px`,
            position: 'relative',
        };
    });

    // 楼层标题样式
    const sectionTitleStyle = computed(() => {
        return {
            width: `${props.pageSection.layout.rect.width}px`,
            height: `30px`,
        };
    });

    // 坑位样式
    const cellStyle = (cell: PageCell): CSSProperties => {
        return {
            top: `${cell.layout.rect.top}px`,
            left: `${cell.layout.rect.left}px`,
            width: `${cell.layout.rect.width}px`,
            height: `${cell.layout.rect.height}px`,
            position: 'absolute',
            zIndex: '51',
        };
    };

    // 坑位类
    const cellClass = (cell: PageCell) => {
        /**
         * 坑位的几种状态
         * 1、非批量模式下，没有被选中：ring-2 ring-gray-200
         * 2、非批量模式下，被选中：ring-2 ring-blue-500
         * 3、批量模式下，可被选择：ring-2 ring-gray-200 cursor-pointer
         * 4、批量模式下，不可被选择：ring-2 ring-yellow-300/50 cursor-not-allowed
         * 5、批量模式下，已被选择：ring-2 ring-blue-500 cursor-pointer
         * 6、接受组件样式拖拽：ring-2 ring-green-500
         * */
        return {
            'ring-2 ring-gray-200':
                !pageDesignerStore.batchEditMode && pageDesignerStore.selectedPageCell?.code !== cell.code,
            'ring-2 ring-blue-500':
                !pageDesignerStore.batchEditMode && pageDesignerStore.selectedPageCell?.code === cell.code,
            'ring-2 ring-gray-200 cursor-pointer': pageDesignerStore.batchEditMode && !isSelectedForBatch(cell),
            'ring-2 ring-yellow-300/50 cursor-not-allowed': pageDesignerStore.batchEditMode && !canSelectForBatch(cell),
            'ring-2 ring-blue-500 cursor-pointer': pageDesignerStore.batchEditMode && isSelectedForBatch(cell),
            'ring-2 ring-green-500': isDraggingOver.value === cell.code,
        };
    };

    // 坑位宽高比
    const ratio = (cell: PageCell) => {
        if (!cell.layout.rect.width || !cell.layout.rect.height) return '';
        return Math.trunc(cell.layout.rect.width) + ' / ' + Math.trunc(cell.layout.rect.height);
    };

    // 点击坑位
    const onClickCell = (cellCode: string) => {
        selectedCellCode.value = cellCode;
        pageDesignerStore.switchElement('Cell', cellCode);
    };

    // 开始批量编辑
    const startBatchEdit = (cell: PageCell) => {
        selectedCellCode.value = cell.code;
        pageDesignerStore.switchElement('Cell', cell.code);

        // 进入批量编辑模式
        pageDesignerStore.batchEditMode = true;

        // 清空之前的选择
        pageDesignerStore.batchEditCells = [];

        // 添加当前坑位到选择列表
        pageDesignerStore.batchEditCells.push(cell);

        // tab 到样式编辑器
        pageDesignerStore.switchTab('right', 'props');
    };

    // 判断是否可以选择该坑位进行批量编辑
    const canSelectForBatch = (cell: PageCell): boolean => {
        // 有坑位的样式编辑权限即可，不要求组件类型一致
        return (
            canEdit(cell) &&
            permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT_STYLE, {
                type: 'org',
                value: cell.orgId,
            })
        );
    };

    // 判断坑位是否已被选中
    const isSelectedForBatch = (cell: PageCell): boolean => {
        return pageDesignerStore.batchEditCells.some((selectedCell) => selectedCell.code === cell.code);
    };

    // 切换坑位选择状态
    const toggleCellSelection = (cell: PageCell) => {
        if (!canSelectForBatch(cell)) return;

        const index = pageDesignerStore.batchEditCells.findIndex((selectedCell) => selectedCell.code === cell.code);

        if (index === -1) {
            // 添加到选择列表
            pageDesignerStore.batchEditCells.push(cell);
        } else {
            // 从选择列表中移除
            pageDesignerStore.batchEditCells.splice(index, 1);
        }
    };

    // 处理滚轮事件
    const handleWheel = (e: WheelEvent) => {
        if (!isScrollableMode.value || !cellsContainerRef.value) return;

        // 如果按下Shift键，将垂直滚动转换为水平滚动
        const deltaX = e.shiftKey ? e.deltaY : e.deltaX;
        cellsContainerRef.value.scrollLeft += deltaX;

        // 更新滚动按钮状态
        updateScrollButtonsState();
    };

    // 更新滚动按钮状态
    const updateScrollButtonsState = () => {
        if (!isScrollableMode.value || !cellsContainerRef.value) {
            canScrollLeft.value = false;
            canScrollRight.value = false;
            showScrollButtons.value = false;
            return;
        }

        const container = cellsContainerRef.value;
        const scrollLeft = container.scrollLeft;
        const scrollWidth = container.scrollWidth;
        const clientWidth = container.clientWidth;

        // 检查是否需要显示滚动按钮
        showScrollButtons.value = scrollWidth > clientWidth;

        // 检查是否可以向左滚动
        canScrollLeft.value = scrollLeft > 0;

        // 检查是否可以向右滚动
        canScrollRight.value = scrollLeft < scrollWidth - clientWidth - 1;
    };

    // 向左滚动
    const scrollLeft = () => {
        if (!cellsContainerRef.value) return;

        const scrollAmount = 200; // 每次滚动的距离
        cellsContainerRef.value.scrollBy({
            left: -scrollAmount,
            behavior: 'smooth',
        });
    };

    // 向右滚动
    const scrollRight = () => {
        if (!cellsContainerRef.value) return;

        const scrollAmount = 200; // 每次滚动的距离
        cellsContainerRef.value.scrollBy({
            left: scrollAmount,
            behavior: 'smooth',
        });
    };

    // 滚动模式下：新增坑位的样式
    const addCellButtonStyle = computed<CSSProperties>(() => {
        const lastCell = props.pageSection.pageCellList[props.pageSection.pageCellList.length - 1];
        const leftPosition = lastCell ? lastCell.layout.rect.left + lastCell.layout.rect.width + 10 : 10;

        const width = props.pageSection.layout.standardSize?.width || 200;
        const height = props.pageSection.layout.standardSize?.height || 180;

        return {
            position: 'absolute',
            left: `${leftPosition}px`,
            top: '50%',
            transform: 'translateY(-50%)',
            width: `${width}px`,
            height: `${height}px`,
        };
    });

    // 滚动模式下：处理添加坑位
    const handleAddCell = async () => {
        const confirmRes = await feedback.confirm('确定在楼层：' + props.pageSection.name + ' 新增坑位？');
        if (confirmRes) {
            await pageDesignerStore.createPageCell(props.pageSection.code);
        }
    };

    // 滚动模式下，滚动遮罩
    const scrollOverlayStyle = computed<CSSProperties>(() => {
        const lastCell = props.pageSection.pageCellList[props.pageSection.pageCellList.length - 1];
        const btnWidth = 60;

        let rightEdge = btnWidth + 20;
        if (lastCell) {
            rightEdge = lastCell.layout.rect.left + lastCell.layout.rect.width + btnWidth + 20;
        }

        return {
            position: 'absolute',
            top: 0,
            left: 0,
            width: `${rightEdge}px`,
            height: '100%',
            zIndex: 50,
        };
    });

    // 送审坑位
    const handlePublishCell = (cellCode: string) => {
        selectedCellCode.value = cellCode;
        pageDesignerStore.switchElement('Cell', cellCode);

        emit('publishCell', props.pageSection.code, cellCode);
    };

    // 删除坑位
    const handleDeleteCell = (cellCode: string) => {
        selectedCellCode.value = cellCode;
        pageDesignerStore.switchElement('Cell', cellCode);

        emit('deleteCell', props.pageSection.code, cellCode);
    };

    // 处理对话框关闭
    const handleDialogClose = () => {
        // 对话框关闭但不退出批量编辑模式，用户可以继续选择
    };

    // 拖拽相关的事件处理函数
    const isDraggingOver = ref<string>('');
    const isComponentStyleDrag = ref<boolean>(false);

    const onCellDragEnter = (event: DragEvent, cellCode: string) => {
        // 确认是组件样式拖拽而不是其他类型
        const types = event.dataTransfer?.types || [];
        const isComponentStyle = types.some(
            (type) =>
                type.toLowerCase().includes('componentstyle') || type.toLowerCase().includes('json+componentstyle')
        );

        if (!isComponentStyle) return;

        event.preventDefault();
        isDraggingOver.value = cellCode;
        isComponentStyleDrag.value = true;
    };

    const onCellDragOver = (event: DragEvent, cellCode: string) => {
        // 确认是组件样式拖拽而不是其他类型
        const types = event.dataTransfer?.types || [];
        const isComponentStyle = types.some(
            (type) =>
                type.toLowerCase().includes('componentstyle') || type.toLowerCase().includes('json+componentstyle')
        );

        if (!isComponentStyle) return;

        event.preventDefault();
        isDraggingOver.value = cellCode;
        isComponentStyleDrag.value = true;
    };

    const onCellDragLeave = (event: DragEvent, cellCode: string) => {
        event.preventDefault();
        // 检查鼠标是否真的离开了元素
        const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();

        if (
            event.clientX < rect.left ||
            event.clientX > rect.right ||
            event.clientY < rect.top ||
            event.clientY > rect.bottom
        ) {
            isDraggingOver.value = '';
            isComponentStyleDrag.value = false;
        }
    };

    const onCellDrop = async (event: DragEvent, cell: PageCell) => {
        event.preventDefault();
        // 重置拖拽状态
        isDraggingOver.value = '';
        isComponentStyleDrag.value = false;

        // 判断有否有权限
        if (
            !canEdit(cell) ||
            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT_STYLE, {
                type: 'org',
                value: cell.orgId,
            })
        ) {
            feedback.warning('无权限设置样式');
            return;
        }

        // 确认是组件样式拖拽而不是其他类型
        const types = event.dataTransfer?.types || [];
        const isComponentStyle = types.some(
            (type) =>
                type.toLowerCase().includes('componentstyle') || type.toLowerCase().includes('json+componentstyle')
        );

        if (!isComponentStyle) return;

        // 获取组件样式数据
        const componentStyleStr = event.dataTransfer?.getData('componentStyle');
        if (!componentStyleStr) return;

        try {
            // 解析组件样式数据
            const componentStyle: ComponentStyle = JSON.parse(componentStyleStr);

            // 切换到对应坑位
            pageDesignerStore.switchElement('Cell', cell.code);

            const confirmRes = await feedback.confirm(
                '确定更换坑位：' +
                    pageDesignerStore.selectedElement.name +
                    ' 的组件为：' +
                    componentStyle.name +
                    ' 吗？'
            );
            if (confirmRes) {
                // 更新组件样式
                await pageDesignerStore.updateCellComponent(componentStyle, true);
            }
        } catch (err) {
            feedback.error('组件更新失败');
        }
    };

    // 滚动事件监听器
    const handleScroll = () => {
        updateScrollButtonsState();
    };

    // 组件挂载时初始化
    onMounted(() => {
        if (cellsContainerRef.value) {
            cellsContainerRef.value.addEventListener('scroll', handleScroll);
            // 初始化滚动按钮状态
            setTimeout(updateScrollButtonsState, 100);
        }
    });

    // 组件卸载时清理事件监听器
    onUnmounted(() => {
        if (cellsContainerRef.value) {
            cellsContainerRef.value.removeEventListener('scroll', handleScroll);
        }
    });
</script>

<style scoped>
    /* 隐藏滚动条 */
    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }
</style>
