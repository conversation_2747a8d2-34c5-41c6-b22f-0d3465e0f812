<template>
    <div class="w-full flex items-center justify-end">
        <action-button
            :disabled="
                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                !canEdit(item) ||
                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                    type: 'org',
                    value: pageDesignerStore.selectedPageCell.orgId,
                })
            "
            v-if="item.id"
            link
            type="warning"
            text-class="text-sm"
            text="送审坑位元素"
            @click="handlePublish">
            <template #icon>
                <i-mdi-share class="mr-2 w-4 h-4" />
            </template>
        </action-button>
        <action-button
            :disabled="
                !canDelete(item) ||
                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                    type: 'org',
                    value: pageDesignerStore.selectedPageCell.orgId,
                })
            "
            v-if="item.id"
            icon="Delete"
            link
            type="danger"
            text-class="text-sm"
            text="删除坑位元素"
            @click="handleDelete" />
    </div>

    <el-alert v-if="item.auditStatus === 0" title="当前编辑信息未送审" type="warning" show-icon :closable="false" />

    <el-form label-width="100px" :rules="rules" :model="item" ref="formRef" class="mt-4">
        <el-collapse v-model="activeCollapse" :accordion="false">
            <el-collapse-item v-if="showStrategy && !item.defaultFlag" name="displayStrategy">
                <template #icon>
                    <div class="flex items-center gap-2 ml-5 w-full">
                        <span class="text-lg">展示策略</span>
                    </div>
                </template>

                <div class="mt-4">
                    <el-form-item label="策略信息：" prop="strategy">
                        <div class="flex items-center justify-between w-full">
                            <el-tooltip :content="getPersonalRuleName(item.ruleCode)">
                                <div class="break-words whitespace-nowrap overflow-hidden text-ellipsis">
                                    {{ getPersonalRuleName(item.ruleCode) }}
                                </div>
                            </el-tooltip>
                            <el-button
                                :disabled="
                                    isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                    !canEdit(item) ||
                                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                        type: 'org',
                                        value: pageDesignerStore.selectedPageCell.orgId,
                                    })
                                "
                                link
                                type="primary"
                                @click="handleChoosePersonalRule">
                                选择
                            </el-button>
                        </div>
                    </el-form-item>

                    <el-form-item label="是否反选：" prop="inverted">
                        <el-switch
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.inverted"
                            size="default"
                            style="width: 100%" />
                    </el-form-item>

                    <el-form-item label="紧急程度：" prop="orderNo">
                        <el-select
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.orderNo"
                            placeholder="请选择"
                            clearable>
                            <el-option
                                v-for="option in urgencyOptions"
                                :key="option.code"
                                :label="option.name"
                                :value="Number(option.code)"></el-option>
                        </el-select>
                    </el-form-item>
                </div>
            </el-collapse-item>

            <el-collapse-item name="baseInfo">
                <template #icon>
                    <div class="flex items-center gap-2 ml-5 w-full">
                        <span class="text-lg">基本信息</span>
                    </div>
                </template>

                <div class="mt-2">
                    <el-form-item label="关联对象：" prop="dataType">
                        <el-select
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.dataType"
                            placeholder="请选择"
                            clearable
                            @click="getLinkTypes"
                            @change="handleDataTypeChange(item)">
                            <el-option
                                v-for="option in dataTypeOptions"
                                :key="option.code"
                                :label="option.name"
                                :value="option.code"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="关联内容：" prop="dataCode">
                        <div class="flex items-center justify-between w-full">
                            <el-tooltip :content="item.dataName">
                                <div class="break-words whitespace-nowrap overflow-hidden text-ellipsis">
                                    {{ item.dataName }}
                                </div>
                            </el-tooltip>
                            <el-button
                                :disabled="
                                    isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                    !canEdit(item) ||
                                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                        type: 'org',
                                        value: pageDesignerStore.selectedPageCell.orgId,
                                    })
                                "
                                link
                                type="primary"
                                @click="handleChoose">
                                选择
                            </el-button>
                        </div>
                    </el-form-item>

                    <el-form-item label="内容名称：" prop="dataName">
                        <el-input
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.dataName"
                            placeholder="请输入" />
                    </el-form-item>

                    <el-form-item label="显示标题：" prop="title">
                        <el-input
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.title"
                            placeholder="请输入" />
                    </el-form-item>

                    <el-form-item label="副标题：" prop="subTitle">
                        <el-input
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.subTitle"
                            placeholder="请输入" />
                    </el-form-item>

                    <el-form-item label="链接类型：" prop="linkType" :rules="linkTypeRules">
                        <el-select
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.linkType"
                            placeholder="请选择"
                            clearable>
                            <el-option
                                v-for="option in linkTypeOptions"
                                :key="option.id"
                                :label="option.name"
                                :value="option.code"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item
                        v-for="param in currentExtraParams"
                        :key="param.name"
                        :label="'链接参数-' + param.nameExplain + '：'"
                        :prop="'extraParams.' + param.name"
                        :rules="
                            param.required
                                ? [
                                      {
                                          required: true,
                                          message: `请输入${param.nameExplain}`,
                                          trigger: ['blur', 'change'],
                                      },
                                  ]
                                : []
                        ">
                        <el-input
                            v-if="param.type === 'text'"
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.extraParams[param.name]"
                            :placeholder="`请输入${param.nameExplain}`"
                            clearable />
                        <el-input-number
                            v-else-if="param.type === 'number'"
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.extraParams[param.name]"
                            :placeholder="`请输入${param.nameExplain}`"
                            :min="0"
                            style="width: 100%" />
                    </el-form-item>

                    <el-form-item
                        v-for="imageType in pageCellItemImageTypes"
                        :key="imageType.code"
                        :label="imageType.name + '：'"
                        :prop="'icons.' + imageType.code">
                        <image-editor
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.icons[imageType.code]" />
                    </el-form-item>

                    <el-form-item label="基础角标：" prop="baseCorner">
                        <el-select
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.baseCorner"
                            placeholder="请选择"
                            clearable
                            @click="getCornerMarks">
                            <el-option
                                v-for="option in baseCornerMarkList"
                                :key="option.code"
                                :label="option.name"
                                :value="option.code"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="运营角标：" prop="opCorner">
                        <el-select
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model="item.opCorner"
                            placeholder="请选择"
                            clearable
                            @click="getCornerMarks">
                            <el-option
                                v-for="option in opCornerMarkList"
                                :key="option.code"
                                :label="option.name"
                                :value="option.code"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item v-if="!item.defaultFlag" label="有效期：" prop="validTime">
                        <base-date-picker
                            :disabled="
                                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                                !canEdit(item) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                    type: 'org',
                                    value: pageDesignerStore.selectedPageCell.orgId,
                                })
                            "
                            v-model:start-date="item.validTime"
                            v-model:end-date="item.expireTime"
                            type="datetimerange"
                            start-placeholder="生效时间"
                            end-placeholder="失效时间" />
                    </el-form-item>

                    <el-form-item v-if="!item.defaultFlag" label="频次：" prop="scheduleRuleCode">
                        <el-select
                            v-model="item.scheduleRule.scheduleConfig.scheduleType"
                            placeholder="请选择"
                            clearable
                            @change="handleScheduleTypeChange(item)"
                            class="mb-2">
                            <el-option
                                v-for="option in scheduleTypeList"
                                :key="option.value"
                                :label="option.label"
                                :value="option.value"></el-option>
                        </el-select>

                        <base-checkbox
                            v-if="item.scheduleRule.scheduleConfig.scheduleType !== 0"
                            v-model:model-value="item.scheduleRule.scheduleConfig.dayList"
                            :options="
                                item.scheduleRule.scheduleConfig.scheduleType === 1
                                    ? weekList
                                    : item.scheduleRule.scheduleConfig.scheduleType === 2
                                      ? dayList
                                      : []
                            "></base-checkbox>

                        <el-config-provider :locale="zhCn">
                            <el-time-picker
                                :model-value="getTimeRange(item)"
                                @update:model-value="(val: any) => handleTimeRangeChange(item, val)"
                                is-range
                                range-separator="-"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                class="mt-2" />
                        </el-config-provider>
                    </el-form-item>
                </div>
            </el-collapse-item>
        </el-collapse>
    </el-form>

    <div class="flex items-center justify-end gap-2 mt-4">
        <el-button
            :disabled="
                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                !canEdit(item) ||
                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                    type: 'org',
                    value: pageDesignerStore.selectedPageCell.orgId,
                })
            "
            size="default"
            @click="handleCancel">
            取消
        </el-button>
        <el-button
            :disabled="
                isRefSectionCellItem(pageDesignerStore.page, pageDesignerStore.selectedPageCell) ||
                !canEdit(item) ||
                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                    type: 'org',
                    value: pageDesignerStore.selectedPageCell.orgId,
                })
            "
            size="default"
            type="primary"
            @click="handleSave">
            保存
        </el-button>
    </div>

    <base-modal
        v-model="contentDialogVisible"
        :title="contentDialogTitle"
        width="60%"
        destroy-on-close
        confirm-text="保存"
        @cancel="handleCancelContent"
        @confirm="handleSaveContent">
        <content-selector :content-type="item.dataType" ref="contentContainerRef" />
    </base-modal>

    <personal-rule-dialog
        v-model="item.ruleCode"
        v-model:dialog-visible="personalRuleDialogVisible"
        v-if="personalRuleDialogVisible" />
</template>

<script setup lang="ts">
    import { computed, onMounted, PropType, reactive, ref, watch } from 'vue';
    import { FormInstance, FormRules } from 'element-plus';
    import { Enumeration, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import { cmsApi, linkTypeApi } from '@smartdesk/common/api';
    import { EpgShowFlag, LinkType, PageCellItem, PersonalRule, ScheduleRule } from '@smartdesk/common/types';
    import ContentSelector from '../selector/content-selector.vue';
    import { canDelete, canEdit, DESIGN_BIZ_PERMISSION, isRefSectionCellItem } from '@smartdesk/common/permission';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import dayjs from 'dayjs';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';
    import { dayList, scheduleTypeList, weekList } from '@smartdesk/common/constant';
    import { useFeedback } from '@smartdesk/common/composables';

    // 坑位元素配置表单
    defineOptions({
        name: 'PageCellItemConfigFormEditor',
    });

    // Props
    const props = defineProps({
        item: {
            type: Object as PropType<PageCellItem>,
            required: true,
        },
        showStrategy: {
            type: Boolean,
            default: true,
        },
        personalRules: {
            type: Object as PropType<PersonalRule[]>,
            required: true,
        },
    });

    // 事件
    const emit = defineEmits<{
        (e: 'publish', item: PageCellItem): void;
        (e: 'delete', item: PageCellItem): void;
        (e: 'cancel', item: PageCellItem): void;
        (e: 'save', item: PageCellItem): void;
    }>();

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const enumStore = useEnumStore();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 图片类型
    const pageCellItemImageTypes = ref<Enumeration[]>(enumStore.getEnumsByKey('pageCellItemImageTypeEnum') || []);

    // 关联对象
    const dataTypeOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('relevanceContentEnum') || []);

    // 紧急程度枚举
    const urgencyOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('urgency') || []);

    // 链接类型选项
    const linkTypeOptions = ref<LinkType[]>([]);

    // 角标列表
    const cornerMarkList = ref<EpgShowFlag[]>([]);

    // 基础角标列表
    const baseCornerMarkList = computed(() => {
        return cornerMarkList.value.filter((item) => item.type === 1);
    });

    // 运营角标列表
    const opCornerMarkList = computed(() => {
        return cornerMarkList.value.filter((item) => item.type === 2);
    });

    // 当前展开：基本信息、推荐策略
    const activeCollapse = ref<string[]>(['displayStrategy', 'baseInfo']);

    // 默认频次配置
    const defaultScheduleRule = ref<ScheduleRule>({
        scheduleConfig: {
            scheduleType: 1,
            dayList: [],
            startTime: '',
            endTime: '',
        },
    } as unknown as ScheduleRule);

    // 表单
    const formRef = ref<FormInstance | null>(null);

    // 表单规则
    const rules = reactive<FormRules>({
        dataCode: [
            {
                required: true,
                message: '请选择关联内容',
                trigger: ['blur', 'change'],
            },
        ],
        dataName: [
            {
                required: true,
                message: '请输入内容名称',
                trigger: ['blur', 'change'],
            },
        ],
        title: [
            {
                required: true,
                message: '请输入显示标题',
                trigger: ['blur', 'change'],
            },
        ],
        dataType: [
            {
                required: true,
                message: '请选择关联对象',
                trigger: ['blur', 'change'],
            },
        ],
        'icons.icon': [
            {
                required: true,
                message: '请选择海报图',
                trigger: ['blur', 'change'],
            },
        ],
        validTime: [
            {
                required: true,
                message: '请选择有效期',
                trigger: ['blur', 'change'],
            },
        ],
        orderNo: [
            {
                required: true,
                message: '请输入优先级',
                trigger: ['blur', 'change'],
            },
        ],
    });

    // 链接类型校验规则
    const linkTypeRules = computed(() => {
        if (linkTypeOptions.value.length > 0) {
            return [
                {
                    required: true,
                    message: '请选择链接类型',
                    trigger: ['blur', 'change'],
                },
            ];
        }

        // 当前没有链接类型时，不进行校验，并主动清除错误提示
        formRef.value?.clearValidate('linkType');
        return [];
    });

    // 获取频次的时间段
    const getTimeRange = (item: PageCellItem): [Date, Date] => {
        const { startTime, endTime } = item.scheduleRule.scheduleConfig;
        if (!startTime || !endTime)
            return [
                dayjs().startOf('day').hour(0).minute(0).second(0).toDate(),
                dayjs().endOf('day').hour(23).minute(59).second(59).toDate(),
            ];

        const today = new Date().toDateString();
        return [new Date(`${today} ${startTime}`), new Date(`${today} ${endTime}`)];
    };

    // 处理频次时间段变化
    const handleTimeRangeChange = (item: PageCellItem, val: [Date, Date]) => {
        item.scheduleRule.scheduleConfig.startTime = dayjs(val[0]).format('HH:mm:ss');
        item.scheduleRule.scheduleConfig.endTime = dayjs(val[1]).format('HH:mm:ss');
    };

    // 处理频次类型变化
    const handleScheduleTypeChange = (item: PageCellItem) => {
        item.scheduleRule.scheduleConfig.dayList = [];
    };

    // 获取角标列表
    const getCornerMarks = async () => {
        const res = await cmsApi.searchCorner({ types: [1, 2] }, { paged: false });
        if (res.code === 200) {
            cornerMarkList.value = res.result;
        }
    };

    // 获取推荐策略名称
    const getPersonalRuleName = (ruleCode: string) => {
        const rule = props.personalRules.find((rule) => rule.code === ruleCode);
        return rule ? rule.name : '';
    };

    // 处理关联对象变化
    const handleDataTypeChange = (item: PageCellItem) => {
        // 如果关联对象变化，清空相关属性
        item.dataCode = '';
        item.dataName = '';
        item.title = '';
        item.linkType = '';
        item.extraParams = {};
    };

    // 获取链接类型
    const getLinkTypes = async () => {
        if (props.item && props.item.dataType) {
            let type = props.item.dataType;
            if (type == 'episode2' || type == 'episode') {
                type = 'movie';
            }
            const res = await linkTypeApi.getLinkTypes({ dataType: type }, { paged: false });
            if (res.code === 200) {
                linkTypeOptions.value = res.result;
            }
        }
    };

    // 送审当前坑位元素
    const handlePublish = (): void => {
        emit('publish', props.item);
    };

    // 删除当前坑位元素
    const handleDelete = (): void => {
        emit('delete', props.item);
    };

    // 内容选择对话框
    const contentDialogVisible = ref<boolean>(false);
    const contentContainerRef = ref<any>();
    const contentDialogTitle = computed(() => {
        return `选择${dataTypeOptions.value.find((item) => item.code === props.item.dataType)?.name || ''}`;
    });

    // 选择关联对象
    const handleChoose = () => {
        if (!props.item.dataType) {
            feedback.error('请选择关联对象');
            return;
        }
        contentDialogVisible.value = true;
    };

    // 选择推荐策略
    const personalRuleDialogVisible = ref<boolean>(false);
    const handleChoosePersonalRule = () => {
        personalRuleDialogVisible.value = true;
    };

    // 保存选择的内容
    const handleSaveContent = async () => {
        const content = contentContainerRef.value?.selectedContent;

        if (!content) {
            contentDialogVisible.value = false;
            return;
        }

        try {
            // 根据关联对象类型，更新坑位元素的内容
            switch (props.item.dataType) {
                case 'category':
                    props.item.dataName = content.node.name || content.node.title;
                    props.item.dataCode = content.node.code;
                    props.item.title = content.node.title;
                    props.item.icons['icon'] = content.node.categoryIcon;
                    props.item.icons['background'] = content.node.background;
                    break;
                case 'desktop':
                    props.item.dataName = content.name;
                    props.item.dataCode = content.code;
                    props.item.title = content.name;
                    props.item.icons['icon'] = content.icon;
                    break;
                case 'page':
                    props.item.dataName = content.name;
                    props.item.dataCode = content.code;
                    props.item.title = content.name;
                    props.item.icons['icon'] = content.icon;
                    break;
                default:
                    props.item.dataName = content.name || content.title;
                    props.item.dataCode = content.code;
                    props.item.title = content.title;
                    props.item.icons['icon'] = content.poster;
                    props.item.icons['background'] = content.background;
                    break;
            }
        } finally {
            contentDialogVisible.value = false;
        }
    };

    // 取消选择内容
    const handleCancelContent = () => {
        contentDialogVisible.value = false;
    };

    // 取消
    const handleCancel = (): void => {
        emit('cancel', props.item);
    };

    // 保存
    const handleSave = async (): Promise<void> => {
        if (!formRef.value) return;

        try {
            // 表单校验
            await formRef.value.validate();
            emit('save', props.item);
        } catch (error) {
            feedback.error('表单验证失败，请检查必填项');
        }
    };

    // 获取当前 linkType 对应的 extraParams
    const currentExtraParams = computed(() => {
        const current = linkTypeOptions.value?.find((opt) => opt.code === props.item.linkType);
        return current?.extraParams || [];
    });

    // linkType 变化时，自动赋默认值
    watch(
        () => props.item.linkType,
        () => {
            // 确保extraParams是一个对象
            if (!props.item.extraParams) {
                props.item.extraParams = {};
            }

            currentExtraParams.value.forEach((param) => {
                if (props.item.extraParams[param.name] === undefined) {
                    props.item.extraParams[param.name] = param.value ?? '';
                }
            });
        },
        { immediate: true }
    );

    // 监听dataType变化时自动获取链接类型
    watch(
        () => props.item.dataType,
        (newVal) => {
            if (newVal) {
                getLinkTypes();
            }
        }
    );

    // 确保折叠面板始终保持展开状态
    watch(activeCollapse, (newVal) => {
        if (!newVal.includes('displayStrategy')) {
            activeCollapse.value.push('displayStrategy');
        }
        if (!newVal.includes('baseInfo')) {
            activeCollapse.value.push('baseInfo');
        }
    });

    // 初始化
    onMounted(() => {
        // 初始加载链接类型
        getLinkTypes();

        // 加载角标
        getCornerMarks();

        // 确保折叠面板展开
        activeCollapse.value = ['displayStrategy', 'baseInfo'];

        // 保证频次不为空
        if (!props.item.scheduleRule) {
            props.item.scheduleRule = defaultScheduleRule.value;
        }
    });
</script>
