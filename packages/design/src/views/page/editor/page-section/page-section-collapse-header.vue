<template>
    <collapse-item-wrapper-header :is-active="isActive">
        <template #activeHeader>
            <div class="flex items-center gap-2">
                <el-icon>
                    <CaretBottom />
                </el-icon>
                <div class="text-lg max-w-full">{{ title }}</div>
                <div v-if="showStatus" class="flex items-center gap-2 text-sm">
                    <status-dot :type="item?.onlineStatus === 0 || item?.onlineStatus === 2 ? 'error' : 'success'">
                        {{ enumStore.getLabelByKeyAndValue('onlineStatus', item?.onlineStatus) }}
                    </status-dot>
                    <status-dot :type="item?.auditStatus === 2 ? 'success' : 'error'">
                        {{ enumStore.getLabelByKeyAndValue('auditStatus', item?.auditStatus) }}
                    </status-dot>
                </div>
            </div>
        </template>

        <template #deActiveHeader>
            <div class="flex items-center gap-2">
                <el-icon>
                    <CaretRight />
                </el-icon>
                <div class="text-lg max-w-full">{{ title }}</div>
            </div>
        </template>
    </collapse-item-wrapper-header>
</template>

<script setup lang="ts">
    import { PropType } from 'vue';
    import { CaretBottom, CaretRight } from '@element-plus/icons-vue';
    import { PublishStatus } from '@smartdesk/common/types';
    import { useEnumStore } from '@chances/portal_common_core';

    // 枚举
    const enumStore = useEnumStore();

    defineOptions({
        name: 'PageSectionCollapseHeader',
    });

    defineProps({
        isActive: {
            type: Boolean,
            required: true,
        },
        title: {
            type: String,
            required: true,
        },
        item: {
            type: Object as PropType<PublishStatus>,
            required: false,
        },
        showStatus: {
            type: Boolean,
            default: true,
        },
    });
</script>
