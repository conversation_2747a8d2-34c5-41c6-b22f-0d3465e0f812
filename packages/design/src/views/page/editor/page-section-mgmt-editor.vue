<template>
    <div class="p-[10px] h-full">
        <div class="flex items-center justify-end mb-4" style="height: 24px">
            <div class="flex items-center justify-end">
                <action-button
                    v-if="!sortMode"
                    :disabled="!canEdit(pageDesignerStore.page)"
                    icon="Sort"
                    link
                    type="primary"
                    text-class="text-sm"
                    text="排序"
                    @click="onClickSort" />
                <action-button
                    v-if="!sortMode"
                    :disabled="
                        !canEdit(pageDesignerStore.page) ||
                        !canBatchOnline(checkedPageSections) ||
                        !permissionStore.hasBatchPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.BATCH_ONLINE, {
                            type: 'org',
                            value: checkedPageSections
                                .filter((pageSection) => pageSection.orgId)
                                .map((pageSection) => pageSection.orgId),
                        })
                    "
                    icon="Top"
                    link
                    type="success"
                    text-class="text-sm"
                    text="上线"
                    @click="onClickOnline" />
                <action-button
                    v-if="!sortMode"
                    :disabled="
                        !canEdit(pageDesignerStore.page) ||
                        !canBatchOffline(checkedPageSections) ||
                        !permissionStore.hasBatchPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.BATCH_OFFLINE, {
                            type: 'org',
                            value: checkedPageSections
                                .filter((pageSection) => pageSection.orgId)
                                .map((pageSection) => pageSection.orgId),
                        })
                    "
                    icon="Bottom"
                    link
                    type="danger"
                    text-class="text-sm"
                    text="下线"
                    @click="onClickOffline" />
                <action-button
                    v-if="!sortMode"
                    :disabled="
                        !canEdit(pageDesignerStore.page) ||
                        !canBatchAudit(checkedPageSections) ||
                        !permissionStore.hasBatchPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.BATCH_AUDIT, {
                            type: 'org',
                            value: checkedPageSections
                                .filter((pageSection) => pageSection.orgId)
                                .map((pageSection) => pageSection.orgId),
                        })
                    "
                    icon="Promotion"
                    link
                    type="warning"
                    text-class="text-sm"
                    text="送审"
                    @click="onClickPublish" />
                <action-button
                    v-if="sortMode"
                    icon="Close"
                    link
                    type="default"
                    text-class="text-sm"
                    text="取消"
                    @click="onClickCancelSort" />
                <action-button
                    v-if="sortMode"
                    icon="Select"
                    link
                    type="primary"
                    text-class="text-sm"
                    text="保存"
                    @click="onClickSaveSort" />
            </div>
        </div>

        <div style="height: calc(100% - 80px); overflow: auto">
            <div v-if="!sortMode" class="overflow-hidden">
                <el-checkbox
                    v-model="checkAll"
                    :indeterminate="isIndeterminate"
                    @change="handleCheckAllChange"
                    class="mb-2"
                    size="default">
                    全选
                </el-checkbox>

                <el-checkbox-group
                    v-model="checkedPageSectionCodes"
                    @change="handleCheckedCitiesChange"
                    class="flex flex-col w-full mt-4"
                    size="default">
                    <el-checkbox
                        v-for="pageSection in pageSectionList"
                        :key="pageSection.code"
                        :label="pageSection.name"
                        :value="pageSection.code"
                        class="custom-checkbox mb-4">
                        <div class="w-full overflow-hidden">
                            <div class="flex items-center justify-between w-full">
                                <div class="min-w-0 mr-2">
                                    <el-tooltip
                                        :content="pageSection.name"
                                        placement="top"
                                        :disabled="pageSection.name.length <= 10">
                                        <div class="truncate">
                                            {{ pageSection.orderNo + ' ' + pageSection.name }}
                                        </div>
                                    </el-tooltip>
                                </div>

                                <div class="flex items-center gap-2 flex-shrink-0">
                                    <status-dot :type="getAuditStatusDotStatus(pageSection.auditStatus)">
                                        {{ enumStore.getLabelByKeyAndValue('auditStatus', pageSection.auditStatus) }}
                                    </status-dot>
                                    <status-dot :type="getOnlineStatusDotStatus(pageSection.onlineStatus)">
                                        {{ enumStore.getLabelByKeyAndValue('onlineStatus', pageSection.onlineStatus) }}
                                    </status-dot>
                                </div>
                            </div>
                            <div class="w-full mt-2">
                                <el-image
                                    @click.prevent
                                    class="w-full h-[100px]"
                                    fit="cover"
                                    preview-teleported
                                    hide-on-click-modal
                                    :src="pageSection.icon"
                                    :preview-src-list="[pageSection.icon]">
                                    <template #error>
                                        <image-error-fallback text="图片损坏" />
                                    </template>
                                </el-image>
                            </div>
                        </div>
                    </el-checkbox>
                </el-checkbox-group>
            </div>

            <div v-if="sortMode" class="overflow-hidden cursor-move">
                <vue-draggable
                    ref="el"
                    v-model="pageSectionList"
                    ghost-class="ghost"
                    :animation="200"
                    handle=".handle"
                    @end="updateOrderNo">
                    <div v-for="item in pageSectionList" :key="item.id">
                        <div class="mb-2 handle">
                            <div class="flex items-center justify-between gap-1">
                                <div class="flex items-baseline gap-1">
                                    <el-icon class="h-full">
                                        <Sort />
                                    </el-icon>
                                    <el-input-number v-model="item.orderNo" :min="1" :step="1" size="small" />
                                </div>
                                <el-tooltip :content="item.name" placement="left" :disabled="item.name.length <= 10">
                                    <div class="truncate">
                                        {{ item.name }}
                                    </div>
                                </el-tooltip>
                            </div>
                            <div class="ml-5">
                                <el-image
                                    class="w-full h-[100px]"
                                    fit="cover"
                                    preview-teleported
                                    hide-on-click-modal
                                    :src="item.icon"
                                    :preview-src-list="[item.icon]">
                                    <template #error>
                                        <image-error-fallback text="图片损坏" />
                                    </template>
                                </el-image>
                            </div>
                        </div>
                    </div>
                </vue-draggable>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref } from 'vue';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { useFeedback } from '@smartdesk/common/composables';
    import { CheckboxValueType } from 'element-plus';
    import { Sort } from '@element-plus/icons-vue';
    import { VueDraggable } from 'vue-draggable-plus';
    import { pageSectionApi, publishApi } from '@smartdesk/common/api';
    import { PageSection } from '@smartdesk/common/types';
    import { useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import {
        canBatchAudit,
        canBatchOffline,
        canBatchOnline,
        canEdit,
        DESIGN_BIZ_PERMISSION,
    } from '@smartdesk/common/permission';
    import { getAuditStatusDotStatus, getOnlineStatusDotStatus } from '@smartdesk/common/utils';

    // 页面楼层管理编辑器
    defineOptions({
        name: 'PageSectionMgmtEditor',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();
    const enumStore = useEnumStore();

    // 排序模式
    const sortMode = ref<boolean>(false);

    // 默认模式下权限
    const checkAll = ref<boolean>(false);

    // 标识 checkbox 的不确定状态
    const isIndeterminate = ref<boolean>(false);

    // 已选中的楼层编码列表
    const checkedPageSectionCodes = ref<string[]>([]);
    const checkedPageSections = computed(() => {
        return pageSectionList.value.filter((item) => checkedPageSectionCodes.value.includes(item.code));
    });

    // 页面楼层列表
    const pageSectionList = ref<PageSection[]>([]);

    // 更新页面楼层排序
    const updateOrderNo = () => {
        pageSectionList.value.forEach((item, index) => {
            item.orderNo = index + 1;
        });
    };

    // 点击全选
    const handleCheckAllChange = (val: CheckboxValueType) => {
        checkedPageSectionCodes.value = val ? pageSectionList.value.map((item) => item.code) : [];
        isIndeterminate.value = false;
    };

    // 处理选择
    const handleCheckedCitiesChange = (value: CheckboxValueType[]) => {
        const checkedCount = value.length;
        checkAll.value = checkedCount === pageSectionList.value.length;
        isIndeterminate.value = checkedCount > 0 && checkedCount < pageSectionList.value.length;
    };

    // 点击排序
    const onClickSort = () => {
        sortMode.value = true;
    };

    // 点击取消排序
    const onClickCancelSort = () => {
        sortMode.value = false;
    };

    // 点击保存
    const onClickSaveSort = async () => {
        if (await feedback.confirm(`确定要保存页面楼层排序吗？`, '确认操作', 'warning')) {
            const res = await pageSectionApi.updatePageSectionOrder(pageSectionList.value);
            if (res.code === 200) {
                sortMode.value = false;
                await refresh();
                feedback.success('保存排序成功');
            } else {
                feedback.error('保存排序失败：' + res.msg);
            }
        }
    };

    // 点击上线
    const onClickOnline = async () => {
        if (!checkedPageSectionCodes.value || checkedPageSectionCodes.value.length === 0) {
            feedback.error('请先选择页面楼层');
        }

        if (await feedback.confirm(`确定要上线页面楼层吗？`, '确认操作', 'warning')) {
            const res = await publishApi.batchPublishSelf('PageSection', checkedPageSectionCodes.value, 'ONLINE');
            if (res.code === 200) {
                await refresh();
                feedback.success('上线成功');
            } else {
                feedback.error('上线失败：' + res.msg);
            }
        }
    };

    // 点击下线
    const onClickOffline = async () => {
        if (!checkedPageSectionCodes.value || checkedPageSectionCodes.value.length === 0) {
            feedback.error('请先选择页面楼层');
        }

        if (await feedback.confirm(`确定要下线页面楼层吗？`, '确认操作', 'warning')) {
            const res = await publishApi.batchPublishSelf('PageSection', checkedPageSectionCodes.value, 'OFFLINE');
            if (res.code === 200) {
                await refresh();
                feedback.success('下线成功');
            } else {
                feedback.error('下线失败：' + res.msg);
            }
        }
    };

    // 点击送审
    const onClickPublish = async () => {
        if (!checkedPageSectionCodes.value || checkedPageSectionCodes.value.length === 0) {
            feedback.error('请先选择页面楼层');
        }

        if (await feedback.confirm(`确定要送审页面楼层吗？`, '确认操作', 'warning')) {
            const res = await publishApi.batchPublishSelf('PageSection', checkedPageSectionCodes.value, 'CREATE');
            if (res.code === 200) {
                await refresh();
                feedback.success('送审成功');
            } else {
                feedback.error('送审失败：' + res.msg);
            }
        }
    };

    // 查询页面楼层列表
    const findPageSectionList = async () => {
        const res = await pageSectionApi.findPageSectionList(
            {
                pageCode: pageDesignerStore.page.code,
                delFlag: 0,
            },
            { paged: false, sort: 'orderNo,asc' }
        );
        if (res.code === 200) {
            pageSectionList.value = res.result;
        }
    };

    // 刷新
    const refresh = async () => {
        checkedPageSectionCodes.value = [];
        isIndeterminate.value = false;
        await findPageSectionList();
        // 需要刷新页面，否则页面楼层列表不会更新
        await pageDesignerStore.refreshPageData(pageDesignerStore.page.code);
    };

    onMounted(() => {
        findPageSectionList();
    });

    defineExpose({
        findPageSectionList,
    });
</script>

<style scoped>
    .ghost {
        opacity: 0.5;
        background: #c8ebfb;
    }

    .handle {
        cursor: move;
    }

    .custom-checkbox {
        width: 100%;
        height: 100%;
        display: flex !important;
        align-items: flex-start !important;
    }

    :deep(.el-checkbox) {
        width: 100%;
        height: 100%;
        display: flex !important;
        align-items: flex-start !important;
        overflow: hidden;
    }

    :deep(.el-checkbox__input) {
        flex: 0 0 auto;
    }

    :deep(.el-checkbox__label) {
        width: 100%;
        flex: 1;
        margin: 0;
        padding-left: 10px;
        overflow: hidden;
    }
</style>
