<template>
    <div class="h-full">
        <div class="flex items-center justify-between item-top p-[10px]" style="height: 52px">
            <div class="flex items-center">
                <action-button
                    :disabled="
                        checkedItems.length === 0 ||
                        !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                            type: 'org',
                            value: pageDesignerStore.selectedPageCell.orgId,
                        })
                    "
                    icon="Promotion"
                    link
                    type="warning"
                    text-class="text-sm"
                    @click="handleBatchPublish"
                    text="批量送审" />
                <action-button
                    :disabled="
                        checkedItems.length === 0 ||
                        !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                            type: 'org',
                            value: pageDesignerStore.selectedPageCell.orgId,
                        })
                    "
                    icon="Delete"
                    link
                    type="danger"
                    text-class="text-sm"
                    @click="handleBatchDelete"
                    text="批量删除" />
            </div>

            <el-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange">
                全选
            </el-checkbox>
        </div>

        <el-checkbox-group
            v-model="checkedItems"
            @change="handleCheckedItemsChange"
            style="height: calc(100% - 140px); overflow: auto">
            <collapse-wrapper
                v-for="item in pageDesignerStore.selectedPageCell.pageCellItemList"
                v-model="expandCollapseItemNames"
                @click="handleSelectCellItem(item.code)"
                style="width: calc(100% - 10px)"
                class="cursor-pointer rounded-md m-[5px] p-[5px] transition-all duration-200"
                :class="{
                    'ring-2 ring-blue-500 shadow-sm': isItemSelected(item.code),
                }">
                <collapse-item-wrapper :name="item.code" @click="handleSelectCellItem(item.code)">
                    <template #header="{ isActive, toggle }">
                        <div @click="handleSelectCellItem(item.code)">
                            <div class="flex items-center gap-2">
                                <el-divider content-position="left">
                                    <div
                                        v-if="isActive"
                                        class="cursor-pointer flex items-center gap-2"
                                        @click.stop="toggle">
                                        <el-icon>
                                            <CaretBottom />
                                        </el-icon>
                                        <el-tooltip :content="getTitle(item)" teleported>
                                            <div class="text-lg min-w-0 truncate" style="max-width: calc(100% - 140px)">
                                                {{ getTitle(item) }}
                                            </div>
                                        </el-tooltip>
                                        <div v-if="item.id" class="flex items-center gap-2 text-sm">
                                            <status-dot
                                                :type="
                                                    item.onlineStatus === 0 || item.onlineStatus === 2
                                                        ? 'error'
                                                        : 'success'
                                                ">
                                                {{ enumStore.getLabelByKeyAndValue('onlineStatus', item.onlineStatus) }}
                                            </status-dot>
                                            <status-dot :type="item.auditStatus === 2 ? 'success' : 'error'">
                                                {{ enumStore.getLabelByKeyAndValue('auditStatus', item.auditStatus) }}
                                            </status-dot>
                                        </div>
                                    </div>
                                    <div v-else class="cursor-pointer flex items-center gap-2" @click.stop="toggle">
                                        <el-icon>
                                            <CaretRight />
                                        </el-icon>
                                        <el-tooltip :content="getTitle(item)" teleported>
                                            <div class="text-lg max-w-full min-w-0 truncate">
                                                {{ getTitle(item) }}
                                            </div>
                                        </el-tooltip>
                                    </div>
                                </el-divider>
                                <el-checkbox
                                    :disabled="
                                        !canAudit(item) ||
                                        !item.id ||
                                        !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                                            type: 'org',
                                            value: pageDesignerStore.selectedPageCell.orgId,
                                        })
                                    "
                                    :key="item.code"
                                    :value="item.code"
                                    @click.stop />
                            </div>

                            <div v-if="!isActive" class="w-full">
                                <div class="w-full mb-2">
                                    <div class="flex items-center w-full h-[100px]">
                                        <div class="flex-shrink-0 w-[100px] h-[100px]">
                                            <el-image
                                                class="w-[100px] h-[100px]"
                                                :src="item.icons?.icon || ''"
                                                :preview-src-list="[item.icons?.icon || '']"
                                                preview-teleported>
                                                <template #error>
                                                    <image-error-fallback text="图标损坏" />
                                                </template>
                                            </el-image>
                                        </div>
                                        <div
                                            class="flex flex-col justify-between h-full px-2"
                                            style="width: calc(100% - 100px)">
                                            <el-tooltip :content="item.dataName || '未设置'" placement="right">
                                                <div class="text-base text-start truncate w-full overflow-hidden">
                                                    {{ item.dataName || '未设置' }}
                                                </div>
                                            </el-tooltip>
                                            <div class="flex flex-col gap-1">
                                                <div class="flex items-center gap-2 text-sm">
                                                    <status-dot
                                                        :type="
                                                            item.onlineStatus === 0 || item.onlineStatus === 2
                                                                ? 'error'
                                                                : 'success'
                                                        ">
                                                        {{
                                                            enumStore.getLabelByKeyAndValue(
                                                                'onlineStatus',
                                                                item.onlineStatus
                                                            )
                                                        }}
                                                    </status-dot>
                                                    <status-dot :type="item.auditStatus === 2 ? 'success' : 'error'">
                                                        {{
                                                            enumStore.getLabelByKeyAndValue(
                                                                'auditStatus',
                                                                item.auditStatus
                                                            )
                                                        }}
                                                    </status-dot>
                                                </div>
                                            </div>
                                            <div class="flex gap-2 text-sm">
                                                生效时间：{{ item.validTime || '未设置' }}
                                            </div>
                                            <div class="flex gap-2 text-sm">
                                                失效时间：{{ item.expireTime || '未设置' }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>

                    <page-cell-item-form-editor
                        :item="item"
                        :show-strategy="item.defaultFlag === CELL_ITEM_TYPE.PERSONALIZED"
                        :personal-rules="personalRules"
                        @save="handleSave(pageDesignerStore.selectedPageCell, item)"
                        @publish="handlePublish"
                        @delete="handleDelete" />
                </collapse-item-wrapper>
            </collapse-wrapper>
        </el-checkbox-group>

        <div class="add-item-button">
            <action-button
                :disabled="
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                        type: 'org',
                        value: pageDesignerStore.selectedPageCell.orgId,
                    })
                "
                icon="Plus"
                link
                type="default"
                text-class="text-base"
                text="新建"
                @click="handleAddItem" />
        </div>
    </div>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { useFeedback } from '@smartdesk/common/composables';
    import { personalRuleApi, publishApi } from '@smartdesk/common/api';
    import PageCellItemFormEditor from './page-cell-item-form-editor.vue';
    import { PageCell, PageCellItem, PersonalRule } from '@smartdesk/common/types';
    import { CELL_ITEM_TYPE } from '@smartdesk/common/constant';
    import { CheckboxValueType } from 'element-plus';
    import { CaretBottom, CaretRight } from '@element-plus/icons-vue';
    import { RestResultResponse, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import { canAudit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';

    // 坑位元素编辑器
    defineOptions({
        name: 'PageCellItemEditor',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const feedback = useFeedback();
    const enumStore = useEnumStore();
    const permissionStore = usePermissionStore();

    // 是否全选
    const checkAll = ref<boolean>(false);

    // 是否中间状态：在选择了部分选项但未全选时为 true
    const isIndeterminate = ref<boolean>(false);

    // 已选中的坑位元素
    const checkedItems = ref<string[]>([]);

    // 推荐策略列表
    const personalRules = ref<PersonalRule[]>([]);

    // 展开的折叠面板
    const expandCollapseItemNames = ref<string[]>([]);

    // 处理全选
    const handleCheckAllChange = (val: CheckboxValueType) => {
        checkedItems.value = val
            ? pageDesignerStore.selectedPageCell.pageCellItemList
                  .filter(
                      (item) =>
                          item.id &&
                          canAudit(item) &&
                          permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_CELL.EDIT, {
                              type: 'org',
                              value: pageDesignerStore.selectedPageCell.orgId,
                          })
                  )
                  .map((item) => item.code)
            : [];
        checkAll.value = checkedItems.value.length === pageDesignerStore.selectedPageCell.pageCellItemList.length;
        isIndeterminate.value =
            checkedItems.value.length > 0 &&
            checkedItems.value.length < pageDesignerStore.selectedPageCell.pageCellItemList.length;
    };

    // 处理选择
    const handleCheckedItemsChange = (value: CheckboxValueType[]) => {
        const checkedCount = value.length;
        checkAll.value = checkedCount === pageDesignerStore.selectedPageCell.pageCellItemList.length;
        isIndeterminate.value =
            checkedCount > 0 && checkedCount < pageDesignerStore.selectedPageCell.pageCellItemList.length;
    };

    // 处理选中坑位元素
    const handleSelectCellItem = (itemCode: string) => {
        pageDesignerStore.switchPageCellItem(itemCode);
    };

    // 判断坑位元素是否被选中
    const isItemSelected = (itemCode: string) => {
        if (!pageDesignerStore.selectedPageCell) return false;

        const cellCode = pageDesignerStore.selectedPageCell.code;
        const selectedItemCode = pageDesignerStore.selectedCellItemMap.get(cellCode);

        // 如果用户有选择，使用用户选择的
        if (selectedItemCode) {
            return selectedItemCode === itemCode;
        }

        // 如果用户没有选择，判断是否是默认元素
        const item = pageDesignerStore.selectedPageCell.pageCellItemList?.find((i) => i.code === itemCode);
        if (item?.defaultFlag === CELL_ITEM_TYPE.DEFAULT) {
            return true;
        }

        // 如果没有默认元素，判断是否是第一个元素
        const firstItem = pageDesignerStore.selectedPageCell.pageCellItemList?.[0];
        return firstItem?.code === itemCode;
    };

    // 处理批量送审坑位元素
    const handleBatchPublish = async (): Promise<void> => {
        if (checkedItems.value.length === 0) {
            feedback.error('请先选择坑位元素');
            return;
        }

        const response = await feedback.confirm('确定批量送审坑位元素吗？');
        if (!response) {
            return;
        }

        const res = await publishApi.batchPublishSelf('PageCellItem', checkedItems.value, 'CREATE');
        if (res.code === 200) {
            feedback.success('批量送审坑位元素成功');
            checkAll.value = false;
            isIndeterminate.value = false;
            checkedItems.value = [];
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code, false, [
                pageDesignerStore.selectedPageCell.pageSectionCode,
            ]);
        } else {
            feedback.error('批量送审坑位元素失败：' + res.msg);
        }
    };

    // 处理批量删除元素
    const handleBatchDelete = async (): Promise<void> => {
        if (checkedItems.value.length === 0) {
            feedback.error('请先选择坑位元素');
            return;
        }

        const response = await feedback.confirm('确定批量删除坑位元素吗？');
        if (!response) {
            return;
        }

        const res = await publishApi.batchPublishSelf('PageCellItem', checkedItems.value, 'DELETE');
        if (res.code === 200) {
            feedback.success('批量删除坑位元素成功');
            checkAll.value = false;
            isIndeterminate.value = false;
            checkedItems.value = [];
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code, false, [
                pageDesignerStore.selectedPageCell.pageSectionCode,
            ]);
        } else {
            feedback.error('批量删除坑位元素失败：' + res.msg);
        }
    };

    // 点击新建坑位元素
    const handleAddItem = (): void => {
        if (pageDesignerStore.selectedPageCell) {
            const newCellItem: PageCellItem = {
                code: `item_${Date.now()}-${Math.floor(Math.random() * 1000)}`,
                cellId: pageDesignerStore.selectedPageCell?.id,
                cellCode: pageDesignerStore.selectedPageCell?.code,
                defaultFlag: CELL_ITEM_TYPE.PERSONALIZED,
                icons: {},
                type: CELL_ITEM_TYPE.PERSONALIZED,
                delFlag: 0,
                status: 1,
                auditStatus: 0,
                visibleStatus: 1,
                onlineStatus: 0,
                scheduleRule: {
                    scheduleConfig: {
                        scheduleType: 0,
                        dayList: [],
                        startTime: '',
                        endTime: '',
                    },
                },
            } as unknown as PageCellItem;

            pageDesignerStore.selectedPageCell.pageCellItemList = [
                ...(pageDesignerStore.selectedPageCell.pageCellItemList || []),
                newCellItem,
            ];

            // 自动展开新建的坑位元素的折叠面板
            expandCollapseItemNames.value.push(newCellItem.code);
        }
    };

    // 处理送审坑位元素
    const handlePublish = async (item: PageCellItem): Promise<void> => {
        const res = await publishApi.publishSelf('PageCellItem', item.code, 'CREATE');
        if (res.code === 200) {
            feedback.success('送审坑位元素成功');
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code, false, [
                pageDesignerStore.selectedPageCell.pageSectionCode,
            ]);
        } else {
            feedback.error('送审坑位元素失败：' + res.msg);
        }
    };

    // 处理删除坑位元素
    const handleDelete = async (item: PageCellItem): Promise<void> => {
        const res = await publishApi.publishSelf('PageCellItem', item.code, 'DELETE');
        if (res.code === 200) {
            feedback.success('删除坑位元素成功');
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code, false, [
                pageDesignerStore.selectedPageCell.pageSectionCode,
            ]);
        } else {
            feedback.error('删除坑位元素失败：' + res.msg);
        }
    };

    // 处理保存
    const handleSave = async (cell: PageCell, item: PageCellItem): Promise<void> => {
        try {
            // 保存原始的code，用于在保存后更新折叠面板状态
            const originalCode = item.code;
            let res: RestResultResponse<PageCellItem>;
            if (item.id) {
                // 修改
                res = await pageDesignerStore.updateCellItem(cell, item);
            } else {
                // 新增
                res = await pageDesignerStore.createCellItem(cell, item);
            }

            // 检查保存后code是否变化
            if (originalCode !== res.result.code && expandCollapseItemNames.value.includes(originalCode)) {
                // 更新折叠面板的展开状态
                expandCollapseItemNames.value = expandCollapseItemNames.value.filter((code) => code !== originalCode);
                expandCollapseItemNames.value.push(res.result.code);

                // 更新选中状态
                pageDesignerStore.switchPageCellItem(res.result.code);
            }
        } catch (error) {
            feedback.error('保存失败: ' + error);
        }
    };

    // 根据 ruleCode 获取 ruleName
    const getPersonalRules = async () => {
        const res = await personalRuleApi.getPersonalRules({}, { paged: false });
        if (res.code === 200 && res.result.length > 0) {
            personalRules.value = res.result;
        }
    };

    // 根据 item 获取 title
    const getTitle = (item: PageCellItem) => {
        if (item.defaultFlag === CELL_ITEM_TYPE.DEFAULT) {
            return '默认';
        }
        if (item.defaultFlag === CELL_ITEM_TYPE.PERSONALIZED) {
            if (!item.ruleCode) {
                return '全局缺省';
            }
            if (personalRules.value.find((rule) => rule.code === item.ruleCode)) {
                return personalRules.value.find((rule) => rule.code === item.ruleCode)!.name;
            }
        }
        return '';
    };

    // 监听选中的坑位，自动展开选中坑位元素的折叠面板
    watch(
        () => pageDesignerStore.selectedPageCell,
        (newCell) => {
            if (!newCell) return;

            // 获取当前选中的坑位元素
            let targetItemCode = pageDesignerStore.selectedCellItemMap.get(newCell.code);

            // 如果没有用户选择，找默认元素或第一个元素
            if (!targetItemCode) {
                const defaultItem = newCell.pageCellItemList?.find(
                    (item) => item.defaultFlag === CELL_ITEM_TYPE.DEFAULT
                );
                targetItemCode = defaultItem?.code || newCell.pageCellItemList?.[0]?.code;
            }

            // 自动展开选中坑位元素的折叠面板
            if (targetItemCode && !expandCollapseItemNames.value.includes(targetItemCode)) {
                expandCollapseItemNames.value.push(targetItemCode);
            }
        }
    );

    onMounted(() => {
        // 获取推荐策略
        getPersonalRules();
    });
</script>

<style scoped>
    .add-item-button {
        border: 1px dashed #dcdfe6;
        border-radius: 4px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        cursor: pointer;
        margin: 16px 0;
    }

    :deep(.el-divider__text) {
        padding: unset;
    }

    :deep(.el-divider__text.is-left) {
        left: 0;
        width: 100%;
    }

    :deep(.el-collapse-item__wrap) {
        border: unset !important;
    }

    :deep(.el-collapse) {
        border: unset !important;
    }

    :deep(.el-collapse-item__header) {
        border: unset !important;
        height: auto !important;
    }

    :deep(.el-collapse-item__content) {
        padding: unset;
    }

    .item-top:deep(.el-checkbox) {
        display: flex;
        flex-direction: row-reverse;
        justify-content: flex-end;
    }

    .item-top:deep(.el-checkbox__input) {
        margin-left: 8px;
        margin-right: 8px;
    }

    .item-top:deep(.el-checkbox__label) {
        padding-left: 0;
        padding-right: 0;
    }
</style>
