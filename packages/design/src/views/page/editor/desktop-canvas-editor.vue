<template>
    <div :style="pageStyle">
        <div :style="desktopTopSectionStyle" v-if="showDesktopStuff">
            <top-section-canvas-editor />
        </div>
        <div :style="desktopNavStyle" v-if="showDesktopStuff">
            <nav-canvas-editor />
        </div>
        <div :style="showDesktopStuff ? desktopPageStyle : {}" class="bg-inherit">
            <page-canvas-editor />
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, CSSProperties } from 'vue';
    import TopSectionCanvasEditor from './top-section-canvas-editor.vue';
    import NavCanvasEditor from './nav-canvas-editor.vue';
    import PageCanvasEditor from './page-canvas-editor.vue';
    import { usePageDesignerStore } from '@smartdesk/design/stores';

    // 桌面画布编辑器
    defineOptions({
        name: 'DesktopCanvasEditor',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();

    // 展示桌面相关
    const showDesktopStuff = computed(() => {
        return pageDesignerStore.mode === 'page' && pageDesignerStore.desktopCode;
    });

    // 顶部楼层样式
    const desktopTopSectionStyle = computed<CSSProperties>(() => {
        return {
            position: 'absolute',
            top: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'top-section')?.layout.rect.top}px`,
            left: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'top-section')?.layout.rect.left}px`,
            width: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'top-section')?.layout.rect.width}px`,
            height: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'top-section')?.layout.rect.height}px`,
        };
    });

    // 导航样式
    const desktopNavStyle = computed<CSSProperties>(() => {
        return {
            position: 'absolute',
            top: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'nav')?.layout.rect.top}px`,
            left: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'nav')?.layout.rect.left}px`,
            width: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'nav')?.layout.rect.width}px`,
            height: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'nav')?.layout.rect.height}px`,
        };
    });

    // 页面样式
    const desktopPageStyle = computed<CSSProperties>(() => {
        return {
            position: 'relative',
            top: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'page')?.layout.rect.top}px`,
            left: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'page')?.layout.rect.left}px`,
            width: `${pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'page')?.layout.rect.width}px`,
        };
    });

    // 页面样式
    const pageStyle = computed<CSSProperties>(() => {
        if (pageDesignerStore.page.layout.layout.props && pageDesignerStore.page.layout.layout.props.background) {
            if (pageDesignerStore.page.layout.layout.props.background.type === 'color') {
                // 纯色
                return {
                    backgroundColor: pageDesignerStore.page.layout.layout.props.background.color,
                    opacity: pageDesignerStore.page.layout.layout.props.background.opacity,
                };
            }

            if (pageDesignerStore.page.layout.layout.props.background.type === 'gradient') {
                // 渐变色
                const direction =
                    pageDesignerStore.page.layout.layout.props.background.linearGradient?.direction || 'to bottom';
                const colors =
                    pageDesignerStore.page.layout.layout.props.background.linearGradient?.colors?.join(', ') || '';

                return {
                    background: `linear-gradient(${direction}, ${colors})`,
                    opacity: pageDesignerStore.page.layout.layout.props.background.opacity,
                };
            }

            if (pageDesignerStore.page.layout.layout.props.background.type === 'image') {
                // 图片
                const imageUrl = pageDesignerStore.page.layout.layout.props.background.image
                    ? `url('${pageDesignerStore.page.layout.layout.props.background.image}')`
                    : '';
                return {
                    backgroundImage: `${imageUrl}`,
                    backgroundSize: 'contain',
                    backgroundPosition: 'center center',
                    backgroundRepeat: 'repeat',
                    opacity: pageDesignerStore.page.layout.layout.props.background.opacity,
                };
            }
        }
        return {};
    });
</script>
