<template>
    <div class="absolute w-full h-full" style="pointer-events: none">
        <div class="h-full w-full">
            <component :is="'epgui-' + cell.componentType" v-bind="cellProps" :key="componentKey"></component>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed } from 'vue';
    import { PageCell, PageSection } from '@smartdesk/common/types';
    import { getDataConvert } from '@epgui/epg-components';
    import { usePageDesignerStore } from '@smartdesk/design/stores';

    // 页面设计器坑位画布编辑器
    defineOptions({
        name: 'PageSectionCellCanvasEditor',
    });

    // 参数
    const props = defineProps<{
        cell: PageCell;
        pageSection: PageSection;
    }>();

    // pinia store
    const pageDesignerStore = usePageDesignerStore();

    // 缓存数据转换函数
    const dataConverter = computed(() => getDataConvert(props.cell.componentType));

    // 传入动态组件的属性
    const cellProps = computed(() => {
        // 这里不应该直接返回选中的坑位元素，应该把坑位元素和数据分开，只建立坑位与数据的映射，
        // 坑位与数据是多对多的关系，一个坑位可以有多个数据，一个数据也可以被多个坑位使用
        // 当用户选择了某个坑位元素时，需要切换坑位元素对应的数据
        // 如果坑位没有坑位元素，但坑位有页面楼层的数据，就使用页面楼层的数据，需要建立坑位与页面楼层数据的映射
        // 总而言之，不能将数据绑在坑位元素上，因为坑位下是可以没有坑位元素的，但可能是有数据的（使用页面楼层数据）
        const cellItemData = pageDesignerStore.getSelectedCellItemData(props.cell.code);

        return {
            ...props.cell.layout.props,
            rect: {
                top: 0,
                left: 0,
                width: props.cell.layout.rect.width,
                height: props.cell.layout.rect.height,
            },
            data:
                cellItemData instanceof Array
                    ? cellItemData.map((item: any) => dataConverter.value(item ?? {}))
                    : dataConverter.value(cellItemData ?? {}),
        };
    });

    // 组件 key
    const componentKey = computed(() => {
        return props.cell.code + '-' + JSON.stringify(cellProps.value);
    });
</script>
