<template>
    <div class="p-[10px] h-full">
        <div class="w-full flex items-center justify-end gap-1 mb-4" style="height: 24px">
            <action-button
                :disabled="
                    isRefSection(pageDesignerStore.selectedPageSection) ||
                    !canEdit(pageDesignerStore.selectedPageSection) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.PERMISSION_CONFIG, {
                        type: 'org',
                        value: pageDesignerStore.selectedPageSection.orgId,
                    })
                "
                icon="Operation"
                link
                type="primary"
                text-class="text-sm"
                text="权限配置"
                @click="onClickPermissionConfig" />
            <action-button
                :disabled="
                    isRefSection(pageDesignerStore.selectedPageSection) ||
                    !canAudit(pageDesignerStore.selectedPageSection) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.AUDIT, {
                        type: 'org',
                        value: pageDesignerStore.selectedPageSection.orgId,
                    })
                "
                link
                type="warning"
                text-class="text-sm"
                text="送审楼层"
                @click="onClickPublishSelf">
                <template #icon>
                    <i-mdi-share class="mr-2 w-4 h-4" />
                </template>
            </action-button>
            <action-button
                :disabled="
                    isRefSection(pageDesignerStore.selectedPageSection) ||
                    !canAudit(pageDesignerStore.selectedPageSection) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.AUDIT_ALL, {
                        type: 'org',
                        value: pageDesignerStore.selectedPageSection.orgId,
                    })
                "
                link
                type="warning"
                text-class="text-sm"
                text="全部送审"
                @click="onClickPublishComplete">
                <template #icon>
                    <i-mdi-share-all class="mr-2 w-4 h-4" />
                </template>
            </action-button>
        </div>

        <div style="height: calc(100% - 80px); overflow: auto">
            <div class="text-red-400" v-if="pageDesignerStore.selectedPageSection.auditStatus === 0">
                当前编辑信息未送审
            </div>

            <collapse-wrapper v-model="baseInfoCollapseName">
                <collapse-item-wrapper name="baseInfo" title="基础信息">
                    <template #header="{ isActive, toggle }">
                        <page-section-collapse-header
                            class="cursor-pointer"
                            title="基础信息"
                            :is-active="isActive"
                            :item="pageDesignerStore.selectedElement"
                            @click="toggle" />
                    </template>

                    <el-form
                        :rules="rules"
                        :model="pageDesignerStore.selectedPageSection"
                        label-width="100px"
                        label-position="left"
                        :label-suffix="':'">
                        <el-form-item label="楼层名称" prop="name">
                            <el-input
                                :disabled="
                                    isRefSection(pageDesignerStore.selectedPageSection) ||
                                    !canEdit(pageDesignerStore.selectedPageSection) ||
                                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.EDIT, {
                                        type: 'org',
                                        value: pageDesignerStore.selectedPageSection.orgId,
                                    })
                                "
                                v-model="pageDesignerStore.selectedPageSection.name"
                                clearable />
                        </el-form-item>
                        <el-form-item label="策略信息" prop="strategy">
                            <div class="flex items-center justify-between w-full">
                                <el-tooltip :content="pageDesignerStore.selectedPageSection.ruleCode">
                                    <div class="break-words whitespace-nowrap overflow-hidden text-ellipsis">
                                        {{ getPersonalRuleName(pageDesignerStore.selectedPageSection.ruleCode) }}
                                    </div>
                                </el-tooltip>
                                <el-button
                                    :disabled="
                                        isRefSection(pageDesignerStore.selectedPageSection) ||
                                        !canEdit(pageDesignerStore.selectedPageSection) ||
                                        !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.EDIT, {
                                            type: 'org',
                                            value: pageDesignerStore.selectedPageSection.orgId,
                                        })
                                    "
                                    link
                                    type="primary"
                                    @click="handleChoosePersonalRule">
                                    选择
                                </el-button>
                            </div>
                        </el-form-item>
                        <el-form-item label="是否反选" prop="inverted">
                            <el-switch
                                :disabled="
                                    isRefSection(pageDesignerStore.selectedPageSection) ||
                                    !canEdit(pageDesignerStore.selectedPageSection) ||
                                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.EDIT, {
                                        type: 'org',
                                        value: pageDesignerStore.selectedPageSection.orgId,
                                    })
                                "
                                v-model="pageDesignerStore.selectedPageSection.inverted"
                                size="default"
                                style="width: 100%" />
                        </el-form-item>
                        <el-form-item label="有效期" prop="validTime">
                            <base-date-picker
                                :disabled="
                                    isRefSection(pageDesignerStore.selectedPageSection) ||
                                    !canEdit(pageDesignerStore.selectedPageSection) ||
                                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.EDIT, {
                                        type: 'org',
                                        value: pageDesignerStore.selectedPageSection.orgId,
                                    })
                                "
                                v-model:start-date="pageDesignerStore.selectedPageSection.validTime"
                                v-model:end-date="pageDesignerStore.selectedPageSection.expireTime"
                                type="datetimerange"
                                start-placeholder="生效时间"
                                end-placeholder="失效时间" />
                        </el-form-item>
                        <el-form-item label="频次" prop="scheduleRuleCode">
                            <el-select
                                v-model="pageDesignerStore.selectedPageSection.scheduleRule.scheduleConfig.scheduleType"
                                placeholder="请选择"
                                clearable
                                @change="handleScheduleTypeChange()"
                                class="mb-2">
                                <el-option
                                    v-for="option in scheduleTypeList"
                                    :key="option.value"
                                    :label="option.label"
                                    :value="option.value"></el-option>
                            </el-select>

                            <base-checkbox
                                v-if="
                                    pageDesignerStore.selectedPageSection.scheduleRule.scheduleConfig.scheduleType !== 0
                                "
                                v-model:model-value="
                                    pageDesignerStore.selectedPageSection.scheduleRule.scheduleConfig.dayList
                                "
                                :options="
                                    pageDesignerStore.selectedPageSection.scheduleRule.scheduleConfig.scheduleType === 1
                                        ? weekList
                                        : pageDesignerStore.selectedPageSection.scheduleRule.scheduleConfig
                                                .scheduleType === 2
                                          ? dayList
                                          : []
                                ">
                            </base-checkbox>

                            <el-config-provider :locale="zhCn">
                                <el-time-picker
                                    :model-value="getTimeRange()"
                                    @update:model-value="(val: any) => handleTimeRangeChange(val)"
                                    is-range
                                    range-separator="-"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    class="mt-2" />
                            </el-config-provider>
                        </el-form-item>
                    </el-form>
                </collapse-item-wrapper>
            </collapse-wrapper>

            <collapse-wrapper v-model="pageSectionDataCollapseName">
                <collapse-item-wrapper name="pageSectionData">
                    <template #header="{ isActive, toggle }">
                        <page-section-collapse-header
                            class="cursor-pointer"
                            title="楼层数据源"
                            :is-active="isActive"
                            :show-status="false"
                            @click="toggle" />
                    </template>

                    <el-form
                        ref="formRef"
                        :model="pageDesignerStore.selectedPageSection"
                        label-width="120px"
                        label-position="left"
                        :label-suffix="':'"
                        :rules="
                            !!pageDesignerStore.selectedPageSection.dsType
                                ? {
                                      dsCode: [
                                          {
                                              required: true,
                                              message: '请选择数据源',
                                              trigger: ['blur', 'change'],
                                          },
                                      ],
                                  }
                                : {}
                        ">
                        <el-form-item label="数据源类型" prop="dsType">
                            <el-select
                                :disabled="
                                    isRefSection(pageDesignerStore.selectedPageSection) ||
                                    !canEdit(pageDesignerStore.selectedPageSection) ||
                                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.EDIT, {
                                        type: 'org',
                                        value: pageDesignerStore.selectedPageSection.orgId,
                                    })
                                "
                                v-model="pageDesignerStore.selectedPageSection.dsType"
                                placeholder="请选择"
                                clearable
                                @change="handleDsTypeChange">
                                <el-option
                                    v-for="option in pageSectionDsTypeOptions"
                                    :key="option.code"
                                    :label="option.name"
                                    :value="option.code"></el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="数据源名称" prop="dsCode">
                            <div class="flex items-center justify-between">
                                <el-input v-model="pageDesignerStore.selectedPageSection.dsName" readonly />
                                <el-button
                                    :disabled="
                                        isRefSection(pageDesignerStore.selectedPageSection) ||
                                        !canEdit(pageDesignerStore.selectedPageSection) ||
                                        !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.EDIT, {
                                            type: 'org',
                                            value: pageDesignerStore.selectedPageSection.orgId,
                                        })
                                    "
                                    type="primary"
                                    link
                                    @click="onClickChoose">
                                    选择
                                </el-button>
                            </div>
                        </el-form-item>

                        <el-form-item
                            v-if="pageDesignerStore.selectedPageSection.dsType === 'category'"
                            label="数据源是否随机"
                            prop="dsParams.random">
                            <el-switch
                                :disabled="
                                    isRefSection(pageDesignerStore.selectedPageSection) ||
                                    !canEdit(pageDesignerStore.selectedPageSection) ||
                                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.EDIT, {
                                        type: 'org',
                                        value: pageDesignerStore.selectedPageSection.orgId,
                                    })
                                "
                                v-model="pageDesignerStore.selectedPageSection.dsParams.random"
                                size="default"
                                style="width: 100%" />
                        </el-form-item>
                    </el-form>
                </collapse-item-wrapper>
            </collapse-wrapper>
        </div>

        <div class="flex items-center justify-end gap-2" style="height: 32px">
            <el-button
                :disabled="
                    isRefSection(pageDesignerStore.selectedPageSection) ||
                    !canEdit(pageDesignerStore.selectedPageSection) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.EDIT, {
                        type: 'org',
                        value: pageDesignerStore.selectedPageSection.orgId,
                    })
                "
                size="default"
                @click="onClickCancelPageSectionData">
                取消
            </el-button>
            <el-button
                :disabled="
                    isRefSection(pageDesignerStore.selectedPageSection) ||
                    !canEdit(pageDesignerStore.selectedPageSection) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE_SECTION.EDIT, {
                        type: 'org',
                        value: pageDesignerStore.selectedPageSection.orgId,
                    })
                "
                size="default"
                type="primary"
                @click="onClickSavePageSectionData">
                保存
            </el-button>
        </div>
    </div>

    <base-modal
        v-if="contentDialogVisible"
        v-model="contentDialogVisible"
        :title="contentDialogTitle"
        width="60%"
        destroy-on-close
        confirm-text="保存"
        @cancel="onClickCancelContent"
        @confirm="onClickSaveContent">
        <content-selector :content-type="contentType" ref="contentContainerRef" />
    </base-modal>

    <permission-config-dialog
        v-if="permissionConfigDialogVisible"
        v-model:visible="permissionConfigDialogVisible"
        v-model:item="pageDesignerStore.selectedPageSection"
        @submit="handlePermissionConfigSubmit" />

    <personal-rule-dialog
        v-model="pageDesignerStore.selectedPageSection.ruleCode"
        v-model:dialog-visible="personalRuleDialogVisible"
        v-if="personalRuleDialogVisible" />
</template>

<script setup lang="ts">
    import { computed, onMounted, reactive, ref } from 'vue';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { FormInstance, FormRules } from 'element-plus';
    import { personalRuleApi, publishApi } from '@smartdesk/common/api';
    import { useFeedback } from '@smartdesk/common/composables';
    import { PersonalRule } from '@smartdesk/common/types';
    import PageSectionCollapseHeader from './page-section/page-section-collapse-header.vue';
    import ContentSelector from '../selector/content-selector.vue';
    import PermissionConfigDialog from '../dialog/permission-config-dialog.vue';
    import { canAudit, canEdit, isRefSection, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { Enumeration, useEnumStore, usePermissionStore } from '@chances/portal_common_core';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';
    import { dayList, scheduleTypeList, weekList } from '@smartdesk/common/constant';
    import dayjs from 'dayjs';

    // 页面设计器页面楼层配置编辑器
    defineOptions({
        name: 'PageSectionConfigEditor',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const feedback = useFeedback();
    const permissionStore = usePermissionStore();
    const enumStore = useEnumStore();

    // 表单引用
    const formRef = ref<FormInstance | null>(null);

    // 折叠面板状态
    const baseInfoCollapseName = ref<string[]>(['baseInfo']);
    const pageSectionDataCollapseName = ref<string[]>(['pageSectionData']);

    // 页面楼层数据源枚举
    const pageSectionDsTypeOptions = ref<Enumeration[]>(enumStore.getEnumsByKey('pageSectionDsType') || []);

    // 选择内容对话框显隐
    const contentDialogVisible = ref<boolean>(false);
    const contentDialogTitle = computed(() => {
        return `选择${pageSectionDsTypeOptions.value.find((item) => item.code === pageDesignerStore.selectedPageSection.dsType)?.name || ''}`;
    });

    // 选择内容类型
    const contentType = ref<'category' | 'thirdParty'>('category');

    // 内容容器引用
    const contentContainerRef = ref<HTMLElement>();

    // 权限配置的对话框
    const permissionConfigDialogVisible = ref<boolean>(false);

    // 表单规则
    const rules = reactive<FormRules>({
        name: [
            {
                required: true,
                message: '请输入楼层名称',
                trigger: ['blur', 'change'],
            },
        ],
    });

    // 选择推荐策略
    const personalRuleDialogVisible = ref<boolean>(false);
    const handleChoosePersonalRule = () => {
        personalRuleDialogVisible.value = true;
    };
    // 推荐策略列表
    const personalRuleList = ref<PersonalRule[]>([]);

    // 处理频次类型变化
    const handleScheduleTypeChange = () => {
        pageDesignerStore.selectedPageSection.scheduleRule.scheduleConfig.dayList = [];
    };

    // 获取频次的时间段
    const getTimeRange = (): [Date, Date] => {
        const { startTime, endTime } = pageDesignerStore.selectedPageSection.scheduleRule.scheduleConfig;
        if (!startTime || !endTime)
            return [
                dayjs().startOf('day').hour(0).minute(0).second(0).toDate(),
                dayjs().endOf('day').hour(23).minute(59).second(59).toDate(),
            ];

        const today = new Date().toDateString();
        return [new Date(`${today} ${startTime}`), new Date(`${today} ${endTime}`)];
    };

    // 处理频次时间段变化
    const handleTimeRangeChange = (val: [Date, Date]) => {
        pageDesignerStore.selectedPageSection.scheduleRule.scheduleConfig.startTime = dayjs(val[0]).format('HH:mm:ss');
        pageDesignerStore.selectedPageSection.scheduleRule.scheduleConfig.endTime = dayjs(val[1]).format('HH:mm:ss');
    };

    // 处理数据源类型变化
    const handleDsTypeChange = () => {
        pageDesignerStore.selectedPageSection.dsCode = '';
        pageDesignerStore.selectedPageSection.dsName = '';
    };

    // 点击权限配置
    const onClickPermissionConfig = (): void => {
        permissionConfigDialogVisible.value = true;
    };

    // 处理提交权限配置
    const handlePermissionConfigSubmit = async () => {
        const res = await pageDesignerStore.updateElement();
        if (res.code === 200) {
            permissionConfigDialogVisible.value = false;
        }
    };

    // 点击保存内容
    const onClickSaveContent = async () => {
        const data = (contentContainerRef?.value as any)?.selectedContent;

        // 处理 pageDesignerStore.selectedPageSection 和 selectedContent
        if (pageDesignerStore.selectedPageSection.dsType === 'category') {
            pageDesignerStore.selectedPageSection.dsCode = data.node.code;
            pageDesignerStore.selectedPageSection.dsName = data.path.map((node: any) => node.name).join(' | ');
        } else if (pageDesignerStore.selectedPageSection.dsType === 'recommend') {
            pageDesignerStore.selectedPageSection.dsCode = data.code;
            pageDesignerStore.selectedPageSection.dsName = data.name;
        }

        contentDialogVisible.value = false;
    };

    // 点击选择
    const onClickChoose = () => {
        if (!pageDesignerStore.selectedPageSection.dsType) {
            feedback.error('请先选择数据源类型');
            return;
        }

        if (pageDesignerStore.selectedPageSection.dsType === 'category') {
            contentType.value = 'category';
        } else if (pageDesignerStore.selectedPageSection.dsType === 'recommend') {
            contentType.value = 'thirdParty';
        }

        contentDialogVisible.value = true;
    };

    // 点击取消内容
    const onClickCancelContent = () => {
        contentDialogVisible.value = false;
    };

    // 点击取消楼层数据
    const onClickCancelPageSectionData = () => {
        pageDesignerStore.refreshPageData(pageDesignerStore.page.code, false, [
            pageDesignerStore.selectedPageSection.code,
        ]);
    };

    // 点击保存楼层数据
    const onClickSavePageSectionData = () => {
        formRef.value?.validate((valid) => {
            if (valid) {
                pageDesignerStore.updatePageSection(pageDesignerStore.selectedPageSection);
            }
        });
    };

    // 点击送审自己
    const onClickPublishSelf = async () => {
        const res = await publishApi.publishSelf('PageSection', pageDesignerStore.selectedPageSection.code, 'CREATE');
        if (res.code === 200) {
            feedback.success('送审页面楼层成功');
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code, false, [
                pageDesignerStore.selectedPageSection.code,
            ]);
        } else {
            feedback.error('送审页面楼层失败：' + res.msg);
        }
    };

    // 点击送审自己及实体
    const onClickPublishComplete = async () => {
        const res = await publishApi.publishComplete(
            'PageSection',
            pageDesignerStore.selectedPageSection.code,
            'CREATE'
        );
        if (res.code === 200) {
            feedback.success('全部送审成功');
            await pageDesignerStore.refreshPageData(pageDesignerStore.page.code, false, [
                pageDesignerStore.selectedPageSection.code,
            ]);
        } else {
            feedback.error('全部送审失败：' + res.msg);

            // 从 msg 中提取
            const matches = [...res.msg.matchAll(/\[(.*?)\]/g)];
            const codes = matches ? matches.map((match) => match[1]) : [];
            if (codes && codes.length > 1) {
                pageDesignerStore.scrollToPageSection(codes[0]);
                pageDesignerStore.switchElement('Cell', codes[1]);
            }
        }
    };
    // 获取推荐策略列表
    const getPersonalRules = async () => {
        const res = await personalRuleApi.getPersonalRules({}, { paged: false });
        if (res.code === 200) {
            personalRuleList.value = res.result;
        }
    };

    // 获取推荐策略名称
    const getPersonalRuleName = (ruleCode: string) => {
        const rule = personalRuleList.value.find((rule) => rule.code === ruleCode);
        return rule ? rule.name : '';
    };

    onMounted(() => {
        getPersonalRules();
    });
</script>

<style scoped>
    :deep(.el-divider__text) {
        padding: unset;
    }

    :deep(.el-divider__text.is-left) {
        left: 0;
    }

    .personal-collapse :deep(.el-divider__text.is-left) {
        left: 10px;
    }

    :deep(.el-collapse-item__wrap) {
        border: unset !important;
    }

    :deep(.el-collapse) {
        border: unset !important;
    }

    :deep(.el-collapse-item__header) {
        border: unset !important;
        height: auto !important;
    }

    :deep(.el-collapse-item__content) {
        padding: unset;
    }
</style>
