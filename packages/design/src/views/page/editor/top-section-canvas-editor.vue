<template>
    <div class="bg-inherit w-full h-full border-2 border-gray-400">
        <div
            v-for="(pageSection, index) in pageDesignerStore.desktop.pageSectionList"
            :key="index"
            class="relative w-full">
            <div :style="contentStyle(pageSection)">
                <div
                    v-for="cell in pageSection.pageCellList"
                    :key="cell.code"
                    class="absolute z-50"
                    :style="cellStyle(cell)">
                    <component
                        v-if="dataReady"
                        :is="'epgui-' + cell.componentType"
                        v-bind="getCellProps(cell)"
                        :key="getComponentKey(cell)" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { CSSProperties, onBeforeMount, reactive, ref } from 'vue';
    import { CellItemData, ContentData, PageCell, PageSection } from '@smartdesk/common/types';
    import { getDataConvert } from '@epgui/epg-components';
    import { cmsApi } from '@smartdesk/common/api';
    import { convertDsInfoMapToObject, convertStringArrayMapToObject } from '@smartdesk/design/utils';

    // 顶部楼层画布编辑器
    defineOptions({
        name: 'TopSectionCanvasEditor',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();

    // 是否已加载完所有需要的数据（用于 v-if Gate）
    const dataReady = ref(false);

    // key：内容编码，value：内容数据
    const contentMap = reactive<Map<string, ContentData>>(new Map());

    // key：数据源编码，value：内容数据数组
    const contentListMap = reactive<Map<string, ContentData[]>>(new Map());

    // 坑位到坑位数据的映射，key：坑位编码，value：内容数据数组
    const cellContentMap = reactive<Map<string, CellItemData[]>>(new Map());

    // 内容样式
    const contentStyle = (pageSection: PageSection): CSSProperties => {
        return {
            width: `${pageSection.layout.rect.width}px`,
            height: `${pageSection.layout.rect.height}px`,
            position: 'relative',
        };
    };

    // 坑位样式
    const cellStyle = (cell: PageCell): CSSProperties => {
        return {
            top: `${cell.layout.rect.top}px`,
            left: `${cell.layout.rect.left}px`,
            width: `${cell.layout.rect.width}px`,
            height: `${cell.layout.rect.height}px`,
            position: 'absolute',
            zIndex: '51',
        };
    };

    // 传入动态组件的属性
    const getCellProps = (cell: PageCell) => {
        const cellItemDataList = cellContentMap.get(cell.code);
        if (!cellItemDataList) {
            return {};
        }

        let cellItemData;
        // 需要根据坑位的 dsParam.size 决定返回几个数据
        const size = cell?.dsParams?.size ?? 1;
        if (size > 1) {
            // 返回 size 个 pageCellItem
            cellItemData = cellItemDataList?.slice(0, size) ?? [];
        } else {
            const pageCellItem = cell.pageCellItemList[0] ?? ({} as any);
            cellItemData =
                cellItemDataList?.find(
                    (cellItemData: CellItemData) => cellItemData.itemCode === pageCellItem?.dataCode
                ) ?? cellItemDataList[0];
        }

        return {
            ...cell.layout.props,
            rect: {
                top: 0,
                left: 0,
                width: cell.layout.rect.width,
                height: cell.layout.rect.height,
            },
            data:
                cellItemData instanceof Array
                    ? cellItemData.map((item: any) => getDataConvert(cell.componentType)(item ?? {}))
                    : getDataConvert(cell.componentType)(cellItemData ?? ({} as any)),
        };
    };

    // 获取内容数据
    const getContent = async () => {
        // key：内容类型，value：内容编码数组
        const contentFormMap = new Map<string, string[]>();
        pageDesignerStore.desktop.pageSectionList?.forEach((pageSection) => {
            // 构建 contentFormMap
            pageSection.pageCellList?.forEach((pageCell) => {
                pageCell.pageCellItemList?.forEach((pageCellItem) => {
                    if (pageCellItem.dataType && pageCellItem.dataCode) {
                        if (contentFormMap.has(pageCellItem.dataType)) {
                            // 合并并去重
                            const existingCodes = contentFormMap.get(pageCellItem.dataType)!;
                            const merged = Array.from(new Set([...existingCodes, ...[pageCellItem.dataCode]]));
                            contentFormMap.set(pageCellItem.dataType, merged);
                        } else {
                            // 新建项
                            contentFormMap.set(pageCellItem.dataType, [...[pageCellItem.dataCode]]);
                        }
                    }
                });
            });
        });

        const res = await cmsApi.searchContent(convertStringArrayMapToObject(contentFormMap));
        if (res.code === 200) {
            // 清空旧数据
            contentMap.clear();
            for (const [key, value] of Object.entries(res.result)) {
                contentMap.set(key, value);
            }
        }
    };

    // 获取楼层内容数据
    const getSectionContent = async () => {
        // key：数据源类型，value：数据源编码、数量 的数组
        const contentSectionFormMap = new Map<string, Array<{ dsCode: string; size: number }>>();
        pageDesignerStore.desktop.pageSectionList?.forEach((pageSection) => {
            // 构建 contentSectionFormMap
            if (pageSection.dsCode && pageSection.dsType) {
                const size =
                    pageSection.pageCellList?.reduce((acc, pageCell) => acc + (pageCell.dsParams?.size ?? 1), 0) ?? 1;
                const entry = {
                    dsCode: pageSection.dsCode,
                    size,
                };
                if (contentSectionFormMap.has(pageSection.dsType)) {
                    contentSectionFormMap.get(pageSection.dsType)!.push(entry);
                } else {
                    contentSectionFormMap.set(pageSection.dsType, [entry]);
                }
            }
        });

        const res = await cmsApi.searchSectionContent(convertDsInfoMapToObject(contentSectionFormMap));
        if (res.code === 200) {
            // 清空旧数据
            contentListMap.clear();
            for (const [key, value] of Object.entries(res.result)) {
                contentListMap.set(key, value);
            }
        }
    };

    // 初始化坑位元素内容
    const initCellItemData = async () => {
        await Promise.all([
            // 获取楼层内容数据
            getSectionContent(),

            // 获取内容数据
            getContent(),
        ]);
    };

    // 转换 cellContentMap
    const batchConvertToCellItemData = () => {
        for (const pageSection of pageDesignerStore.desktop.pageSectionList) {
            // 预先获取 contentList，避免重复查询
            const contentList = contentListMap.get(pageSection.dsCode) || [];
            let contentIndex = 0;

            for (const pageCell of pageSection.pageCellList) {
                const { code, dsParams, pageCellItemList } = pageCell;
                const size = dsParams?.size ?? 1;

                // 判断数据优先级
                const isPageSectionData =
                    pageDesignerStore.getContentDataPriority(pageSection, pageCell) === 'pageSection';

                if (isPageSectionData) {
                    // 页面楼层数据处理
                    const cellItemDataList = new Array(size);

                    for (let j = 0; j < size; j++) {
                        const content = contentList[contentIndex++];
                        cellItemDataList[j] = {
                            ...pageDesignerStore.convertContentDataToCellItemData(pageCell, content),
                            content,
                        };
                    }

                    cellContentMap.set(code, cellItemDataList);
                } else if (pageCellItemList) {
                    // 坑位数据处理 - 只遍历一次
                    const cellItemDataList = new Array(pageCellItemList.length);

                    for (let i = 0; i < pageCellItemList.length; i++) {
                        const pageCellItem = pageCellItemList[i];
                        const content = contentMap.get(pageCellItem.dataCode);

                        // 创建或更新 cellItemData
                        const cellItemData = pageCellItem.itemData || {};
                        if (content) {
                            cellItemData.content = content;
                        }

                        // 更新引用
                        pageCellItem.itemData = cellItemData;
                        cellItemDataList[i] = cellItemData;
                    }

                    cellContentMap.set(code, cellItemDataList);
                }
            }
        }
    };

    // 组件 key
    const getComponentKey = (cell: PageCell) => {
        return cell.code + '-' + JSON.stringify(getCellProps(cell));
    };

    // 初始化：加载顶部楼层坑位数据、转换 cellContentMap
    onBeforeMount(async () => {
        await initCellItemData();
        batchConvertToCellItemData();
        dataReady.value = true;
    });
</script>
