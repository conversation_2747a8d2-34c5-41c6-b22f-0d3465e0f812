<template>
    <div class="section-nav-panel" :class="{ collapsed: isCollapsed }">
        <div class="panel-header transform-all" @click.stop="toggleCollapse">
            <el-icon class="trigger-icon" :class="{ 'is-collapsed': isCollapsed }">
                <ArrowRight />
            </el-icon>
            <span class="title" v-show="!isCollapsed">楼层导航</span>
            <el-switch
                v-if="!isCollapsed"
                v-model="pageDesignerStore.showDeletedPageSections"
                size="default"
                inline-prompt
                :active-text="'显示预删除'"
                :inactive-text="'隐藏预删除'"
                @change="pageDesignerStore.switchDeletedPageSections"
                @click.stop="pageDesignerStore.switchDeletedPageSections" />
        </div>
        <div class="panel-content" v-show="!isCollapsed">
            <vue-draggable
                :disabled="
                    !canEdit(pageDesignerStore.page) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.PAGE.EDIT, {
                        type: 'org',
                        value: pageDesignerStore.page.orgId,
                    })
                "
                v-model="pageSections"
                ghost-class="ghost"
                :animation="200"
                handle=".handle"
                @end="updateOrderNo">
                <div
                    v-for="(section, index) in pageSections"
                    :key="section.code"
                    :title="section.name || `楼层${index + 1}`"
                    @click="handleSectionClick(section.code)"
                    class="py-1">
                    <el-popover placement="left" trigger="hover" width="400">
                        <template #reference>
                            <div
                                class="handle flex items-center gap-1 hover:bg-blue-100 text-gray-600"
                                :class="{
                                    'bg-blue-200': pageDesignerStore.selectedElementCode === section.code,
                                }">
                                <div>{{ section.orderNo }}.</div>
                                <div
                                    class="flex items-center w-[calc(100%-16px)]"
                                    :class="{
                                        'text-red-400': section.delFlag !== 0,
                                    }">
                                    <span class="truncate flex-1">{{ section.name || `楼层${index + 1}` }}</span>
                                    <span v-if="section.delFlag !== 0" class="text-red-400">预删除</span>
                                </div>
                            </div>
                        </template>

                        <template #default>
                            <el-image
                                class="w-full h-full"
                                fit="scale-down"
                                preview-teleported
                                hide-on-click-modal
                                :src="section.icon"
                                :preview-src-list="[section.icon]">
                                <template #error>
                                    <image-error-fallback text="图片损坏" />
                                </template>
                            </el-image>
                        </template>
                    </el-popover>
                </div>
            </vue-draggable>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, ref } from 'vue';
    import { ArrowRight } from '@element-plus/icons-vue';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { VueDraggable } from 'vue-draggable-plus';
    import { EventManager, usePermissionStore } from '@chances/portal_common_core';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { useFeedback } from '@smartdesk/common/composables';
    import { PageSectionListRefreshEvent } from '@smartdesk/design/events/page-section-list-refresh-event.ts';

    // 页面设计器页面楼层导航编辑器
    defineOptions({
        name: 'PageSectionNavigationEditor',
    });

    // 事件
    const emit = defineEmits(['navigate']);

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const permissionStore = usePermissionStore();
    const feedback = useFeedback();

    // 是否展开
    const isCollapsed = ref<boolean>(true);

    // 页面楼层列表
    const pageSections = computed({
        get: () => {
            return pageDesignerStore.page.pageSectionList.filter(
                (section) =>
                    (pageDesignerStore.showDeletedPageSections && section.delFlag === -1) || section.delFlag === 0
            );
        },
        set: (value) => {
            pageDesignerStore.page.pageSectionList = value;
        },
    });

    // 更新页面楼层排序
    const updateOrderNo = async () => {
        pageSections.value.forEach((item, index) => {
            item.orderNo = index + 1;
        });

        const res = await feedback.confirm('确定要保存页面楼层排序吗？', '确认操作', 'warning');
        if (!res) {
            return;
        }

        await pageDesignerStore.updatePageSectionOrderNo();
        // 发布 page-section-list-refresh-event 事件
        const eventManager = new EventManager<PageSectionListRefreshEvent>(PageSectionListRefreshEvent.type);
        eventManager.publish(new PageSectionListRefreshEvent());
    };

    // 点击展开
    const toggleCollapse = () => {
        isCollapsed.value = !isCollapsed.value;
    };

    // 处理楼层点击，定位到对应页面楼层
    const handleSectionClick = (code: string) => {
        emit('navigate', code);
    };
</script>

<style scoped>
    .section-nav-panel {
        background: white;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border-radius: 6px;
        width: 240px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        margin: 0;
        padding: 0;
    }

    .section-nav-panel.collapsed {
        width: 48px;
    }

    .panel-header {
        padding: 12px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        cursor: pointer;
    }

    .panel-header:hover {
        background: #f5f7fa;
    }

    .trigger-icon {
        transition: transform 0.3s;
    }

    .trigger-icon.is-collapsed {
        transform: rotate(180deg);
    }

    .title {
        flex: 1;
        font-weight: 500;
        color: #333;
        white-space: nowrap;
    }

    .panel-content {
        padding: 8px;
        max-height: calc(100vh - 250px);
        overflow-y: auto;
    }

    .ghost {
        opacity: 0.5;
        background: #c8ebfb;
    }

    .handle {
        cursor: pointer;
    }
</style>
