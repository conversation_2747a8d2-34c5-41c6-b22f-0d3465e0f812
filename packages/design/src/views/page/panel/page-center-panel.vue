<template>
    <base-canvas ref="baseCanvasRef" :content-width="contentWidth">
        <template #navigation>
            <page-section-navigation-editor
                class="absolute top-[70px] right-[10px] z-[22]"
                @navigate="pageDesignerStore.scrollToPageSection" />
        </template>

        <template #overlay>
            <desktop-canvas-editor />
        </template>
    </base-canvas>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref } from 'vue';
    import DesktopCanvasEditor from '../editor/desktop-canvas-editor.vue';
    import PageSectionNavigationEditor from '../editor/page-section-navigation-editor.vue';
    import { usePageDesignerStore } from '@smartdesk/design/stores';

    // 页面设计器中心面板
    defineOptions({
        name: 'PageCenterPanel',
    });

    // 基础画布引用
    const baseCanvasRef = ref<HTMLCanvasElement | null>(null);

    // pinia store
    const pageDesignerStore = usePageDesignerStore();

    // 内容宽度
    const contentWidth = computed<number>(() => {
        return (
            pageDesignerStore.page.layout.layout.rect.width +
            (pageDesignerStore.desktopLayout.sections?.find((item) => item.component === 'page')?.layout.rect.left ?? 0)
        );
    });

    // 组件挂载后注册画布引用到 store
    onMounted(() => {
        if (baseCanvasRef.value) {
            pageDesignerStore.registerBaseCanvas(baseCanvasRef.value);
        }
    });
</script>
