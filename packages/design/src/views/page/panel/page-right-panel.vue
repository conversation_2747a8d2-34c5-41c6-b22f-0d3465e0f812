<template>
    <div class="flex flex-col h-full p-2">
        <div class="flex">
            <div>组件：{{ pageDesignerStore.getElementTypeName() }}</div>
            <el-divider direction="vertical" />
            <el-tooltip :content="pageDesignerStore.selectedElement?.name" teleported>
                <div class="flex-1 truncate">
                    {{ pageDesignerStore.selectedElement?.name }}
                </div>
            </el-tooltip>
        </div>

        <el-tabs v-model="pageDesignerStore.rightPanelActiveTab">
            <el-tab-pane
                label="数据配置"
                name="cellItem"
                v-if="pageDesignerStore.selectedElementType === 'Cell' && !pageDesignerStore.batchEditMode">
                <page-cell-item-editor v-if="pageDesignerStore.rightPanelActiveTab === 'cellItem'" />
            </el-tab-pane>
            <el-tab-pane
                label="坑位配置"
                name="cellConfig"
                v-if="pageDesignerStore.selectedElementType === 'Cell' && !pageDesignerStore.batchEditMode">
                <page-cell-config-editor v-if="pageDesignerStore.rightPanelActiveTab === 'cellConfig'" />
            </el-tab-pane>
            <el-tab-pane
                label="楼层配置"
                name="sectionConfig"
                v-if="pageDesignerStore.selectedElementType === 'PageSection' && !pageDesignerStore.batchEditMode">
                <page-section-config-editor v-if="pageDesignerStore.rightPanelActiveTab === 'sectionConfig'" />
            </el-tab-pane>
            <el-tab-pane
                label="楼层管理"
                name="pageSectionMgmt"
                v-if="pageDesignerStore.selectedElementType === 'Page'">
                <page-section-mgmt-editor
                    ref="pageSectionMgmtRef"
                    v-if="pageDesignerStore.rightPanelActiveTab === 'pageSectionMgmt'" />
            </el-tab-pane>
            <el-tab-pane label="样式配置" name="props">
                <page-designer-props-editor
                    :show-save-button="true"
                    v-if="pageDesignerStore.rightPanelActiveTab === 'props'" />
            </el-tab-pane>
        </el-tabs>
    </div>
</template>

<script setup lang="ts">
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import PageCellConfigEditor from '../editor/page-cell-config-editor.vue';
    import PageCellItemEditor from '../editor/page-cell-item-editor.vue';
    import PageSectionConfigEditor from '../editor/page-section-config-editor.vue';
    import PageDesignerPropsEditor from '../editor/page-designer-props-editor.vue';
    import PageSectionMgmtEditor from '../editor/page-section-mgmt-editor.vue';
    import { onMounted, ref, watch } from 'vue';

    // 页面设计器右侧面板
    defineOptions({
        name: 'PageRightPanel',
    });

    // pinia store
    const pageDesignerStore = usePageDesignerStore();

    // ref
    const pageSectionMgmtRef = ref<InstanceType<typeof PageSectionMgmtEditor>>();

    // 切换 tab
    const changeTab = () => {
        if (pageDesignerStore.selectedElementType === 'Cell') {
            pageDesignerStore.rightPanelActiveTab = 'cellItem';
        } else if (pageDesignerStore.selectedElementType === 'PageSection') {
            pageDesignerStore.rightPanelActiveTab = 'sectionConfig';
        } else if (pageDesignerStore.selectedElementType === 'Page') {
            pageDesignerStore.rightPanelActiveTab = 'pageSectionMgmt';
        }
    };

    // 监听选中元素类型变化
    watch(
        () => pageDesignerStore.selectedElementType,
        () => {
            changeTab();
        }
    );

    onMounted(() => {
        changeTab();
    });

    defineExpose({
        pageSectionMgmtRef,
    });
</script>
