<template>
    <el-dialog
        v-if="dialogVisible"
        v-model="dialogVisible"
        :title="getTitle()"
        destroy-on-close
        width="60%"
        :before-close="handleCancel"
        append-to-body>
        <el-form inline label-suffix=":" label-width="100px">
            <el-form-item label="页面" prop="pageCode">
                <el-select
                    v-model="pageSectionSearchForm.pageCode"
                    placeholder="请选择页面"
                    size="default"
                    style="width: 160px"
                    clearable
                    @click="getPageList">
                    <el-option v-for="item in pageOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item label="名称" prop="name">
                <el-input
                    v-model="pageSectionSearchForm.name"
                    size="default"
                    clearable
                    placeholder="请输入名称"
                    style="width: 160px" />
            </el-form-item>
            <el-form-item label="编码" prop="code">
                <el-input
                    v-model="pageSectionSearchForm.code"
                    size="default"
                    clearable
                    placeholder="请输入编码"
                    style="width: 160px" />
            </el-form-item>
            <el-form-item label="上下线状态" prop="onlineStatus">
                <el-select
                    v-model="pageSectionSearchForm.onlineStatus"
                    placeholder="请选择上下线状态"
                    size="default"
                    style="width: 160px"
                    clearable>
                    <el-option
                        v-for="item in enumStore.getNameCodeNumberOptionsByKey('onlineStatus')"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="审核状态" prop="auditStatus">
                <el-select
                    v-model="pageSectionSearchForm.auditStatus"
                    placeholder="请选择审核状态"
                    size="default"
                    style="width: 160px"
                    clearable>
                    <el-option
                        v-for="item in enumStore.getNameCodeNumberOptionsByKey('auditStatus')"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
            <el-form-item label="可见状态" prop="visibleStatus">
                <el-select
                    v-model="pageSectionSearchForm.visibleStatus"
                    placeholder="请选择可见状态"
                    size="default"
                    style="width: 160px"
                    clearable>
                    <el-option
                        v-for="item in enumStore.getNameCodeNumberOptionsByKey('visibleStatus')"
                        :key="item.code"
                        :label="item.name"
                        :value="item.code" />
                </el-select>
            </el-form-item>
        </el-form>

        <base-table-with-pagination
            :total="totalElements"
            :current-page="currentPage"
            :page-size="pageSize"
            @update:currentPage="handleCurrentPageChange"
            @update:pageSize="handleSizeChange">
            <template #table>
                <el-table
                    :data="pageSectionList"
                    row-key="code"
                    border
                    @current-change="handleCurrentChange"
                    height="450"
                    :current-row-key="selectedRowCode"
                    highlight-current-row>
                    <el-table-column width="45">
                        <template #default="scope">
                            <el-radio
                                class="hidden-label"
                                v-model="selectedRowCode"
                                :label="scope.row.code"
                                :disabled="
                                    scope.row.code === pageDesignerStore.selectedElementCode && action === 'change'
                                "
                                @change="handleRowSelect(scope.row)">
                            </el-radio>
                        </template>
                    </el-table-column>
                    <el-table-column prop="pageName" label="页面名称" show-overflow-tooltip width="160">
                        <template #default="{ row }">
                            {{ pageOptions.find((item) => item.value === row.pageCode)?.label }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="name" label="名称" show-overflow-tooltip width="160" />
                    <el-table-column prop="code" label="编码" show-overflow-tooltip width="160" />
                    <el-table-column prop="icon" label="缩略图" show-overflow-tooltip width="160">
                        <template #default="{ row }">
                            <el-image
                                :src="row.icon"
                                :preview-src-list="[row.icon]"
                                hide-on-click-modal
                                preview-teleported
                                style="width: 100px; height: 50px">
                                <template #error>
                                    <image-error-fallback text="图片损坏" />
                                </template>
                            </el-image>
                        </template>
                    </el-table-column>
                    <el-table-column prop="status" label="状态" show-overflow-tooltip width="280">
                        <template #default="{ row }">
                            {{ enumStore.getLabelByKeyAndValue('delFlag', row.delFlag) }}
                            |
                            {{ enumStore.getLabelByKeyAndValue('enableStatus', row.status) }}
                            |
                            {{ enumStore.getLabelByKeyAndValue('visibleStatus', row.visibleStatus) }}
                            |
                            {{ enumStore.getLabelByKeyAndValue('onlineStatus', row.onlineStatus) }}
                            |
                            {{ enumStore.getLabelByKeyAndValue('auditStatus', row.auditStatus) }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="modifiedTime" label="修改时间" width="180">
                        <template #default="{ row }">
                            {{ format(parseISO(row.modifiedTime), 'yyyy-MM-dd HH:mm:ss') }}
                        </template>
                    </el-table-column>
                    <el-table-column prop="modifiedBy" label="修改人" show-overflow-tooltip width="160" />
                </el-table>
            </template>
        </base-table-with-pagination>

        <template #footer>
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref, watch } from 'vue';
    import { PageSection, PageSectionSearchForm } from '@smartdesk/common/types';
    import { pageApi, pageSectionApi } from '@smartdesk/common/api';
    import { LabelValue, useEnumStore } from '@chances/portal_common_core';
    import { format, parseISO } from 'date-fns';
    import { usePageDesignerStore } from '@smartdesk/design/stores';
    import { DEFAULT_PAGE_SIZE } from '@smartdesk/common/constant';

    // 页面楼层选择弹框
    defineOptions({
        name: 'PageSectionChangeDialog',
    });

    // 参数
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
        action: {
            type: String,
            required: true,
        },
    });

    // 事件
    const emit = defineEmits(['update:visible', 'submit']);

    // pinia store
    const pageDesignerStore = usePageDesignerStore();
    const enumStore = useEnumStore();

    // 控制弹窗显示
    const dialogVisible = computed({
        get: () => props.visible,
        set: (val) => emit('update:visible', val),
    });

    // 页面楼层查询表单
    const pageSectionSearchForm = ref<Partial<PageSectionSearchForm>>({
        siteCode: pageDesignerStore.page.siteCode,
        delFlag: 0,
        status: 1,
    });

    // 分页
    const currentPage = ref(1);
    const pageSize = ref(DEFAULT_PAGE_SIZE);
    const totalElements = ref(0);

    // 页面楼层列表
    const pageSectionList = ref<PageSection[]>([]);

    // 选中行
    const selectedRowCode = ref<string>();
    const selectedRow = ref<any>();

    // 页面
    const pageOptions = ref<LabelValue[]>([]);

    const getTitle = () => {
        if (props.action === 'add') {
            return '新增页面楼层';
        }
        if (props.action === 'change') {
            return '更换页面楼层';
        }
        return '引用页面楼层';
    };

    // 行选中事件
    const handleRowSelect = (row: PageSection) => {
        selectedRowCode.value = row.code;
        selectedRow.value = row;
    };

    // 查询页面楼层列表
    const getPageSectionList = async () => {
        const res = await pageSectionApi.findPageSectionList(pageSectionSearchForm.value, {
            page: currentPage.value - 1,
            size: pageSize.value,
            sort: 'id,desc',
        });
        if (res.code === 200) {
            pageSectionList.value = res.result;
            totalElements.value = Number(res.page.totalElements);
        }
    };

    // 处理页码变化
    const handleCurrentPageChange = (val: number) => {
        currentPage.value = val;
        getPageSectionList();
    };

    // 处理页码变化
    const handleSizeChange = (val: number) => {
        pageSize.value = val;
        currentPage.value = 1;
        getPageSectionList();
    };

    // 处理当前页变化
    const handleCurrentChange = (currentRow: any) => {
        selectedRowCode.value = currentRow ? currentRow.code : null;
        selectedRow.value = currentRow;
    };

    // 取消处理
    const handleCancel = () => {
        dialogVisible.value = false;
    };

    // 提交处理
    const handleSubmit = () => {
        emit('submit', selectedRow.value);
        dialogVisible.value = false;
    };

    // 查询页面列表
    const getPageList = async () => {
        const res = await pageApi.getPageList(
            {
                delFlag: 0,
                status: 1,
                siteCode: pageSectionSearchForm.value.siteCode,
            },
            { paged: false }
        );
        if (res.code === 200) {
            pageOptions.value = res.result.map((item) => {
                return {
                    value: item.code,
                    label: item.name,
                };
            });
        }
    };

    // 页面楼层查询表单变动，查询页面楼层列表
    watch(
        () => pageSectionSearchForm.value,
        async () => {
            await getPageSectionList();
        },
        { deep: true }
    );

    onMounted(async () => {
        await getPageSectionList();
        await getPageList();
    });
</script>

<style scoped>
    :deep(.hidden-label) .el-radio__label {
        display: none !important;
    }
</style>
