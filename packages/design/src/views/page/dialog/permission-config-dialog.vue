<template>
    <el-dialog title="权限配置" v-model="dialogVisible" width="30%" :before-close="handleCancel">
        <el-form label-width="100px" label-suffix=":">
            <el-form-item label="组织" prop="orgId">
                <el-popover placement="bottom-start" :width="370" trigger="click" v-model:visible="popoverVisible">
                    <template #reference>
                        <el-input
                            v-model="selectedLabel"
                            readonly
                            placeholder="请选择组织"
                            clearable
                            @clear="clearSelection">
                            <template #suffix>
                                <el-icon>
                                    <ArrowDown />
                                </el-icon>
                            </template>
                        </el-input>
                    </template>

                    <el-input v-model="searchKeyword" placeholder="搜索..." clearable class="mb-2">
                        <template #prefix>
                            <el-icon>
                                <Search />
                            </el-icon>
                        </template>
                    </el-input>

                    <div class="max-h-64 overflow-y-auto">
                        <div
                            class="px-3 py-2 border-b border-gray-200 cursor-pointer hover:bg-gray-50 text-red-600 text-sm"
                            @click="clearAndClose">
                            <el-icon class="mr-1">
                                <Close />
                            </el-icon>
                            清空选择
                        </div>

                        <el-tree
                            ref="treeRef"
                            :data="orgTree"
                            :props="{
                                children: 'children',
                                label: 'name',
                                disabled: (data: any) => !(data as Dimension).hasPermission,
                            }"
                            :filter-node-method="filterNode"
                            @node-click="handleNodeClick"
                            node-key="id"
                            :current-node-key="currentNodeKey"
                            highlight-current>
                            <template #default="{ node, data }">
                                <span
                                    :class="{
                                        'text-gray-400': !data.hasPermission,
                                        'cursor-not-allowed': !data.hasPermission,
                                    }">
                                    {{ node.label }}
                                    <el-icon v-if="!data.hasPermission" class="ml-1"><Lock /></el-icon>
                                </span>
                            </template>
                        </el-tree>
                    </div>
                </el-popover>
            </el-form-item>
        </el-form>

        <template #footer>
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { computed, nextTick, onMounted, ref, watch, watchEffect } from 'vue';
    import { ArrowDown, Close, Lock, Search } from '@element-plus/icons-vue';
    import { ElTree } from 'element-plus';
    import { dimensionApi } from '@smartdesk/common/api';
    import { useOperatorStore } from '@chances/portal_common_core';
    import { Dimension } from '@smartdesk/common/types';

    // 参数
    const props = defineProps({
        visible: { type: Boolean, default: false },
        item: {
            type: Object as () => { orgId?: string | number },
            required: true,
        },
    });

    // 事件
    const emit = defineEmits(['update:visible', 'submit', 'update:item']);

    // pinia store
    const operatorStore = useOperatorStore();

    // 控制弹窗显示
    const dialogVisible = computed({
        get: () => props.visible,
        set: (val) => emit('update:visible', val),
    });

    // 数据
    const innerItem = computed<{ orgId?: string | number }>({
        get: () => props.item,
        set: (val) => emit('update:item', val),
    });

    // 组织树
    const orgTree = ref<Dimension[]>([]);
    const popoverVisible = ref(false);
    const searchKeyword = ref('');
    const selectedLabel = ref('');
    const treeRef = ref<InstanceType<typeof ElTree>>();
    const currentNodeKey = ref<string | number | undefined>();

    const handleCancel = () => {
        dialogVisible.value = false;
    };

    const handleSubmit = () => {
        emit('submit');
    };

    const filterNode = (value: string, node: any) => {
        return !value || node.name.toLowerCase().includes(value.toLowerCase());
    };

    const handleNodeClick = (dimension: Dimension) => {
        // 完全阻止无权限节点的操作
        if (!dimension.hasPermission) {
            return false;
        }

        // 有权限节点的正常处理
        selectedLabel.value = dimension.name;
        currentNodeKey.value = dimension.id;
        innerItem.value.orgId = dimension.id;
        popoverVisible.value = false;
        return true;
    };

    const clearSelection = () => {
        selectedLabel.value = '';
        currentNodeKey.value = undefined;
        innerItem.value.orgId = undefined;
        treeRef.value?.setCurrentKey(undefined);
    };

    const clearAndClose = () => {
        clearSelection();
        popoverVisible.value = false;
    };

    const getUserOrgIds = (): number[] => {
        return operatorStore?.loginModel?.dimensionMap?.org?.map(Number) || [];
    };

    const markOrgTreePermissions = (tree: Dimension[], userOrgIds: number[]): Dimension[] => {
        return tree.map((node) => {
            const hasPermission = userOrgIds.includes(Number(node.id));
            return {
                ...node,
                hasPermission,
                children: node.children ? markOrgTreePermissions(node.children, userOrgIds) : [],
            };
        });
    };

    const fetchOrgTree = async () => {
        try {
            const res = await dimensionApi.findDimensionTree();
            if (res.code === 200) {
                orgTree.value = markOrgTreePermissions(res.result, getUserOrgIds());
            }
        } catch (error) {
            console.error('获取组织树失败:', error);
        }
    };

    const findNode = (nodes: Dimension[], id: string | number): Dimension | null => {
        if (!nodes) return null;
        for (const node of nodes) {
            if (node.id === id) return node;
            if (node.children) {
                const found = findNode(node.children, id);
                if (found) return found;
            }
        }
        return null;
    };

    watch(searchKeyword, (val) => {
        treeRef.value?.filter(val);
    });

    watchEffect(() => {
        if (innerItem.value.orgId !== undefined && orgTree.value.length > 0) {
            const node = findNode(orgTree.value, innerItem.value.orgId);
            if (node) {
                selectedLabel.value = node.name;
                currentNodeKey.value = node.id;
                nextTick(() => treeRef.value?.setCurrentKey(node.id));
            }
        } else if (innerItem.value.orgId === undefined) {
            clearSelection();
        }
    });

    onMounted(async () => {
        await fetchOrgTree();
    });
</script>
