<template>
    <el-dialog
        title="另存为楼层定义"
        v-if="dialogVisible"
        v-model="dialogVisible"
        destroy-on-close
        width="60%"
        :before-close="handleCancel"
        append-to-body>
        <div>
            <el-form :rules="rules" inline label-suffix=":" label-width="100px">
                <el-form-item label="名称" prop="name">
                    <el-input
                        v-model="sectionForm.name"
                        size="default"
                        clearable
                        placeholder="请输入名称"
                        style="width: 160px" />
                </el-form-item>
            </el-form>
        </div>

        <template #footer>
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit">确认</el-button>
        </template>
    </el-dialog>
</template>

<script setup lang="ts">
    import { computed, ref } from 'vue';
    import { Section } from '@smartdesk/common/types';

    // 页面楼层另存为楼层定义弹框
    defineOptions({
        name: 'PageSectionSaveAsSectionDialog',
    });

    // 参数
    const props = defineProps({
        visible: {
            type: Boolean,
            default: false,
        },
    });

    // 事件
    const emit = defineEmits(['update:visible', 'submit']);

    // 控制弹窗显示
    const dialogVisible = computed({
        get: () => props.visible,
        set: (val) => emit('update:visible', val),
    });

    // 楼层定义表单
    const sectionForm = ref<Partial<Section>>({});

    // 表单校验规则
    const rules = {
        name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
    };

    // 取消处理
    const handleCancel = () => {
        dialogVisible.value = false;
    };

    // 提交处理
    const handleSubmit = () => {
        emit('submit', sectionForm.value);
        dialogVisible.value = false;
    };
</script>
