<template>
    <div class="h-screen w-full bg-white" v-if="isLoaded" @keydown="handleKeydown" tabindex="0">
        <three-panel-layout :header-height="headerHeight">
            <template #top>
                <section-top-panel @save="handleSectionDesignerSave" @publish="handleSectionDesignerPublish" />
            </template>

            <template #left>
                <section-left-panel ref="sectionLeftPanelRef" />
            </template>

            <template #center>
                <section-center-panel
                    ref="sectionCenterPanelRef"
                    @confirm-batch="handleConfirmBatch"
                    @cancel-batch="handleCancelBatch" />
            </template>

            <template #right>
                <section-right-panel ref="sectionRightPanelRef" />
            </template>
        </three-panel-layout>
    </div>
</template>

<script setup lang="ts">
    import { nextTick, onBeforeMount, onBeforeUnmount, onMounted, onUnmounted, ref } from 'vue';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { usePanelLayoutStore, useSectionDesignerStore } from '@smartdesk/design/stores';
    import SectionTopPanel from './panel/section-top-panel.vue';
    import SectionLeftPanel from './panel/section-left-panel.vue';
    import SectionCenterPanel from './panel/section-center-panel.vue';
    import SectionRightPanel from './panel/section-right-panel.vue';
    import { EventManager } from '@chances/portal_common_core';
    import { publishApi } from '@smartdesk/common/api';
    import { useFeedback } from '@smartdesk/common/composables';
    import { CellLayoutFile } from '@smartdesk/common/types';
    import { LayoutListRefreshEvent } from '@smartdesk/design/events';
    import { useRoute } from 'vue-router';

    // 楼层定义设计器
    defineOptions({
        name: 'SectionDesigner',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const panelLayoutStore = usePanelLayoutStore();
    const currentSiteStore = useSiteStore();
    const feedback = useFeedback();
    const route = useRoute();

    // ref
    const sectionLeftPanelRef = ref<InstanceType<typeof SectionLeftPanel> | null>();
    const sectionCenterPanelRef = ref<any>();
    const sectionRightPanelRef = ref<any>();

    // 顶部高度
    const headerHeight = ref<number>(50);

    // 楼层定义编码
    const sectionCode = ref<string>('');

    // 是否加载结束
    const isLoaded = ref<boolean>(false);

    // 校验是否楼层定义的所有坑位都设置了组件
    const validateSection = () => {
        const noComponentCellIndex = sectionDesignerStore.sectionLayout.cells.findIndex(
            (cell: CellLayoutFile) => !cell.component
        );
        if (noComponentCellIndex !== -1) {
            sectionDesignerStore.currentCellIndex = noComponentCellIndex;
            feedback.error('存在坑位没有设置组件');
            return false;
        }
        return true;
    };

    // 处理楼层定义设计器保存
    const handleSectionDesignerSave = async () => {
        // 校验楼层定义
        if (!validateSection()) {
            return;
        }

        // 设置当前为楼层选中
        sectionDesignerStore.selectCell(-1);
        await nextTick(() => {});

        // 上传楼层定义图片
        const res = await sectionCenterPanelRef?.value?.sectionCanvasEditorRef?.uploadToServer();
        if (res && res.code === 200) {
            sectionDesignerStore.section.icon = res.result;
        }

        await sectionDesignerStore.saveSection();
    };

    // 处理楼层定义设计器送审
    const handleSectionDesignerPublish = async () => {
        // 校验楼层定义
        if (!validateSection()) {
            return;
        }

        const res = await publishApi.publishSelf('Section', sectionDesignerStore.section.code, 'CREATE');
        if (res.code === 200) {
            feedback.success('送审楼层定义成功');
            await sectionDesignerStore.refreshSection();
        } else {
            feedback.error('送审楼层定义失败：' + res.msg);
        }
    };

    // 处理确认批量
    const handleConfirmBatch = () => {
        sectionDesignerStore.handleConfirmBatch(
            sectionRightPanelRef.value?.sectionDesignerPropsEditorRef?.elementProps
        );
    };

    // 处理取消批量
    const handleCancelBatch = () => {
        sectionDesignerStore.handleCancelBatch();
    };

    // 初始化
    const init = async () => {
        sectionDesignerStore.section.code = sectionCode.value;
        await sectionDesignerStore.refreshSection();
        currentSiteStore.switchSite(sectionDesignerStore.section.siteCode);
    };

    // 初始化前根据楼层定义编码查询楼层定义
    onBeforeMount(async () => {
        sectionCode.value = route.query.sectionCode as string;
        await init();
        isLoaded.value = true;
    });

    // 键盘事件处理
    const handleKeydown = (event: KeyboardEvent) => {
        // 检查是否在输入框中，如果是则不处理快捷键
        const target = event.target as HTMLElement;
        if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
            return;
        }

        // Ctrl + Z: 撤销
        if (event.ctrlKey && event.key === 'z' && !event.shiftKey) {
            event.preventDefault();
            sectionDesignerStore.undo();
        }

        // Ctrl + Shift + Z: 重做
        if (event.ctrlKey && event.shiftKey && event.key === 'Z') {
            event.preventDefault();
            sectionDesignerStore.redo();
        }
    };

    // 全局键盘事件监听
    const handleGlobalKeydown = (event: KeyboardEvent) => {
        handleKeydown(event);
    };

    // 初始化
    onMounted(() => {
        // 订阅 layout-list-refresh-event 事件
        const eventManager = new EventManager<LayoutListRefreshEvent>(LayoutListRefreshEvent.type);
        eventManager.subscribe(() => {
            sectionLeftPanelRef.value?.layoutListSelectorRef?.getSectionLayout();
        });

        // 添加全局键盘事件监听
        document.addEventListener('keydown', handleGlobalKeydown);
    });

    // 卸载
    onBeforeUnmount(() => {
        panelLayoutStore.$clear();
        panelLayoutStore.$dispose();
        sectionDesignerStore.$clear();
        sectionDesignerStore.$dispose();
    });

    // 清理事件监听
    onUnmounted(() => {
        document.removeEventListener('keydown', handleGlobalKeydown);
    });
</script>

<style scoped>
    :deep(.el-tabs) {
        height: 100%;
    }

    :deep(.el-tab-pane) {
        height: 100%;
    }
</style>
