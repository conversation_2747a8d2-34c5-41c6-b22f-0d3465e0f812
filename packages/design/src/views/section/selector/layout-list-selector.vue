<template>
    <div class="flex flex-col h-full overflow-hidden">
        <div class="p-2 border-b border-gray-200" style="height: 100px">
            <el-input v-model="searchForm.name" placeholder="搜索布局" class="w-full" size="default" clearable>
                <template #prefix>
                    <el-icon>
                        <Search />
                    </el-icon>
                </template>
            </el-input>

            <tags-selector class="mt-2 mb-1" size="default" :tags="tagOptions" v-model="tagsList" />
        </div>

        <div class="p-4 w-full flex-1 overflow-y-auto" style="height: calc(100% - 100px)">
            <div class="flex flex-wrap gap-4">
                <div
                    v-for="(layout, index) in layoutList"
                    :key="index"
                    class="border border-gray-200 rounded-md bg-white cursor-pointer hover:shadow-md transition-all relative flex flex-col overflow-hidden select-none w-[calc(50%-0.5rem)]"
                    :class="{
                        'border-blue-500 ring-2 ring-blue-200': activeLayoutIndex === index,
                        'active:bg-gray-50': isDraggingIndex === index,
                    }"
                    :draggable="draggable"
                    @dragstart="(e) => onDragStart(index, layout, e)"
                    @dragend="onDragEnd"
                    @click="selectLayout(index)">
                    <div class="relative aspect-[2/1] overflow-hidden w-full">
                        <el-image
                            :src="layout.icon"
                            fit="cover"
                            class="w-full h-full object-cover cursor-pointer"
                            :preview-src-list="[layout.icon]"
                            hide-on-click-modal
                            preview-teleported
                            :z-index="3000"
                            @dragstart.prevent>
                            <template #error>
                                <image-error-fallback text="图片损坏" />
                            </template>
                        </el-image>
                    </div>
                    <div class="px-2 py-2 w-full flex justify-center items-center">
                        <span class="truncate text-sm font-medium w-full text-center" :title="layout.name">{{
                            layout.name
                        }}</span>
                    </div>
                </div>
            </div>

            <div v-if="layoutList.length === 0" class="text-center py-8 text-gray-400">
                <el-empty description="暂无布局" :image-size="60" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { Search } from '@element-plus/icons-vue';
    import { computed, onMounted, ref, watch } from 'vue';
    import { LabelValue, usePermissionStore } from '@chances/portal_common_core';
    import { Layout, LayoutSearchForm } from '@smartdesk/common/types';
    import { useLayoutListStore, useSectionDesignerStore } from '@smartdesk/design/stores';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';

    // 布局列表选择器
    defineOptions({
        name: 'LayoutListSelector',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const layoutListStore = useLayoutListStore();
    const permissionStore = usePermissionStore();

    // 坑位列表查询
    const searchForm = ref<Partial<LayoutSearchForm>>({
        type: 'section',
    });

    // 标签选项列表
    const tagOptions = ref<LabelValue[]>([
        {
            label: '1',
            value: '1',
        },
        {
            label: '2',
            value: '2',
        },
        {
            label: '3',
            value: '3',
        },
        {
            label: '4',
            value: '4',
        },
        {
            label: '5',
            value: '5',
        },
        {
            label: '6+',
            value: '6+',
        },
    ]);

    // 标签列表
    const tagsList = ref<string[]>([]);

    // 布局列表
    const layoutList = ref<Layout[]>([]);

    // 当前布局索引
    const activeLayoutIndex = ref<number | null>(null);

    // 拖拽布局索引
    const isDraggingIndex = ref<number | null>(null);

    // 是否可拖拽
    const draggable = computed<boolean>(() => {
        // 业务上可编辑、且有楼层定义编辑权限，才可以拖拽
        return (
            canEdit(sectionDesignerStore.section) &&
            permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                type: 'org',
                value: sectionDesignerStore.section.orgId,
            })
        );
    });

    // 获取布局列表
    const getSectionLayout = async () => {
        searchForm.value.tagsList = tagsList.value;
        if (tagsList.value.indexOf('-1') !== -1) {
            searchForm.value.tagsList = [];
        }

        const res = await layoutListStore.getLayoutListByParams(searchForm.value, {
            paged: false,
            sort: 'id,desc',
        });
        layoutList.value = res.result;
    };

    // 选择布局
    const selectLayout = (index: number) => {
        activeLayoutIndex.value = index;
    };

    // 拖拽布局开始
    const onDragStart = (index: number, layout: Layout, event: DragEvent) => {
        isDraggingIndex.value = index;
        event.dataTransfer?.setData('layout', JSON.stringify(layout));
        event.dataTransfer?.setData('application/json+layout', 'layout');
        event.dataTransfer!.effectAllowed = 'copy';
    };

    // 拖拽布局结束
    const onDragEnd = () => {
        isDraggingIndex.value = null;
    };

    // 监听名称
    watch(
        () => searchForm.value.name,
        () => {
            getSectionLayout();
        }
    );

    // 监听 tags
    watch(
        () => tagsList,
        () => {
            getSectionLayout();
        },
        { deep: true }
    );

    onMounted(() => {
        getSectionLayout();
    });

    defineExpose({
        getSectionLayout,
    });
</script>
