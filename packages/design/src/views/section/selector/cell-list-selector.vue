<template>
    <div class="flex flex-col h-full overflow-hidden">
        <div class="p-2 border-b border-gray-200">
            <el-input v-model="searchValue" placeholder="搜索坑位" class="w-full" size="default" clearable>
                <template #prefix>
                    <el-icon>
                        <Search />
                    </el-icon>
                </template>
            </el-input>
        </div>

        <div class="flex-1 overflow-y-auto p-2">
            <div class="flex flex-col gap-1">
                <div
                    class="flex items-center px-4 py-2 font-bold text-base cursor-pointer select-none"
                    :class="
                        sectionDesignerStore.currentCellIndex === -1
                            ? 'bg-blue-50 text-blue-900'
                            : 'hover:bg-gray-50 text-gray-900'
                    "
                    @click="onClickSection">
                    <span class="flex-1 min-w-0 truncate">{{
                        sectionDesignerStore.section?.name || '未命名楼层'
                    }}</span>
                </div>
                <div
                    v-for="(cell, idx) in filteredCells"
                    :key="cell.code"
                    class="flex items-center pl-8 pr-4 py-2 font-medium text-base cursor-pointer select-none"
                    :class="
                        sectionDesignerStore.currentCellIndex === idx
                            ? 'bg-blue-50 text-blue-900'
                            : 'hover:bg-gray-50 text-gray-800'
                    "
                    @click="onClickCell(idx)">
                    <span class="flex-1 min-w-0 truncate">{{ cell.name || `坑位${idx + 1}` }}</span>

                    <div class="flex items-center gap-1">
                        <el-tooltip content="删除" placement="top">
                            <el-button
                                :disabled="
                                    !canEdit(sectionDesignerStore.section) ||
                                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                        type: 'org',
                                        value: sectionDesignerStore.section.orgId,
                                    })
                                "
                                type="danger"
                                size="small"
                                circle
                                plain
                                icon="Delete"
                                @click="onClickDeleteCell(idx)" />
                        </el-tooltip>
                    </div>
                </div>

                <div v-if="filteredCells.length === 0" class="text-center py-8 text-gray-400">
                    <el-empty description="没有匹配的坑位" :image-size="60" />
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, ref } from 'vue';
    import { useSectionDesignerStore } from '@smartdesk/design/stores';
    import { Search } from '@element-plus/icons-vue';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';

    // 坑位列表选择器
    defineOptions({
        name: 'CellListSelector',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const permissionStore = usePermissionStore();

    // 坑位列表查询
    const searchValue = ref<string>('');

    // 过滤后的坑位列表
    const filteredCells = computed(() => {
        if (!searchValue.value) {
            return sectionDesignerStore.sectionLayout.cells;
        }

        return sectionDesignerStore.sectionLayout.cells.filter((cell, idx) =>
            (cell.name || `坑位${idx + 1}`).toLowerCase().includes(searchValue.value.toLowerCase())
        );
    });

    // 点击楼层定义
    const onClickSection = () => {
        sectionDesignerStore.selectCell(-1);
    };

    // 点击坑位
    const onClickCell = (idx: number) => {
        sectionDesignerStore.selectCell(idx);
    };

    // 点击删除坑位
    const onClickDeleteCell = (idx: number) => {
        sectionDesignerStore.removeCellByIndex(idx);
    };
</script>
