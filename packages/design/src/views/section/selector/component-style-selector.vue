<template>
    <div class="flex flex-col">
        <el-tree-select
            v-model="selectedValue"
            :disabled="disabled"
            :data="treeData"
            :props="treeProps"
            :placeholder="placeholder"
            filterable
            :filter-node-method="filterNode"
            :popper-append-to-body="true"
            @node-click="handleNodeClick">
            <template #default="{ node, data }">
                <div
                    v-if="data.type === 'style'"
                    class="tree-node flex items-baseline py-1 px-2 rounded hover:bg-blue-50 transition-colors">
                    <el-popover
                        placement="left"
                        trigger="hover"
                        :width="240"
                        :show-after="300"
                        popper-class="popover-preview">
                        <template #reference>
                            <div class="flex items-baseline w-full">
                                <el-icon class="mr-2 text-xl text-gray-500">
                                    <Brush />
                                </el-icon>
                                <span class="text-gray-800 font-medium">{{ node.label }}</span>
                            </div>
                        </template>
                        <el-image
                            :src="data.icon"
                            fit="cover"
                            class="w-full h-40 object-cover rounded-lg shadow-md"
                            :preview-src-list="[data.icon]"
                            hide-on-click-modal
                            preview-teleported
                            :z-index="3000">
                            <template #error>
                                <image-error-fallback text="图片损坏" />
                            </template>
                        </el-image>
                    </el-popover>
                </div>
                <div v-else class="tree-node flex items-baseline py-1 px-2 rounded hover:bg-gray-50 transition-colors">
                    <el-icon class="mr-2 text-xl text-gray-500">
                        <Folder v-if="data.type === 'catalog'" />
                        <Box v-else-if="data.type === 'component'" />
                    </el-icon>
                    <span class="text-gray-800 font-medium">{{ node.label }}</span>
                </div>
            </template>
        </el-tree-select>

        <div v-if="selectedItem" class="mt-2 flex items-center bg-white p-3 rounded-lg border border-gray-200">
            <el-image
                :src="selectedItem.icon"
                fit="cover"
                class="w-20 h-20 mr-4 rounded-md shadow-sm"
                :preview-src-list="[selectedItem.icon]"
                hide-on-click-modal
                preview-teleported
                :z-index="3000">
                <template #error>
                    <image-error-fallback text="图片损坏" />
                </template>
            </el-image>
            <div>
                <div class="text-base font-semibold text-gray-800">
                    {{ selectedItem.name }}
                </div>
                <div class="text-sm text-gray-500">已选择样式</div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref, watch } from 'vue';
    import { useComponentListStore } from '@smartdesk/design/stores';
    import { Component, ComponentStyle } from '@smartdesk/common/types';
    import { Box, Brush, Folder } from '@element-plus/icons-vue';
    import { useEnumStore } from '@chances/portal_common_core';

    defineOptions({
        name: 'ComponentStyleSelector',
    });

    // 定义组件 props，支持 v-model 和占位符
    const props = defineProps({
        modelValue: { type: String, default: '' },
        placeholder: { type: String, default: '请选择组件样式' },
        disabled: { type: Boolean, default: false },
    });

    // 定义 emits，支持 update:modelValue 和 select 事件
    const emit = defineEmits(['update:modelValue', 'select']);

    // Pinia stores
    const componentListStore = useComponentListStore();
    const enumStore = useEnumStore();

    // 响应式状态
    // 绑定的选中值 (style code)
    const selectedValue = ref(props.modelValue);
    // 选中的完整 style 对象
    const selectedItem = ref<ComponentStyle | null>(null);
    // 默认展开的节点键
    const expandedKeys = ref<string[]>([]);

    // 树配置：指定 label、value、children
    const treeProps = {
        label: 'label',
        value: 'value',
        children: 'children',
    };

    // 计算树数据：构建 catalog > component > style 结构
    const treeData = computed(() => {
        const groups: any[] = [];
        const catalogs = Object.entries(componentGroups.value);

        catalogs.forEach(([catalog, components]) => {
            // catalog、组件：非叶子 value 唯一但不用于选中
            // 组件样式：叶子 value: style.code (用于 modelValue)
            const catalogNode = {
                value: `catalog-${catalog}`,
                label: `${getCatalogDisplayName(catalog)} (${components.length})`,
                type: 'catalog',
                children: components.map((comp) => ({
                    value: `component-${comp.code}`,
                    label: comp.name,
                    icon: comp.icon,
                    type: 'component',
                    children: (componentStylesMap.value.get(comp.code) || []).map((style) => ({
                        value: style.code,
                        label: style.name,
                        icon: style.icon,
                        type: 'style',
                        data: style,
                    })),
                })),
            };
            groups.push(catalogNode);
        });
        return groups;
    });

    // 计算组件分组
    const componentGroups = computed<Record<string, Component[]>>(() => {
        const groups: Record<string, Component[]> = {};
        componentList.value.forEach((component) => {
            const catalog = component.catalog || '其他';
            if (!groups[catalog]) groups[catalog] = [];
            groups[catalog].push(component);
        });
        return groups;
    });

    // 树节点过滤函数
    const filterNode = (value: string, data: any) => {
        if (!value) return true;
        const query = value.toLowerCase();
        return data.label.toLowerCase().includes(query) || data.data?.code?.toLowerCase().includes(query);
    };

    // 获取 catalog 显示名称
    const getCatalogDisplayName = (catalog: string): string => {
        return enumStore.getLabelByKeyAndValue('componentCatalog', catalog) || catalog;
    };

    // 数据状态
    const componentList = ref<Component[]>([]);
    const componentStylesMap = ref<Map<string, ComponentStyle[]>>(new Map());

    // 加载所有组件和样式数据
    const loadData = async () => {
        const componentRes = await componentListStore.getComponentListByParams({}, { paged: false });
        componentList.value = componentRes.result;

        const res = await componentListStore.getComponentStyleListByParams(
            { componentCodes: componentList.value.map((c) => c.code) },
            { paged: false }
        );
        if (res.code === 200) {
            res.result.forEach((style) => {
                const arr = componentStylesMap.value.get(style.componentCode) ?? [];
                arr.push(style);
                componentStylesMap.value.set(style.componentCode, arr);
            });
        }
    };

    // 懒加载单个组件的样式
    const loadStylesForComponent = async (componentCode: string) => {
        if (!componentStylesMap.value.has(componentCode)) {
            const res = await componentListStore.getComponentStyleListByParams(
                { componentCodes: [componentCode] },
                { paged: false }
            );
            if (res.code === 200) {
                const styles = res.result.filter((style) => style.category === 'base');
                componentStylesMap.value.set(componentCode, styles);
            }
        }
    };

    // 处理节点点击：展开非叶子，选中叶子
    const handleNodeClick = async (data: any, node: any) => {
        if (data.children) {
            // 非叶子：展开并懒加载（如果是 component）
            if (node.level === 2) {
                const componentCode = data.value.split('-')[1];
                await loadStylesForComponent(componentCode);
            }
            if (!expandedKeys.value.includes(data.value)) {
                expandedKeys.value.push(data.value);
            }
        } else {
            // 叶子：选中 (value 已为 style.code)
            selectedValue.value = data.value;
            selectedItem.value = data.data;
            emit('update:modelValue', data.value);
            emit('select', data.data);
        }
    };

    // 监听 props.modelValue 更新内部状态
    watch(
        () => props.modelValue,
        (newVal) => {
            selectedValue.value = newVal;
            selectedItem.value = findStyleByCode(newVal);
        }
    );

    // 辅助函数：根据 code 查找 style 对象
    const findStyleByCode = (code: string): ComponentStyle | null => {
        const search = (nodes: any[]): ComponentStyle | null => {
            for (const node of nodes) {
                if (node.value === code) return node.data;
                if (node.children) {
                    const found = search(node.children);
                    if (found) return found;
                }
            }
            return null;
        };
        return search(treeData.value);
    };

    // 组件挂载时初始化数据
    onMounted(async () => {
        await loadData();
        if (treeData.value.length > 0) {
            expandedKeys.value = [treeData.value[0].value];
        }
        if (props.modelValue) {
            selectedItem.value = findStyleByCode(props.modelValue);
        }
    });
</script>

<style scoped>
    .tree-node {
        cursor: pointer;
    }

    .popover-preview {
        padding: 0;
        border: none;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
</style>
