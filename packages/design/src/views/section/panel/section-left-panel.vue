<template>
    <el-tabs v-model="sectionDesignerStore.leftPanelActiveTab" class="p-2">
        <el-tab-pane label="布局列表" name="layoutList">
            <layout-list-selector
                ref="layoutListSelectorRef"
                v-if="sectionDesignerStore.leftPanelActiveTab === 'layoutList'" />
        </el-tab-pane>

        <el-tab-pane label="组件列表" name="componentList">
            <component-list-selector
                :disabled="
                    !canEdit(sectionDesignerStore.section) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                        type: 'org',
                        value: sectionDesignerStore.section.orgId,
                    })
                " />
        </el-tab-pane>

        <el-tab-pane label="坑位列表" name="cellList">
            <cell-list-selector />
        </el-tab-pane>
    </el-tabs>
</template>

<script setup lang="ts">
    import ComponentListSelector from '../selector/component-list-selector.vue';
    import CellListSelector from '../selector/cell-list-selector.vue';
    import LayoutListSelector from '../selector/layout-list-selector.vue';
    import { useSectionDesignerStore } from '@smartdesk/design/stores';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { ref } from 'vue';

    // 楼层定义设计器左侧面板
    defineOptions({
        name: 'SectionLeftPanel',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const permissionStore = usePermissionStore();

    // ref
    const layoutListSelectorRef = ref<InstanceType<typeof LayoutListSelector> | null>();

    // 暴露方法给父组件
    defineExpose({
        layoutListSelectorRef,
    });
</script>
