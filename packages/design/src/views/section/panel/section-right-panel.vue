<template>
    <el-tabs v-model="sectionDesignerStore.rightPanelActiveTab" class="p-2">
        <el-tab-pane label="楼层布局" name="sectionLayout" v-if="sectionDesignerStore.currentCellIndex === -1">
            <section-layout-editor class="h-full overflow-auto" />
        </el-tab-pane>

        <el-tab-pane
            label="坑位布局"
            name="cellLayout"
            v-if="sectionDesignerStore.currentCellIndex !== -1 && !sectionDesignerStore.batchMode">
            <cell-layout-editor class="h-full overflow-auto" />
        </el-tab-pane>

        <el-tab-pane label="样式配置" name="styleProps">
            <section-designer-props-editor ref="sectionDesignerPropsEditorRef" class="h-full overflow-auto" />
        </el-tab-pane>
    </el-tabs>
</template>

<script setup lang="ts">
    import { onMounted, ref, watch } from 'vue';
    import { useSectionDesignerStore } from '@smartdesk/design/stores';
    import SectionLayoutEditor from '../editor/section-layout-editor.vue';
    import CellLayoutEditor from '../editor/cell-layout-editor.vue';
    import SectionDesignerPropsEditor from '../editor/section-designer-props-editor.vue';

    // 楼层定义设计器右侧面板
    defineOptions({
        name: 'SectionRightPanel',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();

    // ref
    const sectionDesignerPropsEditorRef = ref<any>();

    // 切换tab
    const switchTab = () => {
        if (sectionDesignerStore.currentCellIndex === -1) {
            // 切换到楼层，activeTab 为 sectionLayout
            sectionDesignerStore.rightPanelActiveTab = 'sectionLayout';
        } else {
            // 如果当前是 styleProps，activeTab 不变
            if (sectionDesignerStore.rightPanelActiveTab === 'styleProps') {
                return;
            }

            // 切换到坑位，activeTab 为 cellLayout
            sectionDesignerStore.rightPanelActiveTab = 'cellLayout';
        }
    };

    // 监听当前选中的坑位
    watch(
        () => sectionDesignerStore.currentCellIndex,
        () => {
            switchTab();
        }
    );

    // 组件挂载时初始化
    onMounted(() => {
        switchTab();
    });

    defineExpose({
        sectionDesignerPropsEditorRef,
    });
</script>
