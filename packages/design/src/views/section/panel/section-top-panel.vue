<template>
    <base-top-bar
        :title="title"
        :show-preview="false"
        :show-publish-all="false"
        :can-publish="hasPermission"
        :can-save="hasPermission"
        @save="emit('save')"
        @publish="emit('publish')">
        <template #center>
            <div>
                <el-button
                    :disabled="!sectionDesignerStore.canUndo"
                    type="default"
                    size="default"
                    icon="RefreshLeft"
                    @click="sectionDesignerStore.undo">
                    撤销
                </el-button>
                <el-button
                    :disabled="!sectionDesignerStore.canRedo"
                    type="default"
                    size="default"
                    icon="RefreshRight"
                    @click="sectionDesignerStore.redo">
                    重做
                </el-button>
            </div>
        </template>
    </base-top-bar>
</template>

<script setup lang="ts">
    import { computed, ref } from 'vue';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { useSectionDesignerStore } from '@smartdesk/design/stores';

    // 楼层顶部面板
    defineOptions({
        name: 'SectionTopPanel',
    });

    // 事件
    const emit = defineEmits<{
        (e: 'save'): void;
        (e: 'publish'): void;
    }>();

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const permissionStore = usePermissionStore();

    // 标题
    const title = ref<string>('楼层定义');

    // 是否有权限
    const hasPermission = computed<boolean>(() => {
        return (
            !sectionDesignerStore.batchMode &&
            canEdit(sectionDesignerStore.section) &&
            permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                type: 'org',
                value: sectionDesignerStore.section.orgId,
            })
        );
    });
</script>
