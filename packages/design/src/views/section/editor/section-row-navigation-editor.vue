<template>
    <div class="row-nav-panel" :class="{ collapsed: isCollapsed }">
        <div class="panel-header transform-all" @click.stop="toggleCollapse">
            <el-icon class="trigger-icon" :class="{ 'is-collapsed': isCollapsed }">
                <ArrowRight />
            </el-icon>
            <span class="title" v-show="!isCollapsed">坑位行</span>
        </div>

        <div class="panel-content" v-show="!isCollapsed">
            <vue-draggable
                v-model="cellRows"
                :disabled="
                    !canEdit(sectionDesignerStore.section) ||
                    !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                        type: 'org',
                        value: sectionDesignerStore.section.orgId,
                    })
                "
                ghost-class="ghost"
                :animation="200"
                handle=".handle"
                @end="updateRowOrder">
                <div
                    v-for="(row, index) in cellRows"
                    :key="row.id"
                    :title="row.name"
                    @click="handleRowClick(row)"
                    class="py-1">
                    <div
                        class="handle flex items-center gap-1 hover:bg-blue-100 text-gray-600 px-2 py-1 rounded cursor-pointer"
                        :class="{ 'bg-blue-200': isRowSelected(row) }">
                        <div class="text-xs">{{ row.orderNo }}.</div>
                        <div class="flex items-center flex-1 min-w-0">
                            <span class="truncate text-sm">{{ row.name }}</span>
                            <span class="text-xs text-gray-400 ml-1">({{ row.cells.length }})</span>
                        </div>
                    </div>
                </div>
            </vue-draggable>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { computed, ref } from 'vue';
    import { ArrowRight } from '@element-plus/icons-vue';
    import { useSectionDesignerStore } from '@smartdesk/design/stores';
    import { VueDraggable } from 'vue-draggable-plus';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';

    defineOptions({
        name: 'SectionRowNavigationEditor',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const permissionStore = usePermissionStore();

    const isCollapsed = ref<boolean>(true);

    // 计算坑位行
    const cellRows = computed({
        get: () => sectionDesignerStore.cellRows,
        set: (value) => {
            sectionDesignerStore.reorderRows(value);
        },
    });

    const updateRowOrder = () => {
        // 拖拽排序后重新计算位置
        sectionDesignerStore.recalculateRowPositions();
    };

    const handleRowClick = (row: any) => {
        // 选中该行的第一个坑位
        if (row.cells.length > 0) {
            const firstCellIndex = sectionDesignerStore.sectionLayout.cells.findIndex(
                (cell: any) => cell.code === row.cells[0].code
            );
            if (firstCellIndex !== -1) {
                sectionDesignerStore.selectCell(firstCellIndex);
            }
        }
    };

    const isRowSelected = (row: any) => {
        return (
            sectionDesignerStore.batchMode &&
            sectionDesignerStore.batchType === 'copy' &&
            row.cells.some((cell: any) =>
                sectionDesignerStore.batchList.some((batchCell: any) => batchCell.code === cell.code)
            )
        );
    };

    const toggleCollapse = () => {
        isCollapsed.value = !isCollapsed.value;
    };
</script>

<style scoped>
    .row-nav-panel {
        @apply bg-white border border-gray-200 rounded-lg shadow-md min-w-[200px] max-w-[300px];
        transition: all 0.3s ease;
    }

    .row-nav-panel.collapsed {
        @apply min-w-[40px] max-w-[40px];
    }

    .panel-header {
        @apply flex items-center justify-start gap-2 p-3 border-b border-gray-200 cursor-pointer hover:bg-gray-50;
    }

    .trigger-icon {
        @apply transition-transform duration-300;
    }

    .trigger-icon.is-collapsed {
        @apply rotate-180;
    }

    .panel-content {
        @apply p-2 max-h-[400px] overflow-y-auto;
    }

    .ghost {
        @apply opacity-50;
    }
</style>
