<template>
    <div class="flex items-center justify-between gap-4">
        <div class="flex items-center gap-4">
            <div v-if="!sectionDesignerStore.batchMode" class="flex items-center justify-center flex-1 min-w-0">
                <div>当前选中：{{ sectionDesignerStore.selectedElementTypeDesc }}</div>
            </div>

            <div v-if="sectionDesignerStore.batchMode">
                正在{{ getBatchTypeDesc() }}中， 已选择 {{ sectionDesignerStore.batchList.length }} 个坑位
            </div>
        </div>

        <div>
            <div v-if="sectionDesignerStore.currentCellIndex === -1" class="flex items-baseline gap-4">
                <el-input-number
                    :disabled="
                        !canEdit(sectionDesignerStore.section) ||
                        !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                            type: 'org',
                            value: sectionDesignerStore.section.orgId,
                        })
                    "
                    v-model="sectionDesignerStore.sectionDivideCount"
                    :min="1"
                    size="small"
                    class="w-20" />
                <div>
                    <el-button
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        type="primary"
                        size="small"
                        icon="Right"
                        @click="onClickSectionRowDivide">
                        拆分行
                    </el-button>
                    <el-button
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        type="primary"
                        size="small"
                        icon="Bottom"
                        @click="onClickSectionColumnDivide">
                        拆分列
                    </el-button>
                    <el-button
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        type="success"
                        size="small"
                        icon="Share"
                        @click="onClickSaveAsLayout">
                        另存为布局
                    </el-button>
                </div>
            </div>

            <div v-else>
                <div v-if="!sectionDesignerStore.batchMode" class="flex items-center gap-4">
                    <el-input-number
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="sectionDesignerStore.cellDivideCount"
                        :min="2"
                        size="small"
                        class="w-20" />
                    <div>
                        <el-button
                            :disabled="
                                !canEdit(sectionDesignerStore.section) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                    type: 'org',
                                    value: sectionDesignerStore.section.orgId,
                                })
                            "
                            type="primary"
                            size="small"
                            @click="onClickCellRowDivide">
                            <template #icon>
                                <i-mdi-land-rows-horizontal class="w-4 h-4" />
                            </template>
                            拆分行
                        </el-button>
                        <el-button
                            :disabled="
                                !canEdit(sectionDesignerStore.section) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                    type: 'org',
                                    value: sectionDesignerStore.section.orgId,
                                })
                            "
                            type="primary"
                            size="small"
                            @click="onClickCellColumnDivide">
                            <template #icon>
                                <i-mdi-land-rows-vertical class="w-4 h-4" />
                            </template>
                            拆分列
                        </el-button>
                        <el-button
                            :disabled="
                                !canEdit(sectionDesignerStore.section) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                    type: 'org',
                                    value: sectionDesignerStore.section.orgId,
                                })
                            "
                            type="warning"
                            size="small"
                            @click="onClickCellMerge">
                            <template #icon>
                                <i-mdi-call-merge class="w-4 h-4" />
                            </template>
                            合并坑位
                        </el-button>
                        <el-button
                            :disabled="
                                !canEdit(sectionDesignerStore.section) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                    type: 'org',
                                    value: sectionDesignerStore.section.orgId,
                                })
                            "
                            type="success"
                            size="small"
                            @click="onClickCellCopy">
                            <template #icon>
                                <i-mdi-content-copy class="w-4 h-4" />
                            </template>
                            复制坑位
                        </el-button>
                        <el-button
                            :disabled="
                                !canEdit(sectionDesignerStore.section) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                    type: 'org',
                                    value: sectionDesignerStore.section.orgId,
                                })
                            "
                            type="primary"
                            size="small"
                            @click="onClickBatchUpdateProps">
                            <template #icon>
                                <i-mdi-style class="w-4 h-4" />
                            </template>
                            设置样式
                        </el-button>
                        <el-button
                            :disabled="
                                !canEdit(sectionDesignerStore.section) ||
                                !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                    type: 'org',
                                    value: sectionDesignerStore.section.orgId,
                                })
                            "
                            type="danger"
                            size="small"
                            icon="Delete"
                            @click="onClickBatchDeleteCells">
                            删除坑位
                        </el-button>
                    </div>
                </div>

                <div v-if="sectionDesignerStore.batchMode" class="flex items-center gap-4">
                    <div v-if="sectionDesignerStore.batchType === 'copy'" class="flex items-center gap-4">
                        <div class="flex items-center gap-2">
                            <div>复制个数：</div>
                            <el-input-number
                                v-model="sectionDesignerStore.copyConfig.count"
                                :min="1"
                                :max="10"
                                size="small"
                                style="width: 120px" />
                        </div>
                        <div class="flex items-center gap-2">
                            <div>复制位置：</div>
                            <el-select
                                v-model="sectionDesignerStore.copyConfig.position"
                                size="small"
                                style="width: 120px">
                                <el-option label="当前下方" value="below" />
                                <el-option label="当前上方" value="above" />
                                <el-option label="顶部" value="top" />
                                <el-option label="底部" value="bottom" />
                            </el-select>
                        </div>
                    </div>

                    <el-button
                        :disabled="
                            isConfirmBtnDisabled() ||
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        type="primary"
                        size="small"
                        icon="Check"
                        @click="onClickConfirmBatch">
                        确认
                    </el-button>
                    <el-button
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        type="info"
                        size="small"
                        icon="Close"
                        @click="onClickCancelBatch">
                        取消
                    </el-button>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
    import { useSectionDesignerStore } from '@smartdesk/design/stores';
    import { useFeedback } from '@smartdesk/common/composables';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';

    // 楼层工具条编辑器
    defineOptions({
        name: 'SectionToolbarEditor',
    });

    // 事件
    const emit = defineEmits(['confirm-batch', 'cancel-batch']);

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const feedback = useFeedback();
    const permissionStore = usePermissionStore();

    // 点击楼层定义拆分行
    const onClickSectionRowDivide = async () => {
        if (await feedback.confirm('此操作会清空当前所有坑位，确定继续拆分行吗？')) {
            sectionDesignerStore.splitSectionRow();
        }
    };

    // 点击楼层定义拆分列
    const onClickSectionColumnDivide = async () => {
        if (await feedback.confirm('此操作会清空当前所有坑位，确定继续拆分列吗？')) {
            sectionDesignerStore.splitSectionColumn();
        }
    };

    // 点击楼层定义另存为布局
    const onClickSaveAsLayout = () => {
        sectionDesignerStore.saveSectionAsLayout();
    };

    // 点击坑位拆分行
    const onClickCellRowDivide = () => {
        sectionDesignerStore.splitCellRow();
    };

    // 点击坑位拆分列
    const onClickCellColumnDivide = () => {
        sectionDesignerStore.splitCellColumn();
    };

    // 点击坑位合并
    const onClickCellMerge = () => {
        sectionDesignerStore.handleStartBatch('merge');
    };

    // 点击坑位复制
    const onClickCellCopy = () => {
        sectionDesignerStore.handleStartBatch('copy');
    };

    // 点击坑位批量设置样式
    const onClickBatchUpdateProps = () => {
        sectionDesignerStore.handleStartBatch('props');
        sectionDesignerStore.switchTab('left', 'componentList');
        sectionDesignerStore.switchTab('right', 'styleProps');
    };

    // 点击坑位批量删除
    const onClickBatchDeleteCells = () => {
        sectionDesignerStore.handleStartBatch('delete');
    };

    // 点击确认批量
    const onClickConfirmBatch = () => {
        emit('confirm-batch');
    };

    // 点击取消批量
    const onClickCancelBatch = () => {
        emit('cancel-batch');
    };

    // 确认按钮是否被禁用
    const isConfirmBtnDisabled = () => {
        switch (sectionDesignerStore.batchType) {
            case 'merge':
                return sectionDesignerStore.batchList.length < 2;
            case 'props':
                return sectionDesignerStore.batchList.length < 1;
            case 'delete':
                return sectionDesignerStore.batchList.length < 1;
            case 'copy':
                return sectionDesignerStore.batchList.length < 1;
            default:
                return false;
        }
    };

    // 获取操作类型描述
    const getBatchTypeDesc = () => {
        switch (sectionDesignerStore.batchType) {
            case 'merge':
                return '合并';
            case 'props':
                return '设置样式';
            case 'delete':
                return '删除';
            case 'copy':
                return '复制';
            default:
                return '';
        }
    };
</script>
