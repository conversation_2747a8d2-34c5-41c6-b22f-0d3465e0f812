<template>
    <el-form v-if="sectionDesignerStore.currentCell" label-width="80px" label-suffix=":">
        <collapse-wrapper v-model="activeNames">
            <collapse-item-wrapper name="baseInfo" title="基本信息">
                <el-form-item label="坑位名称">
                    <el-input
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        v-model="sectionDesignerStore.currentCell.name"
                        @change="handleCellRectChange"
                        size="default" />
                </el-form-item>
            </collapse-item-wrapper>

            <collapse-item-wrapper name="cellLayout" title="坑位布局">
                <el-form-item label="坑位尺寸">
                    <rect-editor
                        v-model="sectionDesignerStore.currentCell.layout.rect"
                        :disabled="
                            !canEdit(sectionDesignerStore.section) ||
                            !permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                                type: 'org',
                                value: sectionDesignerStore.section.orgId,
                            })
                        "
                        @change="handleCellRectChange" />
                </el-form-item>
            </collapse-item-wrapper>
        </collapse-wrapper>
    </el-form>

    <div v-else class="text-center text-gray-400">
        <el-empty description="请选择任意坑位" :image-size="60" />
    </div>
</template>

<script setup lang="ts">
    import { ref } from 'vue';
    import { useSectionDesignerStore } from '@smartdesk/design/stores';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import { usePermissionStore } from '@chances/portal_common_core';
    import RectEditor from '@smartdesk/design/components/editor/rect-editor.vue';

    // 坑位布局编辑器
    defineOptions({
        name: 'CellLayoutEditor',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const permissionStore = usePermissionStore();

    // 展示
    const activeNames = ref<string[]>(['baseInfo', 'cellLayout']);

    // 处理坑位尺寸变化
    const handleCellRectChange = () => {
        sectionDesignerStore.saveHistory();
    };
</script>
