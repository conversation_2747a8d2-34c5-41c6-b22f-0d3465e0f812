<template>
    <div class="w-full mt-2 h-full">
        <el-scrollbar height="100%">
            <el-form-item v-if="sectionDesignerStore.currentCellIndex !== -1" label="组件样式">
                <component-style-selector
                    :disabled="!hasPermission"
                    v-model="selectedComponentStyleCode"
                    @select="handleComponentStyleSelect"
                    class="w-full" />
            </el-form-item>

            <collapse-wrapper v-model="activeNames" :accordion="true" ref="containerRef">
                <collapse-item-wrapper
                    id="attrs"
                    name="attrs"
                    title="属性"
                    v-if="metaInfo && metaInfo.attrs && metaInfo.attrs.length > 0">
                    <base-editor
                        :disabled="!hasPermission"
                        :metaInfos="metaInfo.attrs"
                        v-model:values="elementProps"
                        @change="handlePropsChange" />
                </collapse-item-wrapper>
                <collapse-item-wrapper
                    id="controls"
                    name="controls"
                    title="控制"
                    v-if="metaInfo && metaInfo.controls && metaInfo.controls.length > 0">
                    <base-editor
                        :disabled="!hasPermission"
                        :metaInfos="metaInfo.controls"
                        v-model:values="elementProps"
                        @change="handlePropsChange" />
                </collapse-item-wrapper>
                <collapse-item-wrapper
                    id="elements"
                    name="elements"
                    title="样式"
                    v-if="metaInfo && metaInfo.elements && metaInfo.elements.length > 0">
                    <base-editor
                        :disabled="!hasPermission"
                        :metaInfos="metaInfo.elements"
                        v-model:values="elementProps"
                        @change="handlePropsChange" />
                </collapse-item-wrapper>
            </collapse-wrapper>
        </el-scrollbar>
    </div>
</template>

<script setup lang="ts">
    import { computed, onMounted, ref, watch } from 'vue';
    import { useSectionDesignerStore } from '@smartdesk/design/stores';
    import { usePermissionStore } from '@chances/portal_common_core';
    import { canEdit, DESIGN_BIZ_PERMISSION } from '@smartdesk/common/permission';
    import BaseEditor from '@smartdesk/design/components/editor/base-editor.vue';
    import ComponentStyleSelector from '../selector/component-style-selector.vue';
    import { ComponentStyle } from '@smartdesk/common/types';
    import { useSiteStore } from '@smartdesk/common/stores';
    import { useComponentResolver } from '@smartdesk/design/composables';

    // 通用属性编辑器
    defineOptions({
        name: 'PropsEditor',
    });

    // pinia store
    const sectionDesignerStore = useSectionDesignerStore();
    const siteStore = useSiteStore();
    const permissionStore = usePermissionStore();

    // 展示项
    const activeNames = ref<string[]>(['attrs', 'controls', 'elements']);

    // 元素属性值
    const elementProps = ref<Record<string, any>>({});

    // 容器引用
    const containerRef = ref<HTMLElement | null>(null);

    // 选中的组件样式
    const selectedComponentStyleCode = ref<string | undefined>();

    // 元数据信息
    const metaInfo = computed(() => {
        return useComponentResolver().resolveComponentManifest(sectionDesignerStore.selectedElement?.component || '');
    });

    // 权限检查
    const hasPermission = computed(() => {
        // 有楼层定义编辑权限、canEdit、网站配置允许编辑组件样式
        return (
            canEdit(sectionDesignerStore.section) &&
            permissionStore.hasPermission(DESIGN_BIZ_PERMISSION.SECTION.EDIT, {
                type: 'org',
                value: sectionDesignerStore.section?.orgId,
            }) &&
            siteStore.currentSite?.config?.canEditComponentStyle
        );
    });

    // 处理组件样式选择
    const handleComponentStyleSelect = async (componentStyle: ComponentStyle) => {
        if (!sectionDesignerStore.selectedElement) {
            return;
        }

        // 组件样式切换，原样式信息也都被移除
        sectionDesignerStore.selectedElement.component = componentStyle.type;
        sectionDesignerStore.selectedElement.componentStyleCode = componentStyle.code;

        sectionDesignerStore.selectedElement.layout.props = {
            ...Object.values(componentStyle.layout).reduce((acc, obj) => ({ ...acc, ...obj }), {}),
        };

        sectionDesignerStore.saveHistory();
    };

    // 处理属性变化（微小改动：添加从本地 elementProps 到 store 的同步，实现实时双向绑定）
    const handlePropsChange = () => {
        if (sectionDesignerStore.selectedElement) {
            // 同步本地变化到 store，实现 v-model 的双向（从本地 → store）
            sectionDesignerStore.selectedElement.layout.props = { ...elementProps.value };
        }
        sectionDesignerStore.saveHistory();
    };

    // 初始化属性
    const init = () => {
        elementProps.value = sectionDesignerStore.selectedElement?.layout?.props || {};
        selectedComponentStyleCode.value = sectionDesignerStore.selectedElement?.componentStyleCode;
    };

    // 监听当前选择的元素（从 store → 本地，实现双向的另一半）
    watch(
        () => sectionDesignerStore.selectedElement,
        () => {
            init();
        },
        { deep: true }
    );

    // 组件挂载时初始化
    onMounted(() => {
        init();
    });

    defineExpose({
        elementProps,
    });
</script>
