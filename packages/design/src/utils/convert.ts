// 用于 Map<string, string[]>
export const convertStringArrayMapToObject = (map: Map<string, string[]>) => {
    const obj: Record<string, string[]> = {};
    map.forEach((value, key) => {
        obj[key] = value;
    });
    return obj;
};

// 用于 Map<string, Array<{ dsCode, size, pageSectionCode }>>
export const convertDsInfoMapToObject = (
    map: Map<string, Array<{ dsCode: string; size: number }>>
): Record<string, Array<{ dsCode: string; size: number }>> => {
    const obj: Record<string, Array<{ dsCode: string; size: number }>> = {};
    map.forEach((value, key) => {
        obj[key] = value;
    });
    return obj;
};
