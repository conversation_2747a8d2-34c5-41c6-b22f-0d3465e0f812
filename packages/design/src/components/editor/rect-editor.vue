<template>
    <el-config-provider :locale="zhCn">
        <div class="flex items-center flex-wrap gap-1 w-full">
            <div class="flex items-center gap-1 w-full">
                <el-input-number
                    v-model="localConfig.top"
                    :disabled="disabled"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    @change="handleChange">
                    <template #prefix>
                        <span>上</span>
                    </template>
                </el-input-number>
                <el-input-number
                    v-model="localConfig.left"
                    :disabled="disabled"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    @change="handleChange">
                    <template #prefix>
                        <span>左</span>
                    </template>
                </el-input-number>
            </div>

            <div class="flex items-center gap-1 w-full">
                <el-input-number
                    v-model="localConfig.width"
                    :disabled="disabled"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    @change="handleChange">
                    <template #prefix>
                        <span>宽</span>
                    </template>
                </el-input-number>
                <el-input-number
                    v-model="localConfig.height"
                    :disabled="disabled"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    @change="handleChange">
                    <template #prefix>
                        <span>高</span>
                    </template>
                </el-input-number>
            </div>
        </div>
    </el-config-provider>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';

    // 直角编辑器：top、left、width、height
    defineOptions({
        name: 'RectEditor',
    });

    interface RectConfig {
        top: number;
        left: number;
        width: number;
        height: number;
    }

    interface Props {
        modelValue?: Partial<RectConfig>;
        disabled?: Boolean;
    }

    interface Emits {
        (e: 'update:modelValue', value: RectConfig): void;

        (e: 'change', value: RectConfig): void;
    }

    const props = withDefaults(defineProps<Props>(), {
        modelValue: () => ({}),
        disabled: () => false,
    });

    const emit = defineEmits<Emits>();

    // 默认配置
    const defaultConfig: RectConfig = {
        top: 0,
        left: 0,
        width: 0,
        height: 0,
    };

    // 本地状态
    const localConfig = ref<RectConfig>({
        ...defaultConfig,
        ...props.modelValue,
    });

    // 处理变化
    const handleChange = () => {
        emit('update:modelValue', localConfig.value);
        emit('change', localConfig.value);
    };

    // 监听 props 变化，同步到本地状态
    watch(
        () => props.modelValue,
        (newValue) => {
            if (newValue) {
                localConfig.value = { ...defaultConfig, ...newValue };
            }
        },
        { deep: true, immediate: true }
    );
</script>
