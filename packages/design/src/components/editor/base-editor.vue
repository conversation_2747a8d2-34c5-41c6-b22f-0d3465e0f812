<template>
    <el-form :model="values" label-width="100" :label-position="labelPosition" :label-suffix="':'">
        <template v-for="editor in refEditor" :key="editor.name">
            <el-form-item :label="editor.title" v-if="editor.type === 'number'">
                <el-input-number
                    v-model="values[editor.name]"
                    :disabled="disabled"
                    :precision="0"
                    @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'radius'">
                <el-input-number
                    v-model="values[editor.name]"
                    :disabled="disabled"
                    :precision="0"
                    @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'boolean'">
                <el-switch v-model="values[editor.name]" :disabled="disabled" @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'string'">
                <el-input
                    v-model="values[editor.name]"
                    :disabled="disabled"
                    :placeholder="`请输入${editor.title}`"
                    @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'image'">
                <image-editor v-model="values[editor.name]" :disabled="disabled" @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'CardRect'">
                <card-rect-editor v-model="values[editor.name]" :disabled="disabled" @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'rect'">
                <card-rect-editor v-model="values[editor.name]" :disabled="disabled" @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'font'">
                <text-editor v-model="values[editor.name]" :disabled="disabled" @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'background'">
                <fill-editor v-model="values[editor.name]" :disabled="disabled" @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'location'">
                <location-editor v-model="values[editor.name]" :disabled="disabled" @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'padding'">
                <padding-editor v-model="values[editor.name]" :disabled="disabled" @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'size'">
                <size-editor v-model="values[editor.name]" :disabled="disabled" @change="handleChange" />
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'align'">
                <el-select
                    v-model="values[editor.name]"
                    :disabled="disabled"
                    :placeholder="`请选择${editor.title}`"
                    @change="handleChange">
                    <el-option v-for="item in alignOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
            </el-form-item>
            <el-form-item :label="editor.title" v-if="editor.type === 'enum'">
                <el-select
                    v-model="values[editor.name]"
                    :disabled="disabled"
                    :placeholder="`请选择${editor.title}`"
                    @change="handleChange"
                    clearable>
                    <el-option
                        v-for="item in editor.options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value" />
                </el-select>
            </el-form-item>
        </template>
    </el-form>
</template>

<script setup lang="ts">
    import { onBeforeMount, PropType, ref, watch } from 'vue';
    import type { FormProps } from 'element-plus';
    import CardRectEditor from '@smartdesk/common/components/card-rect-editor.vue';
    import TextEditor from './text-editor.vue';
    import FillEditor from './fill-editor.vue';
    import LocationEditor from './location-editor.vue';
    import PaddingEditor from './padding-editor.vue';
    import SizeEditor from './size-editor.vue';
    import { LabelValue } from '@chances/portal_common_core';

    // 基础编辑器
    defineOptions({
        name: 'BaseEditor',
    });

    type EditItem = {
        name: string;
        type: string;
        title: string;
        options: LabelValue[];
    };

    const alignOptions = [
        {
            label: '左对齐',
            value: 'left',
        },
        {
            label: '居中',
            value: 'center',
        },
        {
            label: '右对齐',
            value: 'right',
        },
    ];
    // 参数
    const props = defineProps({
        metaInfos: {
            type: Object as PropType<Record<string, any>>,
            required: true,
        },
        values: {
            type: Object as PropType<Record<string, any>>,
            required: true,
        },
        prefix: {
            type: Object as PropType<string>,
            required: false,
        },
        disabled: {
            type: Boolean,
            default: false,
        },
    });

    // 事件
    const emit = defineEmits<{
        (e: 'change'): void;
        (e: 'update:values', values: Record<string, any>): void;
    }>();

    // 编辑器引用
    const refEditor = ref<EditItem[]>([]);

    // 表单项的 label 位置：左边
    const labelPosition = ref<FormProps['labelPosition']>('left');

    // 是否是下拉框类型
    // const isOptions = (type: string) => {
    //     return Options.enums[type] != undefined;
    // };

    // 编辑器初始化
    const initEditor = () => {
        const editor: EditItem[] = [];
        for (let i = 0; i < props.metaInfos.length; i++) {
            const name = props.prefix ? `${props.prefix}.${props.metaInfos[i].name}` : props.metaInfos[i].name;
            editor.push({
                name: name,
                type: props.metaInfos[i].type,
                title: props.metaInfos[i].title,
                options: props.metaInfos[i].options,
            });
        }
        return editor;
    };

    const handleChange = () => {
        emit('change');
    };

    // 监听 props 变化
    watch(props, () => {
        refEditor.value = initEditor();
    });

    // 初始化
    onBeforeMount(() => {
        refEditor.value = initEditor();
    });
</script>
