<template>
    <el-config-provider :locale="zhCn">
        <div class="flex items-center flex-wrap gap-1 w-full">
            <div class="flex items-center gap-1 w-full">
                <el-input-number
                    v-model="localConfig.top"
                    :disabled="disabled"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    @change="handleChange">
                    <template #prefix>
                        <span>上</span>
                    </template>
                </el-input-number>
                <el-input-number
                    v-model="localConfig.bottom"
                    :disabled="disabled"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    @change="handleChange">
                    <template #prefix>
                        <span>下</span>
                    </template>
                </el-input-number>
            </div>

            <div class="flex items-center gap-1 w-full">
                <el-input-number
                    v-model="localConfig.left"
                    :disabled="disabled"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    @change="handleChange">
                    <template #prefix>
                        <span>左</span>
                    </template>
                </el-input-number>
                <el-input-number
                    v-model="localConfig.right"
                    :disabled="disabled"
                    :precision="0"
                    :min="0"
                    controls-position="right"
                    @change="handleChange">
                    <template #prefix>
                        <span>右</span>
                    </template>
                </el-input-number>
            </div>
        </div>
    </el-config-provider>
</template>

<script setup lang="ts">
    import { ref, watch } from 'vue';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';

    // 内边距编辑器：top、right、bottom、left
    defineOptions({
        name: 'PaddingEditor',
    });

    interface PaddingConfig {
        top: number;
        right: number;
        bottom: number;
        left: number;
    }

    interface Props {
        modelValue?: Partial<PaddingConfig>;
        disabled?: Boolean;
    }

    interface Emits {
        (e: 'update:modelValue', value: PaddingConfig): void;

        (e: 'change', value: PaddingConfig): void;
    }

    const props = withDefaults(defineProps<Props>(), {
        modelValue: () => ({}),
        disabled: () => false,
    });

    const emit = defineEmits<Emits>();

    // 默认配置
    const defaultConfig: PaddingConfig = {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
    };

    // 本地状态
    const localConfig = ref<PaddingConfig>({
        ...defaultConfig,
        ...props.modelValue,
    });

    // 处理变化
    const handleChange = () => {
        emit('update:modelValue', localConfig.value);
        emit('change', localConfig.value);
    };

    // 监听 props 变化，同步到本地状态
    watch(
        () => props.modelValue,
        (newValue) => {
            if (newValue) {
                localConfig.value = { ...defaultConfig, ...newValue };
            }
        },
        { deep: true, immediate: true }
    );
</script>
