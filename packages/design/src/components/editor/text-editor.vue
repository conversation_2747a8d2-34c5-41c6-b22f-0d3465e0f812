<template>
    <div class="relative w-full">
        <div
            class="flex items-center gap-2 p-2 bg-white border border-gray-200 rounded-lg hover:shadow-sm transition-all duration-200 w-full">
            <div class="flex items-center gap-1 w-full flex-1">
                <div class="flex items-center gap-1 shrink-0">
                    <el-button
                        :disabled="disabled"
                        :type="textStyle.fontWeight && textStyle.fontWeight > 400 ? 'primary' : ''"
                        @click="toggleBold"
                        title="粗体"
                        class="!w-8 !h-8 !p-0"
                        size="small">
                        <i-mdi-format-bold class="w-4 h-4" />
                    </el-button>

                    <el-button
                        :disabled="disabled"
                        :type="textStyle.fontStyle === 'italic' ? 'primary' : ''"
                        @click="toggleItalic"
                        title="斜体"
                        class="!w-8 !h-8 !p-0"
                        size="small">
                        <i-mdi-format-italic class="w-4 h-4" />
                    </el-button>

                    <el-button
                        :disabled="disabled"
                        :type="textStyle.textDecoration === 'underline' ? 'primary' : ''"
                        @click="toggleUnderline"
                        title="下划线"
                        class="!w-8 !h-8 !p-0"
                        size="small">
                        <i-mdi-format-underline class="w-4 h-4" />
                    </el-button>
                </div>

                <div class="w-px h-6 bg-gray-200 mx-1 shrink-0"></div>

                <div class="flex items-center justify-center gap-2 w-full flex-1">
                    <span class="text-xs text-center px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded shrink-0">
                        {{ textStyle.fontSize }}px
                    </span>
                </div>

                <div class="w-px h-6 bg-gray-200 mx-1 shrink-0"></div>

                <el-button
                    :disabled="disabled"
                    ref="detailButtonRef"
                    :type="showDetailPanel ? 'primary' : ''"
                    @click="toggleDetailPanel"
                    title="详细设置"
                    class="!w-8 !h-8 !p-0 shrink-0"
                    size="small">
                    <i-mdi-cog class="w-4 h-4" />
                </el-button>
            </div>
        </div>

        <floating-panel
            v-model:visible="showDetailPanel"
            :trigger-element="detailButtonRef?.$el"
            placement="left"
            :width="320"
            :max-height="400"
            :hide-delay="999999">
            <div class="flex items-center justify-between p-3 border-b border-gray-200">
                <h3 class="text-sm font-medium text-gray-900">文本样式设置</h3>
                <el-button text @click="hideDetailPanel" class="!p-1">
                    <i-mdi-close class="w-4 h-4" />
                </el-button>
            </div>

            <el-config-provider :locale="zhCn">
                <div class="p-3 space-y-3">
                    <div>
                        <div class="flex items-center gap-2 mb-2">
                            <i-mdi-eye class="w-4 h-4" />
                            <span class="text-xs font-medium text-gray-700">预览效果</span>
                        </div>
                        <div class="text-sm p-2 rounded border border-gray-200 bg-gray-50" :style="fullPreviewStyle">
                            这是预览文字效果
                        </div>
                    </div>

                    <div>
                        <div class="flex items-center gap-2 mb-2">
                            <i-mdi-format-font class="w-4 h-4" />
                            <span class="text-xs font-medium text-gray-700">字体设置</span>
                        </div>

                        <div class="space-y-2">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">字体</label>
                                <el-select v-model="localTextStyle.fontFamily" class="w-full" @change="updateStyle">
                                    <el-option
                                        v-for="font in fontFamilies"
                                        :key="font.value"
                                        :label="font.label"
                                        :value="font.value"
                                        :style="{ fontFamily: font.value }" />
                                </el-select>
                            </div>

                            <div class="grid grid-cols-2 gap-2">
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">字号</label>
                                    <el-input-number
                                        v-model="localTextStyle.fontSize"
                                        :min="8"
                                        :max="72"
                                        controls-position="right"
                                        class="w-full"
                                        @change="updateStyle" />
                                </div>

                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">字重</label>
                                    <el-select v-model="localTextStyle.fontWeight" class="w-full" @change="updateStyle">
                                        <el-option
                                            v-for="weight in fontWeights"
                                            :key="weight.value"
                                            :label="weight.label"
                                            :value="weight.value" />
                                    </el-select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <div class="flex items-center gap-2 mb-2">
                            <i-mdi-palette class="w-4 h-4" />
                            <span class="text-xs font-medium text-gray-700">颜色设置</span>
                        </div>

                        <div class="space-y-2">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">文字颜色</label>
                                <div class="flex gap-2">
                                    <el-color-picker
                                        v-model="localTextStyle.color"
                                        :predefine="predefineColors"
                                        @change="updateStyleWithoutClose" />
                                    <el-input
                                        v-model="localTextStyle.color"
                                        placeholder="#000000"
                                        class="flex-1"
                                        @change="updateStyleWithoutClose" />
                                </div>
                            </div>

                            <div>
                                <label class="block text-xs text-gray-600 mb-1">背景颜色</label>
                                <div class="flex gap-2">
                                    <el-color-picker
                                        v-model="localTextStyle.backgroundColor"
                                        show-alpha
                                        :predefine="predefineColors"
                                        @change="updateStyleWithoutClose" />
                                    <el-input
                                        v-model="localTextStyle.backgroundColor"
                                        placeholder="transparent"
                                        class="flex-1"
                                        @change="updateStyleWithoutClose" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <div class="flex items-center gap-2 mb-2">
                            <i-mdi-format-align-left class="w-4 h-4" />
                            <span class="text-xs font-medium text-gray-700">排版设置</span>
                        </div>

                        <div class="space-y-2">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">水平对齐</label>
                                <el-radio-group v-model="localTextStyle.textAlign" @change="updateStyle">
                                    <el-radio-button value="left">
                                        <i-mdi-format-align-left class="w-4 h-4" />
                                    </el-radio-button>
                                    <el-radio-button value="center">
                                        <i-mdi-format-align-center class="w-4 h-4" />
                                    </el-radio-button>
                                    <el-radio-button value="right">
                                        <i-mdi-format-align-right class="w-4 h-4" />
                                    </el-radio-button>
                                    <el-radio-button value="justify">
                                        <i-mdi-format-align-justify class="w-4 h-4" />
                                    </el-radio-button>
                                </el-radio-group>
                            </div>

                            <div>
                                <label class="block text-xs text-gray-600 mb-1">垂直对齐</label>
                                <el-radio-group
                                    v-model="localTextStyle.verticalAlign"
                                    size="small"
                                    @change="updateStyle">
                                    <el-radio-button value="top">
                                        <i-mdi-align-vertical-top class="w-4 h-4" />
                                    </el-radio-button>
                                    <el-radio-button value="middle">
                                        <i-mdi-align-vertical-center class="w-4 h-4" />
                                    </el-radio-button>
                                    <el-radio-button value="bottom">
                                        <i-mdi-align-vertical-bottom class="w-4 h-4" />
                                    </el-radio-button>
                                    <el-radio-button value="baseline">
                                        <i-mdi-format-text-variant class="w-4 h-4" />
                                    </el-radio-button>
                                </el-radio-group>
                            </div>

                            <div class="grid grid-cols-2 gap-2">
                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">行高</label>
                                    <el-input-number
                                        v-model="localTextStyle.lineHeight"
                                        :min="0.5"
                                        :max="3"
                                        :step="0.1"
                                        :precision="1"
                                        controls-position="right"
                                        class="w-full"
                                        @change="updateStyle" />
                                </div>

                                <div>
                                    <label class="block text-xs text-gray-600 mb-1">字间距</label>
                                    <el-input-number
                                        v-model="localTextStyle.letterSpacing"
                                        :min="-5"
                                        :max="20"
                                        :step="0.5"
                                        :precision="1"
                                        controls-position="right"
                                        class="w-full"
                                        @change="updateStyle" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-config-provider>
        </floating-panel>
    </div>
</template>

<script setup lang="ts">
    import { computed, type CSSProperties, ref, watch } from 'vue';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';

    interface TextStyleConfig {
        fontFamily: string;
        fontSize: number;
        fontWeight: number;
        fontStyle: 'normal' | 'italic';
        textDecoration: 'none' | 'underline';
        color: string;
        backgroundColor: string;
        textAlign: 'left' | 'center' | 'right' | 'justify';
        verticalAlign: 'top' | 'middle' | 'bottom' | 'baseline';
        lineHeight: number;
        letterSpacing: number;
    }

    interface FontOption {
        label: string;
        value: string;
    }

    interface WeightOption {
        label: string;
        value: number;
    }

    interface Props {
        modelValue?: Partial<TextStyleConfig>;
        disabled?: Boolean;
    }

    interface Emits {
        (e: 'update:modelValue', value: Partial<TextStyleConfig>): void;

        (e: 'change', value: Partial<TextStyleConfig>): void;
    }

    const props = withDefaults(defineProps<Props>(), {
        modelValue: () => ({}),
        disabled: () => false,
    });

    const emit = defineEmits<Emits>();

    const defaultStyle: TextStyleConfig = {
        fontFamily: 'Microsoft YaHei, sans-serif',
        fontSize: 14,
        fontWeight: 400,
        fontStyle: 'normal',
        textDecoration: 'none',
        color: '#000000',
        backgroundColor: 'transparent',
        textAlign: 'left',
        verticalAlign: 'baseline',
        lineHeight: 1.4,
        letterSpacing: 0,
    };

    // 本地状态，避免直接修改 props
    const localTextStyle = ref<Partial<TextStyleConfig>>({});

    // 控制面板显示状态
    const showDetailPanel = ref(false);

    // 组件引用
    const detailButtonRef = ref();

    // 计算属性用于显示
    const textStyle = computed<Partial<TextStyleConfig>>(() => localTextStyle.value);

    // 更新样式的统一方法
    const updateStyle = () => {
        emit('update:modelValue', localTextStyle.value);
        emit('change', localTextStyle.value);
    };

    // 更新样式但不关闭面板（用于颜色选择器）
    const updateStyleWithoutClose = () => {
        emit('update:modelValue', localTextStyle.value);
        emit('change', localTextStyle.value);
        // 阻止面板关闭
    };

    const fontFamilies = ref<FontOption[]>([
        { label: '微软雅黑', value: 'Microsoft YaHei, sans-serif' },
        { label: '苹方', value: 'PingFang SC, sans-serif' },
        { label: '宋体', value: 'SimSun, serif' },
        { label: '黑体', value: 'SimHei, sans-serif' },
        { label: 'Arial', value: 'Arial, sans-serif' },
        { label: 'Times New Roman', value: 'Times New Roman, serif' },
        { label: 'Helvetica', value: 'Helvetica, sans-serif' },
        { label: 'Georgia', value: 'Georgia, serif' },
    ]);

    const fontWeights = ref<WeightOption[]>([
        { label: '超细', value: 100 },
        { label: '细体', value: 200 },
        { label: '轻体', value: 300 },
        { label: '正常', value: 400 },
        { label: '中等', value: 500 },
        { label: '半粗', value: 600 },
        { label: '粗体', value: 700 },
        { label: '超粗', value: 800 },
        { label: '最粗', value: 900 },
    ]);

    const predefineColors = ref<string[]>([
        '#000000',
        '#333333',
        '#666666',
        '#999999',
        '#cccccc',
        '#ffffff',
        '#f56565',
        '#ed8936',
        '#ecc94b',
        '#48bb78',
        '#38b2ac',
        '#4299e1',
        '#0bc5ea',
        '#9f7aea',
        '#ed64a6',
    ]);

    // 预览样式
    const fullPreviewStyle = computed<CSSProperties>(() => ({
        fontFamily: textStyle.value.fontFamily,
        fontSize: `${textStyle.value.fontSize}px`,
        fontWeight: textStyle.value.fontWeight ? textStyle.value.fontWeight.toString() : '',
        fontStyle: textStyle.value.fontStyle,
        textDecoration: textStyle.value.textDecoration,
        color: textStyle.value.color,
        backgroundColor: textStyle.value.backgroundColor,
        textAlign: textStyle.value.textAlign,
        verticalAlign: textStyle.value.verticalAlign,
        lineHeight: textStyle.value.lineHeight ? textStyle.value.lineHeight.toString() : '',
        letterSpacing: `${textStyle.value.letterSpacing}px`,
    }));

    // 面板控制方法
    const toggleDetailPanel = () => {
        showDetailPanel.value = !showDetailPanel.value;
    };

    const hideDetailPanel = () => {
        showDetailPanel.value = false;
    };

    // 样式切换方法
    const toggleItalic = (): void => {
        localTextStyle.value.fontStyle = localTextStyle.value.fontStyle === 'normal' ? 'italic' : 'normal';
        updateStyle();
    };

    const toggleUnderline = (): void => {
        localTextStyle.value.textDecoration = localTextStyle.value.textDecoration === 'none' ? 'underline' : 'none';
        updateStyle();
    };

    const toggleBold = (): void => {
        localTextStyle.value.fontWeight = localTextStyle.value.fontWeight === 400 ? 700 : 400;
        updateStyle();
    };

    // 监听 props 变化，同步到本地状态
    watch(
        () => props.modelValue,
        (newValue) => {
            if (!newValue || Object.keys(newValue).length === 0) {
                localTextStyle.value = { ...defaultStyle };
            } else {
                localTextStyle.value = { ...newValue };
            }
        },
        { deep: true, immediate: true }
    );
</script>
