<template>
    <div class="relative w-full">
        <div
            class="flex items-center gap-2 p-2 bg-white border border-gray-200 rounded-lg hover:shadow-sm transition-all duration-200 w-full">
            <div class="flex items-center gap-1 w-full flex-1">
                <div class="flex items-center gap-1 shrink-0">
                    <el-button
                        :disabled="disabled"
                        :type="fillStyle.type === 'color' ? 'primary' : ''"
                        @click="setFillType('color')"
                        title="纯色"
                        class="!w-8 !h-8 !p-0"
                        size="small">
                        <i-mdi-palette class="w-4 h-4" />
                    </el-button>

                    <el-button
                        :disabled="disabled"
                        :type="fillStyle.type === 'gradient' ? 'primary' : ''"
                        @click="setFillType('gradient')"
                        title="渐变"
                        class="!w-8 !h-8 !p-0"
                        size="small">
                        <i-mdi-gradient-horizontal class="w-4 h-4" />
                    </el-button>

                    <el-button
                        :disabled="disabled"
                        :type="fillStyle.type === 'image' ? 'primary' : ''"
                        @click="setFillType('image')"
                        title="图片"
                        class="!w-8 !h-8 !p-0"
                        size="small">
                        <i-mdi-image class="w-4 h-4" />
                    </el-button>
                </div>

                <div class="w-px h-6 bg-gray-200 mx-1 shrink-0"></div>

                <div class="flex items-center justify-center gap-2 w-full flex-1">
                    <span class="text-xs text-center px-1.5 py-0.5 bg-gray-100 text-gray-600 rounded shrink-0">
                        {{ fillTypeLabel }}
                    </span>
                </div>

                <div class="w-px h-6 bg-gray-200 mx-1 shrink-0"></div>

                <el-button
                    :disabled="disabled"
                    ref="detailButtonRef"
                    :type="showDetailPanel ? 'primary' : ''"
                    @click="toggleDetailPanel"
                    title="详细设置"
                    class="!w-8 !h-8 !p-0 shrink-0"
                    size="small">
                    <i-mdi-cog class="w-4 h-4" />
                </el-button>
            </div>
        </div>

        <floating-panel
            v-model:visible="showDetailPanel"
            :trigger-element="detailButtonRef?.$el"
            placement="left"
            :width="360"
            :max-height="500"
            :hide-delay="999999">
            <div class="flex items-center justify-between p-3 border-b border-gray-200">
                <h3 class="text-sm font-medium text-gray-900">背景样式设置</h3>
                <el-button text @click="hideDetailPanel" class="!p-1">
                    <i-mdi-close class="w-4 h-4" />
                </el-button>
            </div>

            <el-config-provider :locale="zhCn">
                <div class="p-3 space-y-3">
                    <div>
                        <div class="flex items-center gap-2 mb-2">
                            <i-mdi-format-color-fill class="w-4 h-4" />
                            <span class="text-xs font-medium text-gray-700">填充类型</span>
                        </div>

                        <el-radio-group :model-value="fillStyle.type" @change="updateFillType" class="w-full">
                            <el-radio-button value="none" @click="setFillType('none')">
                                <div class="flex gap-1">
                                    <i-mdi-close class="w-4 h-4" />
                                    <div>无填充</div>
                                </div>
                            </el-radio-button>
                            <el-radio-button value="color" @click="setFillType('color')">
                                <div class="flex gap-1">
                                    <i-mdi-palette class="w-4 h-4" />
                                    <div>纯色</div>
                                </div>
                            </el-radio-button>
                            <el-radio-button value="gradient" @click="setFillType('gradient')">
                                <div class="flex gap-1">
                                    <i-mdi-gradient-horizontal class="w-4 h-4" />
                                    <div>渐变</div>
                                </div>
                            </el-radio-button>
                            <el-radio-button value="image" @click="setFillType('image')">
                                <div class="flex gap-1">
                                    <i-mdi-image class="w-4 h-4" />
                                    <div>图片</div>
                                </div>
                            </el-radio-button>
                        </el-radio-group>
                    </div>

                    <div v-if="fillStyle.type !== 'none'">
                        <div class="flex items-center gap-2 mb-2">
                            <i-mdi-opacity class="w-4 h-4" />
                            <span class="text-xs font-medium text-gray-700">透明度设置</span>
                        </div>

                        <el-input-number
                            :model-value="fillStyle.opacity"
                            :min="0"
                            :max="1"
                            :step="0.1"
                            @change="updateOpacity" />
                    </div>

                    <div v-if="fillStyle.type === 'color'">
                        <div class="flex items-center gap-2 mb-2">
                            <i-mdi-palette class="w-4 h-4" />
                            <span class="text-xs font-medium text-gray-700">颜色设置</span>
                        </div>

                        <div>
                            <label class="block text-xs text-gray-600 mb-1">背景颜色</label>
                            <div class="flex gap-2">
                                <el-color-picker
                                    :model-value="fillStyle.color"
                                    show-alpha
                                    :predefine="predefineColors"
                                    @change="updateColor" />
                                <el-input
                                    :model-value="fillStyle.color"
                                    placeholder="#000000"
                                    class="flex-1"
                                    @change="updateColor" />
                            </div>
                        </div>
                    </div>

                    <div v-if="fillStyle.type === 'gradient'">
                        <div class="flex items-center gap-2 mb-2">
                            <i-mdi-gradient-horizontal class="w-4 h-4" />
                            <span class="text-xs font-medium text-gray-700">渐变设置</span>
                        </div>

                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">渐变方向</label>
                                <el-select
                                    :model-value="fillStyle.linearGradient.direction"
                                    @change="updateGradientDirection">
                                    <el-option label="从上到下" value="to bottom" />
                                    <el-option label="从右到左" value="to left" />
                                    <el-option label="从左到右" value="to right" />
                                    <el-option label="从下到上" value="to top" />
                                </el-select>
                            </div>
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">起始颜色</label>
                                <div class="flex gap-2">
                                    <el-color-picker
                                        :model-value="fillStyle.gradientStart"
                                        show-alpha
                                        :predefine="predefineColors"
                                        @change="updateGradientStart" />
                                    <el-input
                                        :model-value="fillStyle.gradientStart"
                                        placeholder="#000000"
                                        class="flex-1"
                                        @change="updateGradientStart" />
                                </div>
                            </div>

                            <div>
                                <label class="block text-xs text-gray-600 mb-1">结束颜色</label>
                                <div class="flex gap-2">
                                    <el-color-picker
                                        :model-value="fillStyle.gradientEnd"
                                        show-alpha
                                        :predefine="predefineColors"
                                        @change="updateGradientEnd" />
                                    <el-input
                                        :model-value="fillStyle.gradientEnd"
                                        placeholder="#ffffff"
                                        class="flex-1"
                                        @change="updateGradientEnd" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div v-if="fillStyle.type === 'image'">
                        <div class="flex items-center gap-2 mb-2">
                            <i-mdi-image class="w-4 h-4" />
                            <span class="text-xs font-medium text-gray-700">图片设置</span>
                        </div>

                        <div class="space-y-3">
                            <div>
                                <label class="block text-xs text-gray-600 mb-1">图片上传</label>
                                <div class="flex flex-col gap-2">
                                    <el-upload
                                        v-if="!fillStyle.image"
                                        list-type="picture-card"
                                        :show-file-list="false"
                                        :http-request="handleImageUpload"
                                        :on-success="handleImageUploadSuccess"
                                        accept="image/*"
                                        class="w-full">
                                        <div
                                            class="flex flex-col items-center justify-center w-full h-20 text-gray-400">
                                            <i-mdi-plus class="w-4 h-4" />
                                            <span class="text-xs">上传图片</span>
                                        </div>
                                    </el-upload>

                                    <div v-if="fillStyle.image" class="relative group">
                                        <el-image
                                            class="w-full h-20 object-cover rounded border border-gray-200"
                                            :src="fillStyle.image"
                                            fit="cover">
                                            <template #error>
                                                <div
                                                    class="flex items-center justify-center w-full h-full bg-gray-100 text-gray-400">
                                                    <i-mdi-image-broken class="w-4 h-4" />
                                                </div>
                                            </template>
                                        </el-image>

                                        <div
                                            class="absolute inset-0 bg-blue-300 bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2 rounded">
                                            <el-button
                                                @click="handleImagePreview"
                                                type="primary"
                                                size="small"
                                                circle
                                                icon="View">
                                            </el-button>
                                            <el-button
                                                @click="handleImageRemove"
                                                type="danger"
                                                size="small"
                                                circle
                                                icon="Delete">
                                            </el-button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </el-config-provider>
        </floating-panel>

        <el-dialog v-model="imagePreviewVisible" width="50%" title="图片预览">
            <el-image class="w-full" :src="previewImageUrl">
                <template #error>
                    <div class="flex items-center justify-center w-full h-64 bg-gray-100 text-gray-400">
                        <i-mdi-image-broken class="w-4 h-4" />
                    </div>
                </template>
            </el-image>
        </el-dialog>
    </div>
</template>

<script setup lang="ts">
    import { computed, ref } from 'vue';
    import type { UploadRequestOptions } from 'element-plus';
    import { RestResultResponse } from '@chances/portal_common_core';
    import { storageApi } from '@smartdesk/common/api';
    import zhCn from 'element-plus/es/locale/lang/zh-cn';

    interface FillStyleConfig {
        type: 'none' | 'color' | 'gradient' | 'image';
        color: string;
        linearGradient: {
            direction: 'to bottom' | 'to left' | 'to right' | 'to top';
            colors: string[];
        };
        gradientStart: string;
        gradientEnd: string;
        image: string;
        opacity: number;
    }

    interface Props {
        modelValue?: Partial<FillStyleConfig>;
        disabled?: Boolean;
    }

    interface Emits {
        (e: 'update:modelValue', value: FillStyleConfig): void;

        (e: 'change', value: FillStyleConfig): void;
    }

    const props = withDefaults(defineProps<Props>(), {
        modelValue: () => ({}),
        disabled: () => false,
    });

    const emit = defineEmits<Emits>();

    const defaultStyle: FillStyleConfig = {
        type: 'none',
        color: '',
        linearGradient: {
            direction: 'to bottom',
            colors: [],
        },
        gradientStart: '',
        gradientEnd: '',
        image: '',
        opacity: 1,
    };

    const showDetailPanel = ref(false);
    const detailButtonRef = ref();

    // 图片预览相关
    const imagePreviewVisible = ref(false);
    const previewImageUrl = ref('');

    // 使用计算属性处理 modelValue，确保响应性
    const fillStyle = computed<FillStyleConfig>(() => ({
        ...defaultStyle,
        ...props.modelValue,
        linearGradient: {
            ...defaultStyle.linearGradient,
            ...(props.modelValue?.linearGradient || {}),
            colors: [
                props.modelValue?.gradientStart || defaultStyle.gradientStart,
                props.modelValue?.gradientEnd || defaultStyle.gradientEnd,
            ].filter(Boolean),
        },
    }));

    // 预定义颜色
    const predefineColors = ref<string[]>([
        '#000000',
        '#333333',
        '#666666',
        '#999999',
        '#cccccc',
        '#ffffff',
        '#f56565',
        '#ed8936',
        '#ecc94b',
        '#48bb78',
        '#38b2ac',
        '#4299e1',
        '#0bc5ea',
        '#9f7aea',
        '#ed64a6',
    ]);

    // 填充类型标签
    const fillTypeLabel = computed(() => {
        const labels = {
            none: '无填充',
            color: '纯色',
            gradient: '渐变',
            image: '图片',
        };
        return labels[fillStyle.value.type];
    });

    // 通用的更新方法
    const updateValue = (updates: Partial<FillStyleConfig>) => {
        const newValue: FillStyleConfig = {
            ...fillStyle.value,
            ...updates,
        };

        // 更新 linearGradient.colors
        if (updates.gradientStart !== undefined || updates.gradientEnd !== undefined) {
            newValue.linearGradient.colors = [
                updates.gradientStart ?? newValue.gradientStart,
                updates.gradientEnd ?? newValue.gradientEnd,
            ].filter(Boolean);
        }

        emit('update:modelValue', newValue);
        emit('change', newValue);
    };

    // 面板控制方法
    const toggleDetailPanel = () => {
        showDetailPanel.value = !showDetailPanel.value;
    };

    const hideDetailPanel = () => {
        showDetailPanel.value = false;
    };

    // 设置填充类型
    const setFillType = (type: FillStyleConfig['type']) => {
        updateValue({
            ...defaultStyle,
            type,
        });
    };

    // 具体的更新方法
    const updateFillType = (type: FillStyleConfig['type']) => {
        updateValue({ type });
    };

    const updateOpacity = (opacity: number) => {
        updateValue({ opacity });
    };

    const updateColor = (color: string) => {
        updateValue({ color });
    };

    const updateGradientDirection = (direction: FillStyleConfig['linearGradient']['direction']) => {
        updateValue({
            linearGradient: {
                ...fillStyle.value.linearGradient,
                direction,
            },
        });
    };

    const updateGradientStart = (gradientStart: string) => {
        updateValue({ gradientStart });
    };

    const updateGradientEnd = (gradientEnd: string) => {
        updateValue({ gradientEnd });
    };

    const updateImage = (image: string) => {
        updateValue({ image });
    };

    // 图片上传处理
    const handleImageUpload = (options: UploadRequestOptions) => {
        return storageApi.uploadFile(options.file);
    };

    const handleImageUploadSuccess = (response: RestResultResponse<string>) => {
        updateImage(response.result);
    };

    const handleImagePreview = () => {
        previewImageUrl.value = fillStyle.value.image;
        imagePreviewVisible.value = true;
    };

    const handleImageRemove = () => {
        updateImage('');
    };
</script>
