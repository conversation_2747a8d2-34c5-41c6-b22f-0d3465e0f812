import { useComponentListStore } from '@smartdesk/design/stores';
import { LOCAL_MANIFEST_MAP } from '@smartdesk/design/manifest';

// 组件 manifest 解析器
export const useComponentResolver = () => {
    // 组合函数
    const componentListStore = useComponentListStore();

    // 根据组件类型获取组件的 manifest
    const resolveComponentManifest = (componentType: string) => {
        // 先从本地的 manifest 文件中找，找不到就从组件 layout 中找
        return LOCAL_MANIFEST_MAP[componentType]
            ? LOCAL_MANIFEST_MAP[componentType]
            : componentListStore.state.componentMap.get(componentType)?.layout;
    };

    return {
        resolveComponentManifest,
    };
};
