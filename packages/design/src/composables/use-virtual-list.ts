import { nextTick, ref } from 'vue';

// 虚拟列表
export const useVirtualList = (dataCode: string, executor: (params: string[]) => void) => {
    // 可视区域追踪
    const visibleSectionCodes = ref<Set<string>>(new Set());
    const processedSections = ref<Set<string>>(new Set());

    // 在组件挂载时启动监听
    let sectionObserver: (() => void) | null = null;

    // 添加 Intersection Observer 来监听楼层可见性
    const observeSectionVisibility = () => {
        // 创建 Intersection Observer
        const observer = new IntersectionObserver(
            (entries) => {
                const visibleCodes: string[] = [];

                entries.forEach((entry) => {
                    const element = entry.target;
                    const targetDataCode = element.getAttribute(dataCode);

                    if (entry.isIntersecting && targetDataCode) {
                        visibleCodes.push(targetDataCode);
                    }
                });

                if (visibleCodes.length > 0) {
                    handleVisibleSectionsChange(visibleCodes);
                }
            },
            {
                // 提前 100px 开始加载
                rootMargin: '100px',
                threshold: 0.01,
            }
        );

        // 监听所有楼层元素
        nextTick(() => {
            const sectionElements = document.querySelectorAll('[data-section-code]');
            sectionElements.forEach((element) => {
                observer.observe(element);
            });
        });

        // 返回清理函数
        return () => {
            observer.disconnect();
        };
    };

    // 开始观察
    const startSectionObserver = () => {
        // 延迟启动，确保 DOM 已渲染
        setTimeout(() => {
            sectionObserver = observeSectionVisibility();
        }, 100);
    };

    // 停止观察
    const stopSectionObserver = () => {
        if (sectionObserver) {
            sectionObserver();
            sectionObserver = null;
        }
    };

    // 处理可视区域变化的方法
    const handleVisibleSectionsChange = (visibleCodes: string[]) => {
        // 找出新进入视口的楼层
        const newVisibleSections = visibleCodes.filter((code) => !processedSections.value.has(code));

        if (newVisibleSections.length > 0) {
            // 执行器
            executor(newVisibleSections);
        }

        // 更新可视区域
        visibleSectionCodes.value = new Set(visibleCodes);
    };

    return {
        visibleSectionCodes,
        processedSections,
        startSectionObserver,
        stopSectionObserver,
    };
};
