import type { RouteRecordRaw } from 'vue-router';
import { createRouter, createWebHistory } from 'vue-router';

const routes: Array<RouteRecordRaw> = [
    {
        path: '/page_designer',
        name: '页面设计器',
        meta: {
            componentName: 'PageDesigner',
            layout: 'designer',
        },
        component: () => import('@smartdesk/design/views/page/page-designer.vue'),
    },
    {
        path: '/section_designer',
        name: '楼层定义设计器',
        meta: {
            componentName: 'SectionDesigner',
            layout: 'designer',
        },
        component: () => import('@smartdesk/design/views/section/section-designer.vue'),
    },
];

const router = createRouter({
    history: createWebHistory(),
    routes,
});

export { routes };
export default router;
