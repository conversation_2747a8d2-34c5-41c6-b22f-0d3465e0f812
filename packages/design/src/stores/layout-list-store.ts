import { defineStore } from 'pinia';
import { reactive } from 'vue';
import { Layout, LayoutSearchForm, PaginationParams } from '@smartdesk/common/types';
import { layoutApi } from '@smartdesk/common/api';
import { STORAGE_DRIVER } from '@chances/portal_common_core';
import { useSiteStore } from '@smartdesk/common/stores';

// 布局列表存储
export const useLayoutListStore = defineStore(
    'layout-list-store',
    () => {
        // 状态
        const state = reactive<{
            layoutMap: Map<string, Layout>;
        }>({
            layoutMap: new Map(),
        });

        // pinia store
        const currentSiteStore = useSiteStore();

        // 根据查询条件获取组件列表
        const getLayoutListByParams = async (params: Partial<LayoutSearchForm>, pageInfo: PaginationParams) => {
            params.siteCode = currentSiteStore.currentSiteCode;
            params.delFlag = 0;
            params.status = 1;
            const layoutListRes = await layoutApi.getLayouts(params, pageInfo);
            if (layoutListRes.code === 200) {
                // 合并已有的
                layoutListRes.result.forEach((layout: Layout) => {
                    state.layoutMap.set(layout.code, layout);
                });
            }
            return layoutListRes;
        };

        return {
            state,
            getLayoutListByParams,
        };
    },
    {
        persist: {
            enabled: false,
            storeName: 'design-portal-store',
            driver: STORAGE_DRIVER.INDEXED_DB,
            storeKey: 'layout-list-store',
        },
    }
);
