import { Pinia } from 'pinia';

import { usePageDesignerStore } from './page-designer-store';
import { useSectionDesignerStore } from './section-designer-store';
import { useComponentListStore } from './component-list-store';
import { useLayoutListStore } from './layout-list-store';
import { useSectionListStore } from './section-list-store';
import { usePanelLayoutStore } from './panel-layout-store';

export { usePageDesignerStore } from './page-designer-store';
export { useSectionDesignerStore } from './section-designer-store';
export { useComponentListStore } from './component-list-store';
export { useLayoutListStore } from './layout-list-store';
export { useSectionListStore } from './section-list-store';
export { usePanelLayoutStore } from './panel-layout-store';

// 注册所有 store
export default (piniaInstance?: Pinia) => {
    if (!piniaInstance) {
        console.warn('Pinia 实例不存在，stores 无法正常注册');
        return;
    }
    const pageDesignerStore = usePageDesignerStore(piniaInstance);
    const sectionDesignerStore = useSectionDesignerStore(piniaInstance);
    const componentListStore = useComponentListStore(piniaInstance);
    const layoutListStore = useLayoutListStore(piniaInstance);
    const sectionListStore = useSectionListStore(piniaInstance);
    const panelLayoutStore = usePanelLayoutStore(piniaInstance);

    return {
        pageDesignerStore,
        sectionDesignerStore,
        componentListStore,
        layoutListStore,
        sectionListStore,
        panelLayoutStore,
    };
};
