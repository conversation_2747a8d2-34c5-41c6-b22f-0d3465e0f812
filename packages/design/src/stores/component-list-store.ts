import { defineStore } from 'pinia';
import { reactive } from 'vue';
import {
    Component,
    ComponentSearchForm,
    ComponentStyle,
    ComponentStyleSearchForm,
    PaginationParams,
} from '@smartdesk/common/types';
import { componentApi } from '@smartdesk/common/api';
import { STORAGE_DRIVER } from '@chances/portal_common_core';
import { useSiteStore } from '@smartdesk/common/stores';

// 组件列表存储
export const useComponentListStore = defineStore(
    'component-list-store',
    () => {
        // 状态
        const state = reactive<{
            componentMap: Map<string, Component>;
            componentStyleMap: Map<string, ComponentStyle>;
        }>({
            componentMap: new Map(),
            componentStyleMap: new Map(),
        });

        // pinia store
        const currentSiteStore = useSiteStore();

        // 根据查询条件获取组件列表
        const getComponentListByParams = async (params: Partial<ComponentSearchForm>, pageInfo: PaginationParams) => {
            params.siteCode = currentSiteStore.currentSiteCode;
            params.delFlag = 0;
            params.status = 1;
            const componentListRes = await componentApi.getComponents(params, pageInfo);
            if (componentListRes.code === 200) {
                // 合并已有的
                componentListRes.result.forEach((component: Component) => {
                    state.componentMap.set(component.type, component);
                });
            }
            return componentListRes;
        };

        // 根据查询条件获取组件样式列表
        const getComponentStyleListByParams = async (
            params: Partial<ComponentStyleSearchForm>,
            pageInfo: PaginationParams
        ) => {
            params.siteCode = currentSiteStore.currentSiteCode;
            params.delFlag = 0;
            params.status = 1;
            const componentStyleListRes = await componentApi.getComponentStyles(params, pageInfo);
            if (componentStyleListRes.code === 200) {
                // 合并已有的
                componentStyleListRes.result.forEach((componentStyle: ComponentStyle) => {
                    state.componentStyleMap.set(componentStyle.code, componentStyle);
                });
            }
            return componentStyleListRes;
        };

        return {
            state,
            getComponentListByParams,
            getComponentStyleListByParams,
        };
    },
    {
        persist: {
            enabled: false,
            storeName: 'design-portal-store',
            driver: STORAGE_DRIVER.INDEXED_DB,
            storeKey: 'component-list-store',
        },
    }
);
