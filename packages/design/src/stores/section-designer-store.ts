import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { EventManager, STORAGE_DRIVER } from '@chances/portal_common_core';
import { CellLayoutFile, DesignMode, Section, SectionLayoutFile } from '@smartdesk/common/types';
import { useFeedback, useUndoRedo } from '@smartdesk/common/composables';
import { sectionApi } from '@smartdesk/common/api';
import { LayoutListRefreshEvent } from '@smartdesk/design/events';

// 批量类型
export type BatchType = 'merge' | 'props' | 'delete' | 'copy';

// 楼层定义设计器状态存储（集成布局设计器功能）
export const useSectionDesignerStore = defineStore(
    'section-designer-store',
    () => {
        const feedback = useFeedback();

        // 楼层定义
        const section = ref<Section>({} as Section);

        // 默认楼层定义布局
        const defaultSectionLayout = {
            layout: {
                rect: {
                    width: 1280,
                    height: 720,
                    top: 0,
                    left: 0,
                },
                padding: {
                    top: 20,
                    right: 76,
                    bottom: 20,
                    left: 20,
                },
                gap: 20,
                mode: DesignMode.FIXED,
                standardSize: {
                    width: 200,
                    height: 180,
                },
                props: {},
            },
            cells: [] as CellLayoutFile[],
        } as SectionLayoutFile;

        // 楼层定义布局
        const sectionLayout = computed<SectionLayoutFile>(() => {
            return section.value?.layout ?? defaultSectionLayout;
        });

        // 使用 useUndoRedo 包装初始状态
        const { initHistoryStack, undo, redo, clearHistory, saveHistory, canUndo, canRedo } = useUndoRedo(section);

        // 当前选中坑位的索引
        const currentCellIndex = ref<number>(-1);

        // 当前选中的坑位
        const currentCell = computed<CellLayoutFile | null>(() => {
            if (currentCellIndex.value !== -1) {
                return section.value?.layout.cells[currentCellIndex.value];
            }
            return null;
        });

        // 当前元素
        const selectedElement = computed(() => {
            if (currentCellIndex.value === -1) {
                return sectionLayout.value;
            }
            return currentCell.value;
        });

        // 当前元素类型
        const selectedElementTypeDesc = computed(() => {
            if (currentCellIndex.value === -1) {
                return '楼层定义';
            } else {
                return '坑位';
            }
        });

        // 选择坑位，设置当前坑位索引
        const selectCell = (index: number) => {
            // 如果此时处于 batchMode，不做处理
            if (batchMode.value) {
                return;
            }

            // 已选中的坑位，再次选中，则取消选中
            if (currentCellIndex.value === index) {
                currentCellIndex.value = -1;
            } else {
                currentCellIndex.value = index;
            }
        };

        // 生成楼层定义编码
        const generateSectionCode = () => {
            return `section_${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        };

        // 生成坑位编码
        const generateCellCode = () => {
            return `cell-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        };

        // 切换楼层定义
        const switchSection = (sectionForm: Section) => {
            section.value = sectionForm;
        };

        // 切换设计模式
        const switchDesignMode = (mode: DesignMode) => {
            sectionLayout.value.layout.mode = mode;
        };

        // 删除指定索引的坑位
        const removeCellByIndex = (index: number) => {
            if (index < 0 || index >= sectionLayout.value.cells.length) {
                return;
            }

            sectionLayout.value.cells.splice(index, 1);

            // 检查当前选中的索引是不是越界了
            if (currentCellIndex.value > sectionLayout.value.cells.length) {
                currentCellIndex.value = sectionLayout.value.cells.length - 1;
            }
        };

        // 删除指定编码的坑位
        const removeCellByCode = (code: string) => {
            const idx: number = sectionLayout.value.cells.findIndex((cell) => cell.code === code);
            removeCellByIndex(idx);
        };

        // 保存当前楼层定义
        const saveSection = async () => {
            if (!section.value?.code) {
                return;
            }

            const res = await sectionApi.updateSection(section.value.code, section.value);
            const res2 = await sectionApi.updateSectionLayout(section.value.code, section.value.layout);

            if (res.code === 200 && res2.code === 200) {
                feedback.success('更新楼层定义成功');
                clearHistory();
                initHistoryStack(section.value);
            } else {
                feedback.error('更新楼层定义失败');
            }
        };

        // 楼层定义另存为布局
        const saveSectionAsLayout = async () => {
            const confirm = await feedback.confirm('是否将楼层定义另存为布局？');
            if (!confirm) {
                return;
            }

            const sectionLayoutFile: SectionLayoutFile = JSON.parse(JSON.stringify(section.value.layout));
            sectionLayoutFile.cells.forEach((cellLayoutFile: CellLayoutFile) => {
                cellLayoutFile.component = '';
                cellLayoutFile.componentStyleCode = '';
                cellLayoutFile.layout.props = {};
            });

            const res = await sectionApi.saveSectionAsLayout(section.value.code, sectionLayoutFile, {});
            if (res.code === 200) {
                // 发布 layout-list-refresh-event 事件
                const eventManager = new EventManager<LayoutListRefreshEvent>(LayoutListRefreshEvent.type);
                eventManager.publish(new LayoutListRefreshEvent());

                feedback.success('另存为布局成功');
            } else {
                feedback.error('另存为布局失败');
            }
        };

        // 楼层拆分数
        const sectionDivideCount = ref<number>(1);

        // 通用楼层拆分
        const splitSection = (direction: 'row' | 'column') => {
            if (sectionDivideCount.value < 1) {
                return;
            }
            const { rect, padding, mode, standardSize } = sectionLayout.value.layout;
            const contentWidth = rect.width - (padding?.left || 0) - (padding?.right || 0);
            const contentHeight = rect.height - (padding?.top || 0) - (padding?.bottom || 0);
            const startY = padding?.top || 0;
            const startX = padding?.left || 0;
            const gap = sectionLayout.value.layout.gap || 0;
            const isRow = direction === 'row';

            // 清空坑位
            sectionLayout.value.cells = [];

            if (mode === DesignMode.SCROLLABLE) {
                // 滚动模式，支持水平滚动
                if (direction !== 'column' || !standardSize) return;
                for (let i = 0; i < sectionDivideCount.value; i++) {
                    const left = startX + i * (standardSize.width + gap);
                    const cellLayoutFile: CellLayoutFile = {
                        code: generateCellCode(),
                        name: `坑位-${i + 1}`,
                        component: '',
                        componentStyleCode: '',
                        layout: {
                            rect: {
                                left: left,
                                top: startY,
                                width: standardSize.width,
                                height: contentHeight,
                            },
                            props: {},
                        },
                        layoutVersion: 1,
                    };
                    sectionLayout.value.cells.push(cellLayoutFile);
                }
            } else {
                const totalGap = (sectionDivideCount.value - 1) * gap;
                const blockSize = isRow
                    ? (contentHeight - totalGap) / sectionDivideCount.value
                    : (contentWidth - totalGap) / sectionDivideCount.value;
                for (let i = 0; i < sectionDivideCount.value; i++) {
                    const left = isRow ? startX : startX + i * (blockSize + gap);
                    const top = isRow ? startY + i * (blockSize + gap) : startY;
                    const width = isRow ? contentWidth : blockSize;
                    const height = isRow ? blockSize : contentHeight;
                    const cellLayoutFile: CellLayoutFile = {
                        code: generateCellCode(),
                        name: `坑位-${i + 1}`,
                        component: '',
                        componentStyleCode: '',
                        layout: {
                            rect: {
                                left,
                                top,
                                width,
                                height,
                            },
                            props: {},
                        },
                        layoutVersion: 1,
                    };
                    sectionLayout.value.cells.push(cellLayoutFile);
                }
            }
            currentCellIndex.value = -1;

            saveHistory();
        };

        // 楼层拆分为多行
        const splitSectionRow = () => splitSection('row');

        // 楼层拆分为多列
        const splitSectionColumn = () => splitSection('column');

        // 坑位拆分数
        const cellDivideCount = ref<number>(2);

        // 通用坑位拆分
        const splitCell = (direction: 'row' | 'column') => {
            // 如果坑位拆分数小于 2 或当前坑位索引在楼层上，直接返回
            if (cellDivideCount.value < 2 || currentCellIndex.value === -1) {
                return;
            }

            // 找到当前要拆分的坑位
            const cell = sectionLayout.value.cells[currentCellIndex.value];
            if (!cell) {
                return;
            }

            // 获取这个坑位布局中的位置信息
            const { rect } = cell.layout;
            // 坑位间距
            const gap = sectionLayout.value.layout.gap || 0;
            // 是否按行拆分
            const isRow = direction === 'row';
            // 总间距：（拆分数 - 1）* 坑位间距
            const totalGap = (cellDivideCount.value - 1) * gap;
            // 块大小：如果按行拆分，则为拆分后的坑位高度；如果按列拆分，则为拆分后的坑位宽度
            const blockSize = isRow
                ? (rect.height - totalGap) / cellDivideCount.value
                : (rect.width - totalGap) / cellDivideCount.value;

            // 新建所有子坑位
            for (let i = 0; i < cellDivideCount.value; i++) {
                // X 轴起始点：如果按行拆分，则为原坑位的 X 轴起始点；如果按列拆分，则为原坑位的 X 轴起始点 + 前面的坑位数量 * （块大小 + 坑位间距）
                const left = isRow ? rect.left : rect.left + i * (blockSize + gap);
                // Y 轴起始点：如果按行拆分，则为原坑位的 Y 轴起始点 + 前面的坑位数量 * （块大小 + 坑位间距）；如果按列拆分，则为原坑位的 Y 轴起始点
                const top = isRow ? rect.top + i * (blockSize + gap) : rect.top;
                // 坑位宽度：如果按行拆分，则为原坑位的宽度；如果按列拆分，则为块大小
                const width = isRow ? rect.width : blockSize;
                // 坑位高度：如果按行拆分，则为块大小；如果按列拆分，则为原坑位的宽度
                const height = isRow ? blockSize : rect.height;

                // 生成新的坑位布局
                const cellLayoutFile: CellLayoutFile = {
                    code: generateCellCode(),
                    name: `坑位-${sectionLayout.value.cells.length + 1}`,
                    component: cell.component,
                    componentStyleCode: cell.componentStyleCode,
                    layout: {
                        rect: {
                            left,
                            top,
                            width,
                            height,
                        },
                        props: { ...cell.layout.props },
                    },
                    layoutVersion: cell.layoutVersion,
                };
                sectionLayout.value.cells.push(cellLayoutFile);
            }

            // 删除原坑位
            sectionLayout.value.cells.splice(currentCellIndex.value, 1);

            currentCellIndex.value = sectionLayout.value.cells.length - 1;

            saveHistory();
        };

        // 坑位拆分为多行
        const splitCellRow = () => splitCell('row');

        // 坑位拆分为多列
        const splitCellColumn = () => splitCell('column');

        // 批量模式
        const batchMode = ref<boolean>(false);

        // 批量操作类型
        const batchType = ref<BatchType>('merge');

        // 批量操作列表
        const batchList = ref<CellLayoutFile[]>([]);

        // 复制配置
        const copyConfig = ref({
            count: 1,
            position: 'below' as 'above' | 'below' | 'top' | 'bottom',
        });

        // 批量操作的名称字符串
        const batchNameStr = computed(() => {
            return batchList.value.map((item) => item.name).join(', ');
        });

        // 坑位行计算
        const cellRows = computed(() => {
            return identifyCellRows(sectionLayout.value.cells, sectionLayout.value.layout.gap || 0);
        });

        // 识别坑位行
        const identifyCellRows = (cells: CellLayoutFile[], gap: number) => {
            if (cells.length === 0) return [];

            const sortedCells = [...cells].sort((a, b) => a.layout.rect.top - b.layout.rect.top);
            const rows: any[] = [];
            const usedCells = new Set<string>();

            for (const cell of sortedCells) {
                // 如果这个坑位已经被分配到某一行，跳过
                if (usedCells.has(cell.code)) continue;

                const cellTop = cell.layout.rect.top;
                const cellBottom = cellTop + cell.layout.rect.height;

                // 创建新行，以当前坑位为起始
                const currentRowCells: CellLayoutFile[] = [cell];
                usedCells.add(cell.code);

                // 计算当前行的高度范围
                let rowTop = cellTop;
                let rowBottom = cellBottom;

                // 查找所有可能属于这一行的坑位
                for (const otherCell of sortedCells) {
                    if (usedCells.has(otherCell.code)) continue;

                    const otherTop = otherCell.layout.rect.top;
                    const otherBottom = otherTop + otherCell.layout.rect.height;

                    // 判断是否属于同一行：
                    // 1. otherCell 的 top 在当前行范围内，或者
                    // 2. otherCell 的 bottom 在当前行范围内，或者
                    // 3. otherCell 完全包含当前行范围
                    // 同时考虑 gap 的容差
                    const isInSameRow =
                        (otherTop >= rowTop - gap / 2 && otherTop <= rowBottom + gap / 2) ||
                        (otherBottom >= rowTop - gap / 2 && otherBottom <= rowBottom + gap / 2) ||
                        (otherTop <= rowTop && otherBottom >= rowBottom);

                    if (isInSameRow) {
                        currentRowCells.push(otherCell);
                        usedCells.add(otherCell.code);

                        // 更新行的高度范围
                        rowTop = Math.min(rowTop, otherTop);
                        rowBottom = Math.max(rowBottom, otherBottom);
                    }
                }

                // 按 left 位置对行内坑位排序
                currentRowCells.sort((a, b) => a.layout.rect.left - b.layout.rect.left);

                rows.push({
                    id: `row-${rows.length}`,
                    name: `第${rows.length + 1}行`,
                    top: rowTop,
                    bottom: rowBottom,
                    height: rowBottom - rowTop,
                    cells: currentRowCells,
                    orderNo: rows.length + 1,
                });
            }

            // 按行的 top 位置排序
            rows.sort((a, b) => a.top - b.top);

            // 重新设置行号和名称
            rows.forEach((row, index) => {
                row.id = `row-${index}`;
                row.name = `第${index + 1}行`;
                row.orderNo = index + 1;
            });

            return rows;
        };

        // 处理开始批量操作
        const handleStartBatch = (type: BatchType) => {
            batchMode.value = true;
            batchType.value = type;

            if (type === 'copy' && currentCell.value) {
                // 复制模式：选中当前坑位所在的整行
                const currentRow = cellRows.value.find((row) =>
                    row.cells.some((cell: any) => cell.code === currentCell.value?.code)
                );
                batchList.value = currentRow ? [...currentRow.cells] : [currentCell.value];
            } else {
                batchList.value = currentCell.value ? [currentCell.value] : [];
            }
        };

        // 处理选择批量操作
        const handleSelectBatch = (cellLayoutFile: CellLayoutFile) => {
            if (batchList.value.findIndex((item) => item.code === cellLayoutFile.code) !== -1) {
                // 选择的元素已在列表中
                if (cellLayoutFile.code !== batchList.value[0].code) {
                    // 如果是第一个，不允许移除；否则将二次选中的移除
                    batchList.value = batchList.value.filter((item) => item.code !== cellLayoutFile.code);
                }
            } else {
                // 选择的元素不在列表中，往列表中加入
                batchList.value.push(cellLayoutFile);
            }
        };

        // 处理确认批量操作
        const handleConfirmBatch = async (props?: Record<string, any>) => {
            switch (batchType.value) {
                case 'merge':
                    await handleConfirmBatchMerge();
                    break;
                case 'props':
                    await handleConfirmBatchProps(props);
                    break;
                case 'delete':
                    await handleConfirmBatchDelete();
                    break;
                case 'copy':
                    await handleConfirmBatchCopy();
                    break;
                default:
                    break;
            }
        };

        // 处理取消批量操作
        const handleCancelBatch = () => {
            batchMode.value = false;
            batchList.value = [];
        };

        // 处理确认批量合并
        const handleConfirmBatchMerge = async () => {
            if (batchList.value.length < 2) {
                return;
            }

            const res = await feedback.confirm('确定合并这些坑位：' + batchNameStr.value + ' 吗');
            if (!res) {
                return;
            }

            // 计算需要合并的矩形区域
            const left = Math.min(...batchList.value.map((cell) => cell.layout.rect.left));
            const top = Math.min(...batchList.value.map((cell) => cell.layout.rect.top));
            const right = Math.max(...batchList.value.map((cell) => cell.layout.rect.left + cell.layout.rect.width));
            const bottom = Math.max(...batchList.value.map((cell) => cell.layout.rect.top + cell.layout.rect.height));

            // 创建合并后的坑位布局，使用第一个选中的坑位的属性
            const baseCell = batchList.value[0];
            const mergedCell = { ...baseCell };
            mergedCell.layout = { ...baseCell.layout };
            mergedCell.layout.rect = {
                left,
                top,
                width: right - left,
                height: bottom - top,
            };

            // 删除所有选中的坑位
            sectionLayout.value.cells = sectionLayout.value.cells.filter(
                (item) => !batchList.value.map((b) => b.code).includes(item.code)
            );

            // 添加合并后的坑位
            mergedCell.code = generateCellCode();
            // 选中新合并的坑位
            currentCellIndex.value = sectionLayout.value.cells.push(mergedCell) - 1;

            // 合并成功
            feedback.success('坑位合并成功');
            saveHistory();

            // 退出批量模式
            handleCancelBatch();
        };

        // 处理确认批量删除
        const handleConfirmBatchDelete = async () => {
            if (batchList.value.length < 0) {
                return;
            }

            const res = await feedback.confirm('确定删除这些坑位：' + batchNameStr.value + ' 吗');
            if (!res) {
                return;
            }

            // batchList 转为 code 数组
            const codeArr: string[] = batchList.value.map((item) => item.code);
            codeArr.forEach((code) => {
                removeCellByCode(code);
            });

            if (sectionLayout.value.cells.length === 0) {
                // 坑位都被删除，指向楼层
                currentCellIndex.value = -1;
            } else {
                // 还有坑位，默认指向第一个
                currentCellIndex.value = 0;
            }

            // 删除成功
            feedback.success('坑位删除成功');
            saveHistory();

            // 退出批量模式
            handleCancelBatch();
        };

        // 处理确认批量设置坑位样式
        const handleConfirmBatchProps = async (styleProps?: Record<string, any>) => {
            if (!styleProps) {
                return;
            }

            // batchList 转为 index 数组
            const indexArr = batchList.value.map((item) =>
                sectionLayout.value.cells.findIndex((cell) => item.code === cell.code)
            );
            if (!indexArr.length) {
                return;
            }

            // 已选择的坑位上已经更新了组件样式
            const componentStyleCode = selectedElement.value?.componentStyleCode ?? '';
            const component = selectedElement.value?.component ?? '';

            indexArr.forEach((index) => {
                if (index >= 0 && index < sectionLayout.value.cells.length) {
                    // 确保 layout.props 存在
                    if (!sectionLayout.value.cells[index].layout.props) {
                        sectionLayout.value.cells[index].layout.props = {};
                    }

                    // 更新组件样式
                    sectionLayout.value.cells[index].componentStyleCode = componentStyleCode;
                    sectionLayout.value.cells[index].component = component;

                    // 更新属性
                    sectionLayout.value.cells[index].layout.props = {
                        ...styleProps,
                    };
                }
            });

            feedback.success('批量更新坑位样式成功');
            saveHistory();

            handleCancelBatch();
        };

        // 处理确认批量复制
        const handleConfirmBatchCopy = async () => {
            if (batchList.value.length === 0) return;

            // 找到所有被选中的行
            const selectedRows = cellRows.value.filter((row) =>
                row.cells.some((cell: any) => batchList.value.some((batchCell) => batchCell.code === cell.code))
            );

            if (selectedRows.length === 0) return;

            const rowNames = selectedRows.map((row) => row.name).join('、');
            const res = await feedback.confirm(
                `确定复制 ${rowNames}（${copyConfig.value.count}份）到${getPositionDesc(copyConfig.value.position)}吗？`
            );

            if (!res) return;

            copyMultipleRows(selectedRows, copyConfig.value);

            feedback.success('复制成功');
            saveHistory();

            handleCancelBatch();
        };

        // 复制多行实现
        const copyMultipleRows = (sourceRows: any[], config: any) => {
            const newRowsData: any[] = [];

            for (let copyIndex = 0; copyIndex < config.count; copyIndex++) {
                for (const sourceRow of sourceRows) {
                    const newRowCells: CellLayoutFile[] = [];

                    for (const cell of sourceRow.cells) {
                        const newCell: CellLayoutFile = {
                            ...cell,
                            code: generateCellCode(),
                            name: `${cell.name}-副本${copyIndex + 1}`,
                            layout: {
                                ...cell.layout,
                                rect: { ...cell.layout.rect },
                            },
                        };
                        newRowCells.push(newCell);
                    }

                    // 保持原始行结构
                    newRowsData.push({
                        ...sourceRow,
                        id: `new-row-${newRowsData.length}`,
                        name: `${sourceRow.name}-副本${copyIndex + 1}`,
                        cells: newRowCells,
                        orderNo: newRowsData.length + 1,
                    });
                }
            }

            // 根据位置插入新行
            insertMultipleRowsAndRecalculate(sourceRows, newRowsData, config.position);
        };

        // 插入多行并重新计算位置
        const insertMultipleRowsAndRecalculate = (sourceRows: any[], newRows: any[], position: string) => {
            const { padding, gap } = sectionLayout.value.layout;
            const gapValue = gap || 0;

            // 获取当前所有行
            const currentRows = identifyCellRows(sectionLayout.value.cells, gapValue);

            // 找到第一个源行的索引作为插入位置参考
            const firstSourceRowIndex = currentRows.findIndex((row) =>
                row.cells.some((cell: any) =>
                    sourceRows[0].cells.some((sourceCell: any) => sourceCell.code === cell.code)
                )
            );

            // 根据复制位置插入新行
            let reorderedRows: any[] = [];

            switch (position) {
                case 'above':
                    // 在当前行上方插入新行
                    reorderedRows = [
                        ...currentRows.slice(0, firstSourceRowIndex),
                        ...newRows,
                        ...currentRows.slice(firstSourceRowIndex),
                    ];
                    break;
                case 'below':
                    // 在当前行下方插入新行
                    const lastSourceRowIndex = currentRows.findIndex((row) =>
                        row.cells.some((cell: any) =>
                            sourceRows[sourceRows.length - 1].cells.some(
                                (sourceCell: any) => sourceCell.code === cell.code
                            )
                        )
                    );
                    reorderedRows = [
                        ...currentRows.slice(0, lastSourceRowIndex + 1),
                        ...newRows,
                        ...currentRows.slice(lastSourceRowIndex + 1),
                    ];
                    break;
                case 'top':
                    // 在顶部插入新行
                    reorderedRows = [...newRows, ...currentRows];
                    break;
                case 'bottom':
                    // 在底部插入新行
                    reorderedRows = [...currentRows, ...newRows];
                    break;
            }

            // 重新计算所有行的位置
            recalculateAllRowPositions(reorderedRows);
        };

        // 重新计算所有行的位置
        const recalculateAllRowPositions = (rows: any[]) => {
            const { padding, gap } = sectionLayout.value.layout;
            const startY = padding?.top || 0;
            const gapValue = gap || 0;
            let currentRowTop = startY;

            const allCells: CellLayoutFile[] = [];

            for (let i = 0; i < rows.length; i++) {
                const row = rows[i];

                // 计算当前行的原始top范围
                const originalRowMinTop = Math.min(...row.cells.map((cell: any) => cell.layout.rect.top));
                const originalRowMaxBottom = Math.max(
                    ...row.cells.map((cell: any) => cell.layout.rect.top + cell.layout.rect.height)
                );
                const rowHeight = originalRowMaxBottom - originalRowMinTop;

                // 计算偏移量：将行的最小top调整到当前行位置
                const topOffset = currentRowTop - originalRowMinTop;

                // 更新行内所有坑位的top值，保持行内相对位置关系
                for (const cell of row.cells) {
                    const updatedCell = { ...cell };
                    updatedCell.layout.rect.top = cell.layout.rect.top + topOffset;
                    allCells.push(updatedCell);
                }

                // 计算下一行的起始位置：当前行顶部 + 行高 + 间距
                currentRowTop += rowHeight + gapValue;
            }

            // 计算新的楼层高度
            const bottomPadding = padding?.bottom || 0;
            const newSectionHeight = currentRowTop - gapValue + bottomPadding;

            // 更新楼层高度
            sectionLayout.value.layout.rect.height = newSectionHeight;

            sectionLayout.value.cells = allCells;
        };

        // 获取位置描述
        const getPositionDesc = (position: string) => {
            const positionMap: Record<string, string> = {
                above: '当前上方',
                below: '当前下方',
                top: '顶部',
                bottom: '底部',
            };
            return positionMap[position] || '未知位置';
        };

        // 重新排列行顺序
        const reorderRows = (newRows: any[]) => {
            // 更新行的orderNo
            newRows.forEach((row, index) => {
                row.orderNo = index + 1;
                row.name = `第${index + 1}行`;
            });

            // 重新计算所有行的位置
            recalculateAllRowPositions(newRows);
        };

        // 重新计算行位置
        const recalculateRowPositions = () => {
            const rows = cellRows.value;
            const { padding, gap } = sectionLayout.value.layout;
            const startY = padding?.top || 0;
            let currentRowTop = startY;

            for (const row of rows) {
                // 计算当前行的原始top范围
                const originalRowMinTop = Math.min(...row.cells.map((cell: any) => cell.layout.rect.top));
                const originalRowMaxBottom = Math.max(
                    ...row.cells.map((cell: any) => cell.layout.rect.top + cell.layout.rect.height)
                );
                const rowHeight = originalRowMaxBottom - originalRowMinTop;

                // 计算偏移量：将行的最小top调整到当前行位置
                const topOffset = currentRowTop - originalRowMinTop;

                // 更新行内所有坑位的top值，保持行内相对位置关系
                for (const cell of row.cells) {
                    const cellIndex = sectionLayout.value.cells.findIndex((c) => c.code === cell.code);
                    if (cellIndex !== -1) {
                        sectionLayout.value.cells[cellIndex].layout.rect.top = cell.layout.rect.top + topOffset;
                    }
                }

                // 计算下一行的起始位置：当前行顶部 + 行高 + 间距
                currentRowTop += rowHeight + (gap || 0);
            }

            saveHistory();
        };

        // 左侧面板激活的 Tab
        const leftPanelActiveTab = ref<string>('layoutList');

        // 右侧面板激活的 Tab
        const rightPanelActiveTab = ref<string>();

        // 页面设计器 tab 切换
        const switchTab = (position: 'left' | 'right', tab: string) => {
            if (position === 'left') {
                leftPanelActiveTab.value = tab;
            } else if (position === 'right') {
                rightPanelActiveTab.value = tab;
            }
        };

        // 获取楼层定义
        const getSection = async (code: string) => {
            const res = await sectionApi.getSectionByCode(code);
            if (res.code === 200) {
                switchSection(res.result);
            }
        };

        // 初始化
        const init = async () => {
            currentCellIndex.value = -1;

            batchMode.value = false;
            batchList.value = [];

            leftPanelActiveTab.value = 'layoutList';
            rightPanelActiveTab.value = 'sectionLayout';
        };

        // 刷新楼层定义
        const refreshSection = async () => {
            await getSection(section.value.code);
            await init();
            initHistoryStack(section.value);
        };

        return {
            // 楼层定义相关
            section,
            sectionLayout,
            saveSection,
            saveSectionAsLayout,
            refreshSection,

            // 当前坑位
            currentCellIndex,
            currentCell,
            selectedElement,
            selectedElementTypeDesc,

            // 坑位操作
            selectCell,
            removeCellByIndex,

            // 拆分操作
            sectionDivideCount,
            splitSectionRow,
            splitSectionColumn,
            cellDivideCount,
            splitCellRow,
            splitCellColumn,

            // 坑位样式相关
            switchDesignMode,
            generateCellCode,
            generateSectionCode,

            // 批量操作相关：合并、修改组件样式、删除、复制
            batchMode,
            batchType,
            batchList,
            batchNameStr,
            handleStartBatch,
            handleSelectBatch,
            handleConfirmBatch,
            handleCancelBatch,

            // 坑位行相关
            cellRows,
            copyConfig,
            reorderRows,
            recalculateRowPositions,

            // tab 切换
            leftPanelActiveTab,
            rightPanelActiveTab,
            switchTab,

            // 历史记录
            undo,
            redo,
            canUndo,
            canRedo,
            saveHistory,
        };
    },
    {
        persist: {
            enabled: true,
            storeName: 'design-portal-store',
            driver: STORAGE_DRIVER.LOCAL_STORAGE,
            storeKey: 'section-designer-store',
        },
    }
);
